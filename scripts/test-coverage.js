#!/usr/bin/env node

/**
 * Comprehensive Test Coverage Analysis Script
 * Runs tests and generates detailed coverage reports
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function header(message) {
  log(`\n${message}`, 'bold');
  log('='.repeat(message.length), 'bold');
}

// Test categories and their expected files
const testCategories = {
  services: [
    'databaseService.test.ts',
    'usageTrackingService.test.ts',
    'contactsService.test.ts',
    'messagingService.test.ts',
    'giftService.test.ts',
    'reminderService.test.ts',
    'stripeService.test.ts',
    'settingsService.test.ts',
    'userManagementService.test.ts',
    'migrationService.test.ts',
    'dataMigrationService.test.ts',
    'persistenceService.test.ts'
  ],
  hooks: [
    'useFeatureAccess.test.ts',
    'useContacts.test.ts',
    'useMessaging.test.ts',
    'useGifts.test.ts',
    'useReminders.test.ts',
    'useStripe.test.ts',
    'useSettings.test.ts',
    'useFormValidation.test.ts',
    'useAsyncError.test.ts'
  ],
  components: [
    'Dashboard.test.tsx',
    'ContactList.test.tsx',
    'ContactForm.test.tsx',
    'FeatureGate.test.tsx',
    'LoadingSpinner.test.tsx',
    'ErrorBoundary.test.tsx',
    'FormField.test.tsx',
    'LoginForm.test.tsx',
    'SignupForm.test.tsx',
    'MessagingDashboard.test.tsx',
    'MessageTemplateManager.test.tsx',
    'BirthdayCalendar.test.tsx',
    'AnniversaryCalendar.test.tsx',
    'AnalyticsDashboard.test.tsx',
    'GiftCenter.test.tsx',
    'RemindersPage.test.tsx',
    'SubscriptionManagement.test.tsx',
    'AdminDashboard.test.tsx'
  ],
  context: [
    'AuthContext.test.tsx',
    'ContactsContext.test.tsx',
    'SubscriptionContext.test.tsx',
    'GiftContext.test.tsx',
    'ErrorContext.test.tsx',
    'LoadingContext.test.tsx'
  ]
};

function checkTestFiles() {
  header('📋 Test File Coverage Analysis');
  
  let totalExpected = 0;
  let totalFound = 0;
  const missingTests = [];

  Object.entries(testCategories).forEach(([category, expectedFiles]) => {
    info(`\nChecking ${category} tests:`);
    
    const testDir = path.join('src', category, '__tests__');
    let categoryFound = 0;
    
    expectedFiles.forEach(testFile => {
      totalExpected++;
      const testPath = path.join(testDir, testFile);
      
      if (fs.existsSync(testPath)) {
        success(`  ${testFile}`);
        totalFound++;
        categoryFound++;
      } else {
        error(`  ${testFile} - MISSING`);
        missingTests.push(`${category}/${testFile}`);
      }
    });
    
    const categoryPercentage = Math.round((categoryFound / expectedFiles.length) * 100);
    log(`  ${category}: ${categoryFound}/${expectedFiles.length} (${categoryPercentage}%)`, 
        categoryPercentage >= 80 ? 'green' : categoryPercentage >= 60 ? 'yellow' : 'red');
  });

  const overallPercentage = Math.round((totalFound / totalExpected) * 100);
  
  header('\n📊 Test File Summary');
  log(`Total test files: ${totalFound}/${totalExpected} (${overallPercentage}%)`, 
      overallPercentage >= 80 ? 'green' : overallPercentage >= 60 ? 'yellow' : 'red');
  
  if (missingTests.length > 0) {
    warning('\n📝 Missing test files:');
    missingTests.forEach(test => log(`  - ${test}`, 'yellow'));
  }

  return { totalFound, totalExpected, overallPercentage, missingTests };
}

function runTests() {
  header('🧪 Running Test Suite');
  
  try {
    info('Running Jest with coverage...');
    
    const output = execSync('npm run test:coverage', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    success('Tests completed successfully!');
    
    // Parse coverage output
    const lines = output.split('\n');
    const coverageLines = lines.filter(line => 
      line.includes('All files') || 
      line.includes('Statements') || 
      line.includes('Branches') || 
      line.includes('Functions') || 
      line.includes('Lines')
    );
    
    if (coverageLines.length > 0) {
      header('\n📈 Coverage Summary');
      coverageLines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed) {
          // Extract percentage from coverage line
          const percentMatch = trimmed.match(/(\d+\.?\d*)%/);
          if (percentMatch) {
            const percent = parseFloat(percentMatch[1]);
            const color = percent >= 90 ? 'green' : percent >= 70 ? 'yellow' : 'red';
            log(`  ${trimmed}`, color);
          } else {
            log(`  ${trimmed}`, 'cyan');
          }
        }
      });
    }
    
    return true;
  } catch (err) {
    error('Tests failed!');
    log(err.stdout || err.message, 'red');
    return false;
  }
}

function analyzeCoverageReport() {
  header('📊 Detailed Coverage Analysis');
  
  const coverageFile = path.join('coverage', 'coverage-summary.json');
  
  if (!fs.existsSync(coverageFile)) {
    warning('Coverage summary file not found. Run tests with coverage first.');
    return null;
  }
  
  try {
    const coverageData = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    const total = coverageData.total;
    
    info('Overall Coverage Metrics:');
    
    const metrics = ['statements', 'branches', 'functions', 'lines'];
    metrics.forEach(metric => {
      const data = total[metric];
      const percent = data.pct;
      const color = percent >= 90 ? 'green' : percent >= 70 ? 'yellow' : 'red';
      log(`  ${metric.charAt(0).toUpperCase() + metric.slice(1)}: ${data.covered}/${data.total} (${percent}%)`, color);
    });
    
    // Find files with low coverage
    const lowCoverageFiles = [];
    Object.entries(coverageData).forEach(([filePath, data]) => {
      if (filePath !== 'total' && data.lines && data.lines.pct < 80) {
        lowCoverageFiles.push({
          file: filePath,
          coverage: data.lines.pct
        });
      }
    });
    
    if (lowCoverageFiles.length > 0) {
      warning('\n📉 Files with low coverage (<80%):');
      lowCoverageFiles
        .sort((a, b) => a.coverage - b.coverage)
        .forEach(({ file, coverage }) => {
          log(`  ${file}: ${coverage}%`, 'yellow');
        });
    }
    
    return total;
  } catch (err) {
    error('Failed to parse coverage report');
    log(err.message, 'red');
    return null;
  }
}

function generateRecommendations(testFileStats, coverageData) {
  header('💡 Recommendations');
  
  const recommendations = [];
  
  // Test file recommendations
  if (testFileStats.overallPercentage < 80) {
    recommendations.push('📝 Create missing test files to improve test coverage');
  }
  
  if (testFileStats.missingTests.length > 0) {
    const priorityMissing = testFileStats.missingTests.filter(test => 
      test.includes('services/') || test.includes('hooks/')
    );
    if (priorityMissing.length > 0) {
      recommendations.push('🔧 Prioritize creating tests for services and hooks (core business logic)');
    }
  }
  
  // Coverage recommendations
  if (coverageData) {
    if (coverageData.statements.pct < 90) {
      recommendations.push('📊 Add more test cases to improve statement coverage');
    }
    
    if (coverageData.branches.pct < 90) {
      recommendations.push('🌿 Add tests for edge cases and error conditions to improve branch coverage');
    }
    
    if (coverageData.functions.pct < 90) {
      recommendations.push('🔧 Ensure all functions are tested, including utility and helper functions');
    }
  }
  
  // General recommendations
  recommendations.push('🧪 Run tests regularly during development');
  recommendations.push('📈 Aim for 90%+ coverage in all metrics');
  recommendations.push('🔍 Review uncovered lines and add targeted tests');
  recommendations.push('🚀 Consider adding integration tests for complex workflows');
  
  if (recommendations.length > 0) {
    recommendations.forEach(rec => log(`  ${rec}`, 'cyan'));
  } else {
    success('🎉 Excellent test coverage! Keep up the good work!');
  }
}

function openCoverageReport() {
  const htmlReport = path.join('coverage', 'lcov-report', 'index.html');
  
  if (fs.existsSync(htmlReport)) {
    info('\n🌐 Opening detailed coverage report in browser...');
    
    try {
      const open = process.platform === 'darwin' ? 'open' : 
                   process.platform === 'win32' ? 'start' : 'xdg-open';
      execSync(`${open} ${htmlReport}`);
      success('Coverage report opened in browser');
    } catch (err) {
      warning('Could not open browser automatically');
      info(`Manual open: file://${path.resolve(htmlReport)}`);
    }
  } else {
    warning('HTML coverage report not found');
  }
}

function main() {
  log('\n🎯 WeWish SaaS - Comprehensive Test Coverage Analysis\n', 'bold');
  
  // Check test file coverage
  const testFileStats = checkTestFiles();
  
  // Run tests with coverage
  const testsSucceeded = runTests();
  
  if (!testsSucceeded) {
    error('\n❌ Tests failed. Fix failing tests before analyzing coverage.');
    process.exit(1);
  }
  
  // Analyze coverage report
  const coverageData = analyzeCoverageReport();
  
  // Generate recommendations
  generateRecommendations(testFileStats, coverageData);
  
  // Open detailed report
  if (process.argv.includes('--open')) {
    openCoverageReport();
  }
  
  // Final summary
  header('\n🎯 Summary');
  
  const overallScore = coverageData ? 
    Math.round((coverageData.statements.pct + coverageData.branches.pct + 
                coverageData.functions.pct + coverageData.lines.pct) / 4) : 0;
  
  log(`Test Files: ${testFileStats.overallPercentage}%`, 
      testFileStats.overallPercentage >= 80 ? 'green' : 'yellow');
  
  if (coverageData) {
    log(`Code Coverage: ${overallScore}%`, 
        overallScore >= 90 ? 'green' : overallScore >= 70 ? 'yellow' : 'red');
  }
  
  if (testFileStats.overallPercentage >= 80 && overallScore >= 90) {
    success('\n🎉 Excellent test coverage! Ready for production deployment.');
  } else if (testFileStats.overallPercentage >= 60 && overallScore >= 70) {
    warning('\n⚠️  Good test coverage, but room for improvement.');
  } else {
    error('\n❌ Test coverage needs significant improvement before deployment.');
  }
  
  log('\nRun with --open flag to view detailed HTML coverage report', 'cyan');
}

// Run the analysis
if (require.main === module) {
  main();
}

module.exports = {
  checkTestFiles,
  runTests,
  analyzeCoverageReport,
  generateRecommendations
};
