# Firebase config (replace with your own Firebase project values)
# Note: Vite uses VITE_ prefix instead of REACT_APP_
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=TrackCelebrations

# Stripe Price IDs (replace with your actual price IDs from Stripe Dashboard)
STRIPE_PRICE_STANDARD_MONTHLY=price_1234567890
STRIPE_PRICE_STANDARD_YEARLY=price_1234567891
STRIPE_PRICE_ELITE_MONTHLY=price_1234567892
STRIPE_PRICE_ELITE_YEARLY=price_1234567893
STRIPE_PRICE_PREMIUM_MONTHLY=price_1234567894
STRIPE_PRICE_PREMIUM_YEARLY=price_1234567895

# GitHub Actions Secrets (set in repo settings, NOT committed)
CODECOV_TOKEN=your_codecov_token
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
VPS_HOST=your.vps.ip.or.domain
VPS_USER=your_ssh_username
VPS_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...
-----END OPENSSH PRIVATE KEY-----
