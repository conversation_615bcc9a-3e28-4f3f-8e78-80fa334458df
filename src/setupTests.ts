import '@testing-library/jest-dom';

// Mock Firebase
jest.mock('./firebase', () => ({
    auth: {
        currentUser: null,
        signInWithPopup: jest.fn(),
        signOut: jest.fn(),
    },
    googleProvider: {},
}));

// Mock crypto.subtle for password hashing tests
Object.defineProperty(global, 'crypto', {
    value: {
        subtle: {
            digest: jest.fn().mockImplementation(() => 
                Promise.resolve(new ArrayBuffer(32))
            ),
        },
    },
});

// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
});

// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    error: jest.fn(),
    warn: jest.fn(),
    log: jest.fn(),
};

// Mock IntersectionObserver with required properties and methods
class MockIntersectionObserver {
    readonly root: Element | Document | null = null;
    readonly rootMargin: string = '';
    readonly thresholds: ReadonlyArray<number> = [];
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
    takeRecords() { return []; }
};
global.IntersectionObserver = MockIntersectionObserver;

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
};
