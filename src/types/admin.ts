// Admin-specific types - separate file to avoid caching issues
export type UserRole = 'system_admin' | 'church_admin' | 'user';

export interface AdminUser {
    email: string;
    role?: UserRole;
    name?: string;
    organizationId?: string;
    createdAt?: string;
    updatedAt?: string;
    lastLogin?: string;
    isActive?: boolean;
}

export interface OrganizationSettings {
    allowMemberSelfRegistration: boolean;
    requireAdminApproval: boolean;
    defaultContactCategory: 'Family' | 'Friends' | 'Colleagues' | 'Clients' | 'Members';
    enableAnniversaries: boolean;
    enableLandmarkBirthdays: boolean;
    timezone: string;
    emailNotifications: boolean;
}

export interface Organization {
    id: string;
    name: string;
    type: 'church' | 'business' | 'family' | 'other';
    description?: string;
    adminEmail: string;
    settings: OrganizationSettings;
    createdAt: string;
    updatedAt: string;
    isActive: boolean;
}
