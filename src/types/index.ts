
export interface User {
    email: string;
    role?: UserRole;
    name?: string;
    organizationId?: string; // For church admins to manage specific organizations
    createdAt?: string;
    updatedAt?: string;
    lastLogin?: string;
    isActive?: boolean;
}

export type UserRole = 'system_admin' | 'church_admin' | 'user';

export interface Contact {
    id: string;
    name: string;
    birthday: string; // ISO date string yyyy-mm-dd
    category: "Family" | "Friends" | "Colleagues" | "Clients" | "Members";
    confirmed?: boolean; // Flag to indicate if birthday has been validated
    // Anniversary fields
    anniversaryDate?: string; // ISO date string yyyy-mm-dd
    anniversaryType?: "Wedding" | "Dating" | "Business" | "Engagement" | "Other";
    partnerName?: string; // For wedding/dating anniversaries
    anniversaryNotes?: string; // Additional notes about the anniversary
}

export interface OrganizationSettings {
    allowMemberSelfRegistration: boolean;
    requireAdminApproval: boolean;
    defaultContactCategory: Contact['category'];
    enableAnniversaries: boolean;
    enableLandmarkBirthdays: boolean;
    timezone: string;
    emailNotifications: boolean;
}

export interface Organization {
    id: string;
    name: string;
    type: 'church' | 'business' | 'family' | 'other';
    description?: string;
    adminEmail: string;
    settings: OrganizationSettings;
    createdAt: string;
    updatedAt: string;
    isActive: boolean;
}

