// User management types - fresh file to avoid any caching issues
export type UserRole = 'system_admin' | 'church_admin' | 'user';

export interface UserWithRole {
    email: string;
    role?: UserRole;
    name?: string;
    organizationId?: string;
    createdAt?: string;
    updatedAt?: string;
    lastLogin?: string;
    isActive?: boolean;
}

export interface OrgSettings {
    allowMemberSelfRegistration: boolean;
    requireAdminApproval: boolean;
    defaultContactCategory: 'Family' | 'Friends' | 'Colleagues' | 'Clients' | 'Members';
    enableAnniversaries: boolean;
    enableLandmarkBirthdays: boolean;
    timezone: string;
    emailNotifications: boolean;
}

export interface Org {
    id: string;
    name: string;
    type: 'church' | 'business' | 'family' | 'other';
    description?: string;
    adminEmail: string;
    settings: OrgSettings;
    createdAt: string;
    updatedAt: string;
    isActive: boolean;
}
