export interface MessageTemplate {
  id: string;
  name: string;
  type: 'birthday' | 'anniversary' | 'landmark' | 'reminder' | 'weekly_admin';
  subject: string;
  message: string;
  variables: string[]; // Available template variables like {name}, {age}, {milestone}
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LandmarkBirthday {
  age: number;
  name: string;
  description: string;
  isSpecial: boolean;
}

export interface ScheduledMessage {
  id: string;
  contactId: string;
  templateId: string;
  scheduledFor: string; // ISO date string
  type: 'birthday' | 'landmark' | 'reminder';
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  variables: Record<string, string>;
  sentAt?: string;
  errorMessage?: string;
  retryCount: number;
}

export interface WeeklyAdminNotification {
  id: string;
  weekStartDate: string;
  weekEndDate: string;
  upcomingBirthdays: {
    contactId: string;
    name: string;
    birthday: string;
    age: number;
    isLandmark: boolean;
    landmarkType?: string;
  }[];
  sentAt: string;
  status: 'sent' | 'failed';
}

export interface NotificationSettings {
  id: string;
  userId: string;
  weeklyAdminNotifications: {
    enabled: boolean;
    dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
    time: string; // HH:MM format
    email: string;
  };
  birthdayMessages: {
    enabled: boolean;
    defaultTime: string; // HH:MM format
    timezone: string;
  };
  landmarkBirthdays: {
    enabled: boolean;
    customMessage: boolean;
  };
}

export interface MessageHistory {
  id: string;
  contactId: string;
  templateId: string;
  type: 'birthday' | 'landmark' | 'reminder' | 'weekly_admin';
  subject: string;
  content: string;
  sentAt: string;
  status: 'sent' | 'failed';
  recipient: string;
  errorMessage?: string;
}

export interface MessagingStats {
  totalMessagesSent: number;
  birthdayMessagesSent: number;
  landmarkMessagesSent: number;
  weeklyNotificationsSent: number;
  failedMessages: number;
  successRate: number;
  lastWeekStats: {
    sent: number;
    failed: number;
  };
}


