import React, { useState } from "react";
import { useRemindersQuery, useCreateReminder, useDeleteReminder, useToggleReminder } from "../hooks/useReminders";
import type { Reminder } from "../services/reminderService";

const RemindersPage = () => {
    // React Query hooks
    const { data: reminders = [], isLoading, error } = useRemindersQuery();
    const createReminderMutation = useCreateReminder();
    const deleteReminderMutation = useDeleteReminder();
    const toggleReminderMutation = useToggleReminder();

    // Local UI state
    const [showAddModal, setShowAddModal] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        person: '',
        date: '',
        reminderDate: '',
        type: 'birthday' as 'birthday' | 'anniversary' | 'custom',
        notifications: ['Email'] as string[],
        notes: ''
    });

    // Calculated values from React Query data
    const activeReminders = reminders.filter(r => r.isActive);
    const thisWeekReminders = reminders.filter(r => {
        const reminderDate = new Date(r.reminderDate);
        const today = new Date();
        const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
        return reminderDate >= today && reminderDate <= weekFromNow;
    }).length;
    const totalReminders = reminders.length;

    const handleToggleReminder = async (id: string) => {
        try {
            await toggleReminderMutation.mutateAsync(id);
        } catch (error) {
            console.error('Failed to toggle reminder:', error);
        }
    };

    const handleDeleteReminder = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this reminder?')) {
            try {
                await deleteReminderMutation.mutateAsync(id);
            } catch (error) {
                console.error('Failed to delete reminder:', error);
            }
        }
    };

    const handleCreateReminder = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.title || !formData.person || !formData.date || !formData.reminderDate) {
            alert('Please fill in all required fields');
            return;
        }

        try {
            await createReminderMutation.mutateAsync({
                title: formData.title,
                person: formData.person,
                date: formData.date,
                reminderDate: formData.reminderDate,
                type: formData.type,
                notifications: formData.notifications,
                notes: formData.notes,
                isActive: true,
                recurring: formData.type !== 'custom'
            });

            // Reset form and close modal
            setFormData({
                title: '',
                person: '',
                date: '',
                reminderDate: '',
                type: 'birthday',
                notifications: ['Email'],
                notes: ''
            });
            setShowAddModal(false);
        } catch (error) {
            console.error('Failed to create reminder:', error);
            alert('Failed to create reminder. Please try again.');
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">🔔</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-3">
                        Smart Reminders
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
                        Never miss another celebration with intelligent reminder management
                    </p>
                    <button
                        onClick={() => setShowAddModal(true)}
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-3 rounded-2xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold flex items-center mx-auto"
                    >
                        <span className="mr-2 text-lg">+</span>
                        Add New Reminder
                    </button>
                </div>

                {/* Loading State */}
                {isLoading && (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading reminders...</p>
                        </div>
                    </div>
                )}

                {/* Error State */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center mb-8">
                        <div className="text-red-600 mb-2">
                            <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Reminders</h3>
                        <p className="text-red-600 mb-4">There was an error loading your reminders. Please try again.</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                )}

                {/* Content - only show when not loading and no errors */}
                {!isLoading && !error && (
                    <>
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            {/* Active Reminders */}
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                <div className="flex items-center">
                                    <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                        <span className="text-2xl">🔔</span>
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Active Reminders</p>
                                        <p className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">{activeReminders.length}</p>
                                        <p className="text-sm text-gray-500">Currently monitoring</p>
                                    </div>
                                </div>
                            </div>

                            {/* This Week */}
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                <div className="flex items-center">
                                    <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                        <span className="text-2xl">📅</span>
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">This Week</p>
                                        <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">{thisWeekReminders}</p>
                                        <p className="text-sm text-gray-500">Reminders due</p>
                                    </div>
                                </div>
                            </div>

                            {/* Total Created */}
                            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                <div className="flex items-center">
                                    <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                        <span className="text-2xl">📊</span>
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Total Created</p>
                                        <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">{totalReminders}</p>
                                        <p className="text-sm text-gray-500">All time reminders</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Active Reminders Section */}
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
                            <div className="p-8 border-b border-gray-100">
                                <div className="flex items-center">
                                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                        <span className="text-2xl">🔔</span>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">Active Reminders</h2>
                                        <p className="text-gray-600">Reminders that are currently active and monitoring</p>
                                    </div>
                                </div>
                            </div>

                            <div className="p-8">
                                <div className="space-y-6">
                                    {activeReminders.map((reminder) => (
                                        <div key={reminder.id} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    {/* Reminder Header */}
                                                    <div className="flex items-center mb-4">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                                            <span className="text-xl">{reminder.type === 'birthday' ? '🎂' : '💕'}</span>
                                                        </div>
                                                        <div>
                                                            <h3 className="text-xl font-bold text-gray-900">
                                                                {reminder.title}
                                                            </h3>
                                                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${reminder.type === 'birthday'
                                                                ? 'bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700'
                                                                : 'bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700'
                                                                }`}>
                                                                {reminder.type === 'birthday' ? '🎂 Birthday' : '💕 Anniversary'}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {/* Person and Date */}
                                                    <div className="flex items-center text-gray-700 mb-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-3">
                                                        <span className="font-semibold text-lg">{reminder.person}</span>
                                                        <span className="mx-3 text-gray-400">•</span>
                                                        <span className="font-medium">{reminder.date}</span>
                                                    </div>

                                                    {/* Reminder Date */}
                                                    <div className="flex items-center text-sm font-medium text-gray-600 mb-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-3">
                                                        <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                                                            <span className="text-sm">⏰</span>
                                                        </div>
                                                        <span>Remind on {reminder.reminderDate}</span>
                                                    </div>

                                                    {/* Notification Methods */}
                                                    <div className="flex items-center space-x-4">
                                                        {reminder.notifications.map((method) => (
                                                            <div key={method} className="flex items-center text-sm text-gray-600">
                                                                {method === "Email" && (
                                                                    <>
                                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                                                        </svg>
                                                                        Email
                                                                    </>
                                                                )}
                                                                {method === "Push" && (
                                                                    <>
                                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                                                                        </svg>
                                                                        Push
                                                                    </>
                                                                )}
                                                                {method === "Sms" && (
                                                                    <>
                                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                                                        </svg>
                                                                        Sms
                                                                    </>
                                                                )}
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>

                                                {/* Toggle Switch */}
                                                <div className="ml-6">
                                                    <label className="relative inline-flex items-center cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            className="sr-only peer"
                                                            checked={reminder.isActive}
                                                            onChange={() => handleToggleReminder(reminder.id)}
                                                        />
                                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Empty State */}
                                {activeReminders.length === 0 && (
                                    <div className="text-center py-12">
                                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12a1 1 0 011-1h4a1 1 0 011 1v12z" />
                                        </svg>
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">No active reminders</h3>
                                        <p className="mt-1 text-sm text-gray-500">Get started by creating your first reminder.</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Add Reminder Modal */}
                        {showAddModal && (
                            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                                    <div className="flex justify-between items-center mb-4">
                                        <h3 className="text-lg font-semibold text-gray-900">Add New Reminder</h3>
                                        <button
                                            onClick={() => setShowAddModal(false)}
                                            className="text-gray-400 hover:text-gray-600"
                                        >
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>

                                    <form onSubmit={handleCreateReminder}>
                                        <div className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reminder Title
                                                </label>
                                                <input
                                                    type="text"
                                                    value={formData.title}
                                                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                    placeholder="e.g., Sarah's Birthday"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Person
                                                </label>
                                                <input
                                                    type="text"
                                                    value={formData.person}
                                                    onChange={(e) => setFormData({ ...formData, person: e.target.value })}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                    placeholder="e.g., Sarah Johnson"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Event Date
                                                </label>
                                                <input
                                                    type="date"
                                                    value={formData.date}
                                                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reminder Date
                                                </label>
                                                <input
                                                    type="date"
                                                    value={formData.reminderDate}
                                                    onChange={(e) => setFormData({ ...formData, reminderDate: e.target.value })}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Type
                                                </label>
                                                <select
                                                    value={formData.type}
                                                    onChange={(e) => setFormData({ ...formData, type: e.target.value as 'birthday' | 'anniversary' | 'custom' })}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                >
                                                    <option value="birthday">Birthday</option>
                                                    <option value="anniversary">Anniversary</option>
                                                    <option value="custom">Custom Event</option>
                                                </select>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Notification Methods
                                                </label>
                                                <div className="space-y-2">
                                                    <label className="flex items-center">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.notifications.includes('Email')}
                                                            onChange={(e) => {
                                                                const notifications = e.target.checked
                                                                    ? [...formData.notifications, 'Email']
                                                                    : formData.notifications.filter(n => n !== 'Email');
                                                                setFormData({ ...formData, notifications });
                                                            }}
                                                            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                                        />
                                                        <span className="ml-2 text-sm text-gray-700">Email</span>
                                                    </label>
                                                    <label className="flex items-center">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.notifications.includes('Push')}
                                                            onChange={(e) => {
                                                                const notifications = e.target.checked
                                                                    ? [...formData.notifications, 'Push']
                                                                    : formData.notifications.filter(n => n !== 'Push');
                                                                setFormData({ ...formData, notifications });
                                                            }}
                                                            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                                        />
                                                        <span className="ml-2 text-sm text-gray-700">Push Notification</span>
                                                    </label>
                                                    <label className="flex items-center">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.notifications.includes('SMS')}
                                                            onChange={(e) => {
                                                                const notifications = e.target.checked
                                                                    ? [...formData.notifications, 'SMS']
                                                                    : formData.notifications.filter(n => n !== 'SMS');
                                                                setFormData({ ...formData, notifications });
                                                            }}
                                                            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                                        />
                                                        <span className="ml-2 text-sm text-gray-700">SMS</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex justify-end space-x-3 mt-6">
                                            <button
                                                type="button"
                                                onClick={() => setShowAddModal(false)}
                                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                                            >
                                                Add Reminder
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default RemindersPage;
