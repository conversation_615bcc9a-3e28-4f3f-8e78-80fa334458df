import { useEffect, useState } from "react";
import { useAuth } from "../context/AuthContext";
import { databaseService } from "../services/databaseService";

interface NotificationSettingsRow {
    id: string;
    user_id: string;
    reminder_enabled: boolean;
    email_notifications: boolean;
    push_notifications: boolean;
    reminder_days_before: number;
    created_at: string;
    updated_at: string;
}

const NotificationSettings = () => {
    const { user } = useAuth();
    const [reminderEnabled, setReminderEnabled] = useState(false);
    const [emailNotifications, setEmailNotifications] = useState(true);
    const [pushNotifications, setPushNotifications] = useState(false);
    const [reminderDaysBefore, setReminderDaysBefore] = useState(7);
    const [isLoading, setIsLoading] = useState(false);

    // Initialize database and load settings
    useEffect(() => {
        const loadSettings = async () => {
            if (!user) return;

            try {
                setIsLoading(true);
                await databaseService.initialize();

                const settings = await databaseService.query<NotificationSettingsRow>(
                    'SELECT * FROM notification_settings WHERE user_id = ?',
                    [user.email]
                );

                if (settings.length > 0) {
                    setReminderEnabled(settings[0].reminder_enabled);
                    setEmailNotifications(settings[0].email_notifications);
                    setPushNotifications(settings[0].push_notifications);
                    setReminderDaysBefore(settings[0].reminder_days_before);
                } else {
                    // Create default settings if none exist
                    await databaseService.create<NotificationSettingsRow>('notification_settings', {
                        user_id: user.email,
                        reminder_enabled: false,
                        email_notifications: true,
                        push_notifications: false,
                        reminder_days_before: 7
                    });
                }
            } catch (error) {
                console.error('Error loading notification settings:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadSettings();
    }, [user]);

    // Save settings when changed
    const saveSettings = async () => {
        if (!user) return;

        try {
            setIsLoading(true);

            const settings = await databaseService.query<NotificationSettingsRow>(
                'SELECT * FROM notification_settings WHERE user_id = ?',
                [user.email]
            );

            if (settings.length > 0) {
                await databaseService.query(
                    `UPDATE notification_settings
                     SET reminder_enabled = ?,
                         email_notifications = ?,
                         push_notifications = ?,
                         reminder_days_before = ?,
                         updated_at = ?
                     WHERE user_id = ?`,
                    [
                        reminderEnabled,
                        emailNotifications,
                        pushNotifications,
                        reminderDaysBefore,
                        new Date().toISOString(),
                        user.email
                    ]
                );
            } else {
                await databaseService.create<NotificationSettingsRow>('notification_settings', {
                    user_id: user.email,
                    reminder_enabled: reminderEnabled,
                    email_notifications: emailNotifications,
                    push_notifications: pushNotifications,
                    reminder_days_before: reminderDaysBefore
                });
            }
        } catch (error) {
            console.error('Error saving notification settings:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Save settings when they change
    useEffect(() => {
        if (user) {
            saveSettings();
        }
    }, [reminderEnabled, emailNotifications, pushNotifications, reminderDaysBefore, user]);

    if (!user) return null;

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Notification Settings</h2>
            <label className="inline-flex items-center space-x-3 cursor-pointer">
                <input
                    type="checkbox"
                    checked={reminderEnabled}
                    onChange={() => setReminderEnabled(!reminderEnabled)}
                    className="form-checkbox h-5 w-5 text-blue-600"
                />
                <span className="text-gray-700">Enable 1-day before birthday reminder</span>
            </label>
        </div>
    );
};

export default NotificationSettings;
