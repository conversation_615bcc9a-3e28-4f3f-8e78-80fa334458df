import { useEffect, useState } from "react";
import { useAuth } from "../context/AuthContext";

const STORAGE_KEY_PREFIX = "birthdaySaaSNotificationSettings_";

const NotificationSettings = () => {
    const { user } = useAuth();
    const [reminderEnabled, setReminderEnabled] = useState(false);

    // Load saved setting from localStorage
    useEffect(() => {
        if (user) {
            const saved = localStorage.getItem(STORAGE_KEY_PREFIX + user.email);
            setReminderEnabled(saved === "true");
        }
    }, [user]);

    // Save setting when changed
    useEffect(() => {
        if (user) {
            localStorage.setItem(STORAGE_KEY_PREFIX + user.email, reminderEnabled.toString());
        }
    }, [reminderEnabled, user]);

    if (!user) return null;

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Notification Settings</h2>
            <label className="inline-flex items-center space-x-3 cursor-pointer">
                <input
                    type="checkbox"
                    checked={reminderEnabled}
                    onChange={() => setReminderEnabled(!reminderEnabled)}
                    className="form-checkbox h-5 w-5 text-blue-600"
                />
                <span className="text-gray-700">Enable 1-day before birthday reminder</span>
            </label>
        </div>
    );
};

export default NotificationSettings;
