import React from 'react';
import LoadingSpinner from './LoadingSpinner';

interface LoadingOverlayProps {
    isVisible: boolean;
    message?: string;
    backdrop?: boolean;
    size?: 'sm' | 'md' | 'lg' | 'xl';
    className?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
    isVisible,
    message = 'Loading...',
    backdrop = true,
    size = 'lg',
    className = '',
}) => {
    if (!isVisible) return null;

    return (
        <div
            className={`fixed inset-0 z-50 flex items-center justify-center ${className}`}
            role="status"
            aria-live="polite"
            aria-label={message}
        >
            {backdrop && (
                <div className="absolute inset-0 bg-black bg-opacity-50 transition-opacity" />
            )}
            <div className="relative bg-white rounded-lg p-6 shadow-xl max-w-sm mx-4">
                <div className="flex flex-col items-center space-y-4">
                    <LoadingSpinner size={size} />
                    <p className="text-gray-700 text-center font-medium">{message}</p>
                </div>
            </div>
        </div>
    );
};

// Inline loading component for smaller areas
export const InlineLoader: React.FC<{
    message?: string;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}> = ({ message = 'Loading...', size = 'md', className = '' }) => (
    <div className={`flex items-center justify-center space-x-2 py-4 ${className}`}>
        <LoadingSpinner size={size} />
        <span className="text-gray-600">{message}</span>
    </div>
);

// Button loading state component
export const LoadingButton: React.FC<{
    isLoading: boolean;
    children: React.ReactNode;
    loadingText?: string;
    disabled?: boolean;
    className?: string;
    onClick?: () => void;
    type?: 'button' | 'submit' | 'reset';
}> = ({
    isLoading,
    children,
    loadingText,
    disabled = false,
    className = '',
    onClick,
    type = 'button',
}) => (
    <button
        type={type}
        onClick={onClick}
        disabled={isLoading || disabled}
        className={`flex items-center justify-center space-x-2 transition-all duration-200 ${
            isLoading || disabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:opacity-90'
        } ${className}`}
    >
        {isLoading && <LoadingSpinner size="sm" color="white" />}
        <span>{isLoading && loadingText ? loadingText : children}</span>
    </button>
);

export default LoadingOverlay;
