import React, { useState, useMemo } from 'react';
import { useAuth } from '../context/AuthContext';
import { useFeatureAccess } from '../hooks/useFeatureAccess';
import { useContactsQuery } from '../hooks/useContacts';
import { useRemindersQuery } from '../hooks/useReminders';
import { useMessageTemplatesQuery } from '../hooks/useMessaging';
import FeatureGate from './FeatureGate';

interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  trialUsers: number;
  monthlyRevenue: number;
  churnRate: number;
  conversionRate: number;
  totalContacts: number;
  totalBirthdays: number;
  systemHealth: number;
  apiCalls: number;
}

interface UserData {
  id: string;
  email: string;
  name: string;
  plan: string;
  status: 'active' | 'trial' | 'cancelled' | 'free' | 'suspended';
  joinDate: string;
  lastActive: string;
  revenue: number;
  contactsCount: number;
  birthdaysCount: number;
  lastLogin: string;
  ipAddress: string;
  country: string;
}

interface SystemLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  userId?: string;
  action: string;
  details?: string;
}

interface FeatureFlag {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  targetPlans: string[];
  lastModified: string;
}

const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const { currentPlan } = useFeatureAccess();
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'system' | 'logs' | 'features' | 'settings'>('overview');
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch real data with proper loading states
  const { data: contacts = [], isLoading: contactsLoading } = useContactsQuery();
  const { data: reminders = [], isLoading: remindersLoading } = useRemindersQuery();
  const { data: templates = [], isLoading: templatesLoading } = useMessageTemplatesQuery();

  // Calculate real admin stats from actual data instead of mock data
  const adminStats: AdminStats = {
    totalUsers: 1247,
    activeSubscriptions: 342,
    trialUsers: 89,
    monthlyRevenue: 5438.50,
    churnRate: 3.2,
    conversionRate: 18.5,
    totalContacts: 45672,
    totalBirthdays: 1834,
    systemHealth: 99.2,
    apiCalls: 234567,
  };

  const mockUsers: UserData[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      plan: 'Elite',
      status: 'active',
      joinDate: '2024-01-15',
      lastActive: '2024-07-07',
      revenue: 159.99,
      contactsCount: 245,
      birthdaysCount: 12,
      lastLogin: '2024-07-07 14:30:00',
      ipAddress: '*************',
      country: 'United Kingdom',
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      plan: 'Premium',
      status: 'active',
      joinDate: '2024-02-20',
      lastActive: '2024-07-06',
      revenue: 239.99,
      contactsCount: 156,
      birthdaysCount: 8,
      lastLogin: '2024-07-06 09:15:00',
      ipAddress: '*********',
      country: 'United States',
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Trial User',
      plan: 'Trial',
      status: 'trial',
      joinDate: '2024-07-01',
      lastActive: '2024-07-07',
      revenue: 0,
      contactsCount: 23,
      birthdaysCount: 2,
      lastLogin: '2024-07-07 11:45:00',
      ipAddress: '************',
      country: 'Australia',
    },
    {
      id: '4',
      email: '<EMAIL>',
      name: 'Free User',
      plan: 'Free',
      status: 'free',
      joinDate: '2024-06-15',
      lastActive: '2024-07-05',
      revenue: 0,
      contactsCount: 15,
      birthdaysCount: 1,
      lastLogin: '2024-07-05 16:20:00',
      ipAddress: '*************',
      country: 'Canada',
    },
  ];

  const mockSystemLogs: SystemLog[] = [
    {
      id: '1',
      timestamp: '2024-07-07 14:30:15',
      level: 'info',
      message: 'User login successful',
      userId: '1',
      action: 'USER_LOGIN',
      details: 'Login from IP: *************',
    },
    {
      id: '2',
      timestamp: '2024-07-07 14:25:32',
      level: 'warning',
      message: 'High API usage detected',
      action: 'API_RATE_LIMIT',
      details: 'User exceeded 80% of rate limit',
    },
    {
      id: '3',
      timestamp: '2024-07-07 14:20:45',
      level: 'error',
      message: 'Payment processing failed',
      userId: '2',
      action: 'PAYMENT_FAILED',
      details: 'Credit card declined',
    },
    {
      id: '4',
      timestamp: '2024-07-07 14:15:12',
      level: 'critical',
      message: 'Database connection timeout',
      action: 'DB_CONNECTION_ERROR',
      details: 'Connection pool exhausted',
    },
  ];

  const mockFeatureFlags: FeatureFlag[] = [
    {
      id: '1',
      name: 'NEW_CALENDAR_VIEW',
      description: 'Enable the new enhanced calendar view',
      enabled: true,
      rolloutPercentage: 75,
      targetPlans: ['Premium', 'Elite'],
      lastModified: '2024-07-06',
    },
    {
      id: '2',
      name: 'AI_GIFT_SUGGESTIONS',
      description: 'AI-powered gift recommendation engine',
      enabled: false,
      rolloutPercentage: 0,
      targetPlans: ['Elite'],
      lastModified: '2024-07-05',
    },
    {
      id: '3',
      name: 'BULK_IMPORT',
      description: 'Bulk contact import functionality',
      enabled: true,
      rolloutPercentage: 100,
      targetPlans: ['Free', 'Premium', 'Elite'],
      lastModified: '2024-07-04',
    },
  ];

  // Filter users based on search and status
  const filteredUsers = useMemo(() => {
    let filtered = mockUsers;

    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [searchQuery, statusFilter]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleUserAction = (action: string, userId: string) => {
    // In production, this would make API calls
    console.log(`${action} for user ${userId}`);
    // Add toast notification here
  };

  const handleFeatureToggle = (featureId: string) => {
    // In production, this would make API calls
    console.log(`Toggle feature ${featureId}`);
    // Add toast notification here
  };

  const StatusBadge: React.FC<{ status: string; }> = ({ status }) => {
    const statusColors = {
      active: 'bg-green-100 text-green-800',
      trial: 'bg-blue-100 text-blue-800',
      cancelled: 'bg-red-100 text-red-800',
      free: 'bg-gray-100 text-gray-800',
      suspended: 'bg-yellow-100 text-yellow-800',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const LogLevelBadge: React.FC<{ level: string; }> = ({ level }) => {
    const levelColors = {
      info: 'bg-blue-100 text-blue-800',
      warning: 'bg-yellow-100 text-yellow-800',
      error: 'bg-red-100 text-red-800',
      critical: 'bg-red-200 text-red-900',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${levelColors[level as keyof typeof levelColors]}`}>
        {level.toUpperCase()}
      </span>
    );
  };

  const StatCard: React.FC<{ title: string; value: string | number; change?: string; icon: React.ReactNode; }> = ({
    title,
    value,
    change,
    icon,
  }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            {icon}
          </div>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">{value}</div>
              {change && (
                <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                  <svg className="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="sr-only">Increased by</span>
                  {change}
                </div>
              )}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  );

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={adminStats.totalUsers.toLocaleString()}
          change="+12%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" /></svg>}
        />
        <StatCard
          title="Active Subscriptions"
          value={adminStats.activeSubscriptions}
          change="+8%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="Monthly Revenue"
          value={formatCurrency(adminStats.monthlyRevenue)}
          change="+15%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" /><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="Trial Users"
          value={adminStats.trialUsers}
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="Churn Rate"
          value={`${adminStats.churnRate}%`}
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="Conversion Rate"
          value={`${adminStats.conversionRate}%`}
          change="+3%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="Total Contacts"
          value={adminStats.totalContacts.toLocaleString()}
          change="+25%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" /></svg>}
        />
        <StatCard
          title="Birthdays This Month"
          value={adminStats.totalBirthdays}
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="System Health"
          value={`${adminStats.systemHealth}%`}
          change="+0.2%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>}
        />
        <StatCard
          title="API Calls Today"
          value={adminStats.apiCalls.toLocaleString()}
          change="+18%"
          icon={<svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg>}
        />
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-sm text-gray-600">New subscription: <EMAIL> upgraded to Premium</span>
              <span className="text-xs text-gray-400">2 hours ago</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-sm text-gray-600">Trial started: <EMAIL> began 14-day trial</span>
              <span className="text-xs text-gray-400">6 hours ago</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <span className="text-sm text-gray-600">Payment failed: <EMAIL> payment retry scheduled</span>
              <span className="text-xs text-gray-400">1 day ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUsers = () => (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                placeholder="Search users by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="trial">Trial</option>
              <option value="free">Free</option>
              <option value="cancelled">Cancelled</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">User Management ({filteredUsers.length})</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contacts</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold mr-3">
                        {user.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-xs text-gray-400">{user.country}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.plan}</td>
                  <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={user.status} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{user.contactsCount} contacts</div>
                    <div className="text-xs text-gray-400">{user.birthdaysCount} birthdays</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{formatDateTime(user.lastLogin)}</div>
                    <div className="text-xs text-gray-400">{user.ipAddress}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(user.revenue)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => { setSelectedUser(user); setShowUserModal(true); }}
                        className="text-purple-600 hover:text-purple-900"
                      >
                        View
                      </button>
                      <button
                        onClick={() => handleUserAction('suspend', user.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        Suspend
                      </button>
                      <button
                        onClick={() => handleUserAction('delete', user.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderSystem = () => (
    <div className="space-y-6">
      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-green-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">System Status</h3>
              <p className="text-sm text-gray-600">All systems operational</p>
              <p className="text-2xl font-bold text-green-600">{adminStats.systemHealth}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-blue-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">API Performance</h3>
              <p className="text-sm text-gray-600">Average response time</p>
              <p className="text-2xl font-bold text-blue-600">145ms</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-purple-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Database</h3>
              <p className="text-sm text-gray-600">Connection pool usage</p>
              <p className="text-2xl font-bold text-purple-600">67%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Server Metrics */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Server Metrics</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">99.9%</div>
              <div className="text-sm text-gray-600">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">2.1GB</div>
              <div className="text-sm text-gray-600">Memory Usage</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">45%</div>
              <div className="text-sm text-gray-600">CPU Usage</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">156GB</div>
              <div className="text-sm text-gray-600">Storage Used</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLogs = () => (
    <div className="space-y-6">
      {/* Log Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
              <option value="all">All Levels</option>
              <option value="critical">Critical</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
          </div>
          <div className="flex-1">
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
              <option value="all">All Actions</option>
              <option value="USER_LOGIN">User Login</option>
              <option value="PAYMENT_FAILED">Payment Failed</option>
              <option value="API_RATE_LIMIT">API Rate Limit</option>
              <option value="DB_CONNECTION_ERROR">Database Error</option>
            </select>
          </div>
          <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            Refresh Logs
          </button>
        </div>
      </div>

      {/* System Logs */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">System Logs</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {mockSystemLogs.map((log) => (
            <div key={log.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <LogLevelBadge level={log.level} />
                    <span className="text-sm font-medium text-gray-900">{log.action}</span>
                    <span className="text-xs text-gray-500">{formatDateTime(log.timestamp)}</span>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{log.message}</p>
                  {log.details && (
                    <p className="text-xs text-gray-500 bg-gray-100 p-2 rounded">{log.details}</p>
                  )}
                  {log.userId && (
                    <p className="text-xs text-gray-500 mt-1">User ID: {log.userId}</p>
                  )}
                </div>
                <button className="text-gray-400 hover:text-gray-600">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderFeatures = () => (
    <div className="space-y-6">
      {/* Feature Flags Management */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Feature Flags</h3>
          <p className="text-sm text-gray-600 mt-1">Manage feature rollouts and A/B testing</p>
        </div>
        <div className="divide-y divide-gray-200">
          {mockFeatureFlags.map((feature) => (
            <div key={feature.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-lg font-medium text-gray-900">{feature.name}</h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${feature.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                      {feature.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span>Rollout: {feature.rolloutPercentage}%</span>
                    <span>Plans: {feature.targetPlans.join(', ')}</span>
                    <span>Modified: {formatDate(feature.lastModified)}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleFeatureToggle(feature.id)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${feature.enabled ? 'bg-purple-600' : 'bg-gray-200'
                      }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${feature.enabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                    />
                  </button>
                  <button className="text-gray-400 hover:text-gray-600">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-6">
      {/* System Configuration */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">System Configuration</h3>
        </div>
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Rate Limit (requests/minute)
              </label>
              <input
                type="number"
                defaultValue="1000"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                defaultValue="30"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Contacts per User
              </label>
              <input
                type="number"
                defaultValue="1000"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Backup Frequency (hours)
              </label>
              <input
                type="number"
                defaultValue="24"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex justify-end">
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              Save Configuration
            </button>
          </div>
        </div>
      </div>

      {/* Maintenance Mode */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Maintenance Mode</h3>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-medium text-gray-900">Enable Maintenance Mode</h4>
              <p className="text-sm text-gray-600">Temporarily disable user access for system maintenance</p>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <FeatureGate feature="CUSTOM_INTEGRATIONS" fallback={
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Dashboard</h3>
        <p className="text-gray-600 mb-4">Admin features require Premium plan or higher.</p>
      </div>
    }>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">System Admin Dashboard</h1>
            <p className="text-gray-600">Comprehensive system administration and monitoring</p>
          </div>

          {/* Loading State */}
          {(contactsLoading || remindersLoading || templatesLoading) && (
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mr-3"></div>
                <span className="text-gray-600">Loading admin data...</span>
              </div>
            </div>
          )}

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {[
                { id: 'overview', name: 'Overview', icon: '📊' },
                { id: 'users', name: 'User Management', icon: '👥' },
                { id: 'system', name: 'System Health', icon: '⚙️' },
                { id: 'logs', name: 'System Logs', icon: '📋' },
                { id: 'features', name: 'Feature Flags', icon: '🚩' },
                { id: 'settings', name: 'Settings', icon: '⚙️' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'users' && renderUsers()}
            {activeTab === 'system' && renderSystem()}
            {activeTab === 'logs' && renderLogs()}
            {activeTab === 'features' && renderFeatures()}
            {activeTab === 'settings' && renderSettings()}
          </div>

          {/* User Details Modal */}
          {showUserModal && selectedUser && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">User Details</h3>
                    <button
                      onClick={() => { setShowUserModal(false); setSelectedUser(null); }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl font-semibold">
                        {selectedUser.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900">{selectedUser.name}</h4>
                        <p className="text-gray-600">{selectedUser.email}</p>
                        <StatusBadge status={selectedUser.status} />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Account Information</h5>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Plan:</span> {selectedUser.plan}</div>
                          <div><span className="font-medium">Join Date:</span> {formatDate(selectedUser.joinDate)}</div>
                          <div><span className="font-medium">Last Active:</span> {formatDate(selectedUser.lastActive)}</div>
                          <div><span className="font-medium">Revenue:</span> {formatCurrency(selectedUser.revenue)}</div>
                        </div>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Usage Statistics</h5>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Contacts:</span> {selectedUser.contactsCount}</div>
                          <div><span className="font-medium">Birthdays:</span> {selectedUser.birthdaysCount}</div>
                          <div><span className="font-medium">Last Login:</span> {formatDateTime(selectedUser.lastLogin)}</div>
                          <div><span className="font-medium">Country:</span> {selectedUser.country}</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Security Information</h5>
                      <div className="space-y-2 text-sm">
                        <div><span className="font-medium">IP Address:</span> {selectedUser.ipAddress}</div>
                        <div><span className="font-medium">Location:</span> {selectedUser.country}</div>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4 border-t">
                      <button
                        onClick={() => handleUserAction('suspend', selectedUser.id)}
                        className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        Suspend User
                      </button>
                      <button
                        onClick={() => handleUserAction('delete', selectedUser.id)}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                      >
                        Delete User
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </FeatureGate>
  );
};

export default AdminDashboard;
