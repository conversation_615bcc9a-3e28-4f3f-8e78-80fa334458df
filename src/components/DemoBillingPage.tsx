import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';

const DemoBillingPage: React.FC = () => {
  const { user } = useAuth();
  const [showCancelModal, setShowCancelModal] = useState(false);

  // Mock data for demo purposes
  const mockSubscriptionData = {
    currentPlan: 'Elite',
    status: 'Active',
    nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    chargeAmount: 15.99,
    customerId: 'cus_demo123456789',
    subscriptionId: 'sub_demo987654321',
    trialDaysLeft: 0,
    isTrialActive: false,
    hasActiveSubscription: true
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric',
      month: 'long', 
      day: 'numeric' 
    });
  };

  const handleOpenBillingPortal = () => {
    // Demo: Show alert instead of opening real Stripe portal
    alert('🎭 DEMO MODE: In a real app, this would open the Stripe Customer Portal where you can:\n\n• Update payment methods\n• Download invoices\n• View payment history\n• Cancel subscription\n• Update billing address');
  };

  const handleCancelSubscription = () => {
    alert('🎭 DEMO MODE: In a real app, this would open the Stripe portal for safe subscription cancellation.');
    setShowCancelModal(false);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">Please log in to view your billing information.</p>
          <a href="/login" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Demo Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Demo Mode</h3>
              <p className="text-sm text-yellow-700">This is a demo billing page with mock data. Real Stripe integration requires backend setup.</p>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button 
              onClick={() => window.history.back()}
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 className="text-2xl font-semibold text-gray-900">Billing & Invoices</h1>
          </div>
          <p className="text-gray-600">Manage your billing information and view payment history.</p>
        </div>

        {/* Current Subscription Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Current Subscription</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Plan</h3>
              <p className="text-lg font-semibold text-gray-900">{mockSubscriptionData.currentPlan}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Status</h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                mockSubscriptionData.hasActiveSubscription 
                  ? 'bg-green-100 text-green-800' 
                  : mockSubscriptionData.isTrialActive 
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
              }`}>
                {mockSubscriptionData.hasActiveSubscription ? 'Active' : mockSubscriptionData.isTrialActive ? `Trial (${mockSubscriptionData.trialDaysLeft} days left)` : 'Free'}
              </span>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                {mockSubscriptionData.isTrialActive ? 'Trial Ends' : 'Next Billing'}
              </h3>
              <p className="text-lg font-semibold text-gray-900">
                {formatDate(mockSubscriptionData.nextBillingDate)}
              </p>
            </div>
          </div>

          {mockSubscriptionData.hasActiveSubscription && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Monthly charge</p>
                  <p className="text-lg font-semibold text-gray-900">${mockSubscriptionData.chargeAmount.toFixed(2)}</p>
                </div>
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="text-red-600 hover:text-red-700 text-sm font-medium"
                >
                  Cancel Subscription
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Payment Method & Billing Portal */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Payment & Billing Management</h2>
          <p className="text-gray-600 mb-4">
            Manage your payment methods, update billing information, download invoices, and view payment history.
          </p>
          
          <button
            onClick={handleOpenBillingPortal}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Open Billing Portal (Demo)
          </button>
          
          <p className="text-xs text-gray-500 mt-2">
            In production, you'll be redirected to Stripe's secure billing portal.
          </p>
        </div>

        {/* Billing Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Billing Information</h2>
          
          <div className="space-y-3">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Email</h3>
              <p className="text-gray-900">{user.email}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Customer ID</h3>
              <p className="text-gray-900 font-mono text-sm">{mockSubscriptionData.customerId}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Subscription ID</h3>
              <p className="text-gray-900 font-mono text-sm">{mockSubscriptionData.subscriptionId}</p>
            </div>
          </div>
        </div>

        {/* Recent Invoices (Mock) */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Invoices</h2>
          
          <div className="space-y-3">
            {[
              { date: '2024-01-15', amount: 15.99, status: 'Paid', id: 'in_demo001' },
              { date: '2023-12-15', amount: 15.99, status: 'Paid', id: 'in_demo002' },
              { date: '2023-11-15', amount: 15.99, status: 'Paid', id: 'in_demo003' },
            ].map((invoice, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">${invoice.amount}</p>
                    <p className="text-xs text-gray-500">{invoice.date}</p>
                  </div>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    invoice.status === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {invoice.status}
                  </span>
                </div>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  Download
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">Need Help?</h2>
          <p className="text-gray-600 mb-4">
            If you have questions about your billing or need assistance, we're here to help.
          </p>
          <div className="flex space-x-4">
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Contact Support
            </a>
            <a
              href="/subscription"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              View Plans
            </a>
          </div>
        </div>

        {/* Cancel Subscription Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Cancel Subscription</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to cancel your subscription? In a real app, you'd be redirected to the billing portal 
                where you can manage your subscription and see cancellation options.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Keep Subscription
                </button>
                <button
                  onClick={handleCancelSubscription}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Demo Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DemoBillingPage;
