import React, { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const LandingPage: React.FC = () => {
    const { user } = useAuth();
    const navigate = useNavigate();

    // Redirect authenticated users to dashboard
    useEffect(() => {
        if (user) {
            navigate('/dashboard');
        }
    }, [user, navigate]);

    return (
        <div className="min-h-screen flex flex-col">
            {/* Navigation */}
            <nav className="bg-white shadow-sm">
                <div className="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div className="text-xl font-bold text-blue-600">WeWish</div>
                    <div className="flex space-x-4">
                        <Link
                            to="/subscription"
                            className="text-gray-600 hover:text-blue-600 transition"
                        >
                            Pricing
                        </Link>
                        <Link
                            to="/login"
                            className="text-gray-600 hover:text-blue-600 transition"
                        >
                            Sign In
                        </Link>
                        <Link
                            to="/signup"
                            className="bg-yellow-400 text-blue-900 px-4 py-2 rounded font-semibold hover:bg-yellow-300 transition"
                        >
                            Start Free Trial
                        </Link>
                    </div>
                </div>
            </nav>

            {/* Hero Section */}
            <header className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white flex-1 flex items-center justify-center">
                <div className="container mx-auto px-6 py-20 flex flex-col items-center text-center max-w-5xl">
                    {/* Trial Badge */}
                    <div className="bg-yellow-400 text-purple-900 px-6 py-3 rounded-full text-sm font-bold mb-8 animate-pulse shadow-lg">
                        🎉 FREE 14-Day Trial • No Credit Card Required • Cancel Anytime
                    </div>

                    {/* Main Headline */}
                    <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
                        Never Miss Another
                        <span className="block text-yellow-400">Birthday or Anniversary!</span>
                    </h1>

                    {/* Subheadline */}
                    <p className="text-xl md:text-2xl mb-10 text-pink-100 max-w-4xl leading-relaxed">
                        The complete relationship management platform that helps you remember every birthday, anniversary, and special moment.
                        Strengthen relationships, celebrate milestones, and become the person everyone loves for never forgetting.
                    </p>

                    {/* Key Benefits */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl">
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                            <div className="text-3xl mb-3">🎂</div>
                            <h3 className="font-semibold text-lg mb-2">Birthday Tracking</h3>
                            <p className="text-pink-100 text-sm">Smart reminders, calendar views, and automated notifications</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                            <div className="text-3xl mb-3">💕</div>
                            <h3 className="font-semibold text-lg mb-2">Anniversary Management</h3>
                            <p className="text-pink-100 text-sm">Wedding, dating, business anniversaries with milestone recognition</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                            <div className="text-3xl mb-3">📱</div>
                            <h3 className="font-semibold text-lg mb-2">Smart Automation</h3>
                            <p className="text-pink-100 text-sm">Automated messages, gift suggestions, and celebration planning</p>
                        </div>
                    </div>

                    {/* CTA Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 mb-12">
                        <Link
                            to="/signup"
                            className="bg-yellow-400 text-purple-900 px-10 py-4 rounded-lg text-xl font-bold hover:bg-yellow-300 transition transform hover:scale-105 shadow-xl"
                        >
                            Start Your Free Trial
                        </Link>
                        <Link
                            to="/subscription"
                            className="border-2 border-white text-white px-10 py-4 rounded-lg text-xl font-semibold hover:bg-white hover:text-purple-600 transition shadow-lg"
                        >
                            View Pricing
                        </Link>
                    </div>

                    {/* Trust Indicators */}
                    <div className="flex flex-wrap justify-center items-center gap-8 text-pink-200">
                        <div className="flex items-center gap-2">
                            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">No Credit Card Required</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">14-Day Free Trial</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">Cancel Anytime</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">Secure & Private</span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Customer Logos Section */}
            <section className="bg-white py-12 border-b">
                <div className="container mx-auto px-6">
                    <p className="text-center text-gray-600 mb-8 text-sm">
                        Trusted by teams and organizations worldwide
                    </p>
                    <div className="relative overflow-hidden">
                        <div className="flex animate-scroll space-x-12 items-center">
                            {/* First set of logos */}
                            <div className="flex space-x-12 items-center min-w-max">
                                {/* Google */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                    </svg>
                                    <span className="font-medium">Google</span>
                                </div>

                                {/* Apple */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                                    </svg>
                                    <span className="font-medium">Apple</span>
                                </div>

                                {/* Microsoft */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z" />
                                    </svg>
                                    <span className="font-medium">Microsoft</span>
                                </div>

                                {/* Glassdoor */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z" />
                                        <path d="M12 6c-3.314 0-6 2.686-6 6s2.686 6 6 6 6-2.686 6-6-2.686-6-6-6zm0 10c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4z" />
                                    </svg>
                                    <span className="font-medium">Glassdoor</span>
                                </div>

                                {/* RCCG */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                    </svg>
                                    <span className="font-medium">RCCG</span>
                                </div>

                                {/* Churches */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2L8 6v3H6v11h4v-6h4v6h4V9h-2V6l-4-4z" />
                                        <path d="M12 1l-1 1v2l1-1 1 1V2l-1-1z" />
                                    </svg>
                                    <span className="font-medium">Churches United</span>
                                </div>

                                {/* Shopify */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M15.337 2.367c-.332-.066-.664-.066-.996 0-2.324.464-4.648 1.328-6.64 2.656-.664.464-1.328.996-1.992 1.66-.332.332-.664.664-.996 1.064-.332.332-.664.732-.996 1.128-.332.464-.664.928-.996 1.46-.332.532-.664 1.064-.996 1.66-.332.596-.664 1.26-.996 1.924-.332.732-.664 1.46-.996 2.258-.332.796-.664 1.66-.996 2.59-.332.928-.664 1.924-.996 2.988-.066.332-.066.664 0 .996.066.332.198.664.398.928.2.264.464.464.796.596.332.132.664.132.996 0 .332-.132.664-.332.928-.596.264-.264.464-.596.596-.996.132-.4.132-.796 0-1.196-.132-.4-.332-.796-.596-1.128-.264-.332-.596-.596-.996-.796-.4-.2-.796-.2-1.196 0-.4.2-.796.464-1.128.796-.332.332-.596.732-.796 1.196-.2.464-.2.996 0 1.46.2.464.596.864 1.128 1.128.532.264 1.128.264 1.66 0 .532-.264.996-.732 1.26-1.328.264-.596.264-1.26 0-1.856-.264-.596-.732-1.064-1.328-1.328-.596-.264-1.26-.264-1.856 0-.596.264-1.064.732-1.328 1.328-.264.596-.264 1.26 0 1.856.264.596.732 1.064 1.328 1.328.596.264 1.26.264 1.856 0 .596-.264 1.064-.732 1.328-1.328.264-.596.264-1.26 0-1.856-.264-.596-.732-1.064-1.328-1.328z" />
                                    </svg>
                                    <span className="font-medium">Shopify</span>
                                </div>

                                {/* Airbnb */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z" />
                                        <circle cx="12" cy="8" r="3" />
                                        <path d="M12 14c-3 0-7 1.5-7 4.5V20h14v-1.5c0-3-4-4.5-7-4.5z" />
                                    </svg>
                                    <span className="font-medium">Airbnb</span>
                                </div>
                            </div>

                            {/* Duplicate set for seamless loop */}
                            <div className="flex space-x-12 items-center min-w-max">
                                {/* Google */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                    </svg>
                                    <span className="font-medium">Google</span>
                                </div>

                                {/* Apple */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                                    </svg>
                                    <span className="font-medium">Apple</span>
                                </div>

                                {/* Microsoft */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z" />
                                    </svg>
                                    <span className="font-medium">Microsoft</span>
                                </div>

                                {/* Glassdoor */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z" />
                                        <path d="M12 6c-3.314 0-6 2.686-6 6s2.686 6 6 6 6-2.686 6-6-2.686-6-6-6zm0 10c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4z" />
                                    </svg>
                                    <span className="font-medium">Glassdoor</span>
                                </div>

                                {/* RCCG */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                    </svg>
                                    <span className="font-medium">RCCG</span>
                                </div>

                                {/* Churches */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2L8 6v3H6v11h4v-6h4v6h4V9h-2V6l-4-4z" />
                                        <path d="M12 1l-1 1v2l1-1 1 1V2l-1-1z" />
                                    </svg>
                                    <span className="font-medium">Churches United</span>
                                </div>

                                {/* Shopify */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M15.337 2.367c-.332-.066-.664-.066-.996 0-2.324.464-4.648 1.328-6.64 2.656-.664.464-1.328.996-1.992 1.66-.332.332-.664.664-.996 1.064-.332.332-.664.732-.996 1.128-.332.464-.664.928-.996 1.46-.332.532-.664 1.064-.996 1.66-.332.596-.664 1.26-.996 1.924-.332.732-.664 1.46-.996 2.258-.332.796-.664 1.66-.996 2.59-.332.928-.664 1.924-.996 2.988-.066.332-.066.664 0 .996.066.332.198.664.398.928.2.264.464.464.796.596.332.132.664.132.996 0 .332-.132.664-.332.928-.596.264-.264.464-.596.596-.996.132-.4.132-.796 0-1.196-.132-.4-.332-.796-.596-1.128-.264-.332-.596-.596-.996-.796-.4-.2-.796-.2-1.196 0-.4.2-.796.464-1.128.796-.332.332-.596.732-.796 1.196-.2.464-.2.996 0 1.46.2.464.596.864 1.128 1.128.532.264 1.128.264 1.66 0 .532-.264.996-.732 1.26-1.328.264-.596.264-1.26 0-1.856-.264-.596-.732-1.064-1.328-1.328-.596-.264-1.26-.264-1.856 0-.596.264-1.064.732-1.328 1.328-.264.596-.264 1.26 0 1.856.264.596.732 1.064 1.328 1.328.596.264 1.26.264 1.856 0 .596-.264 1.064-.732 1.328-1.328.264-.596.264-1.26 0-1.856-.264-.596-.732-1.064-1.328-1.328z" />
                                    </svg>
                                    <span className="font-medium">Shopify</span>
                                </div>

                                {/* Airbnb */}
                                <div className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition">
                                    <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z" />
                                        <circle cx="12" cy="8" r="3" />
                                        <path d="M12 14c-3 0-7 1.5-7 4.5V20h14v-1.5c0-3-4-4.5-7-4.5z" />
                                    </svg>
                                    <span className="font-medium">Airbnb</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="flex-grow bg-gray-50 py-20">
                <div className="container mx-auto px-6 max-w-6xl">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                            Everything You Need to Never Forget Again
                        </h2>
                        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            Get full access to all premium features for 14 days. No limitations, no hidden fees.
                            Become the person everyone loves for remembering every special moment.
                        </p>
                        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-full inline-block">
                            <span className="font-semibold">🎉 All Features Included in Free Trial</span>
                        </div>
                    </div>
                    <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        <FeatureCard
                            title="🎂 Birthday Tracking"
                            description="Smart dashboard with upcoming birthdays, countdown timers, and beautiful calendar views."
                            badge="Core Feature"
                            icon={
                                <div className="text-4xl">🎂</div>
                            }
                        />
                        <FeatureCard
                            title="💕 Anniversary Management"
                            description="Track wedding, dating, and business anniversaries with milestone recognition and celebration planning."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">💕</div>
                            }
                        />
                        <FeatureCard
                            title="📱 Smart Reminders"
                            description="Automated notifications 1 week, 3 days, and day-of. Customize timing for each contact."
                            badge="Free in Trial"
                            icon={
                                <div className="text-4xl">📱</div>
                            }
                        />
                        <FeatureCard
                            title="📊 Analytics Dashboard"
                            description="Track celebration history, busiest months, and relationship insights with beautiful charts."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">📊</div>
                            }
                        />
                        <FeatureCard
                            title="📅 Calendar Views"
                            description="Beautiful monthly and yearly calendar views with color-coded events and milestone highlighting."
                            badge="Core Feature"
                            icon={
                                <div className="text-4xl">📅</div>
                            }
                        />
                        <FeatureCard
                            title="🎁 Gift Suggestions"
                            description="AI-powered gift recommendations based on age, interests, relationship, and celebration type."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">🎁</div>
                            }
                        />
                        <FeatureCard
                            title="📤 Message Templates"
                            description="Pre-written birthday and anniversary messages. Customize and send via SMS, email, or WhatsApp."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">📤</div>
                            }
                        />
                        <FeatureCard
                            title="👥 Contact Management"
                            description="Organize contacts by categories, add photos, notes, and track relationship details effortlessly."
                            badge="Core Feature"
                            icon={
                                <div className="text-4xl">👥</div>
                            }
                        />
                        <FeatureCard
                            title="🎉 Milestone Celebrations"
                            description="Special recognition for landmark birthdays (18th, 21st, 30th, 50th) and anniversary milestones."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">🎉</div>
                            }
                        />
                        <FeatureCard
                            title="📋 Bulk Import"
                            description="Import contacts from CSV, Google Contacts, or social media. Add multiple contacts at once."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">📋</div>
                            }
                        />
                        <FeatureCard
                            title="🔔 Weekly Notifications"
                            description="Automated weekly admin summaries of upcoming birthdays and anniversaries."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">🔔</div>
                            }
                        />
                        <FeatureCard
                            title="🎨 Customization"
                            description="Personalize colors, themes, reminder timing, and message templates to match your style."
                            badge="Premium Feature"
                            icon={
                                <div className="text-4xl">🎨</div>
                            }
                        />
                    </div>

                    {/* Additional Benefits Section */}
                    <div className="mt-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-12 text-white text-center">
                        <h3 className="text-3xl font-bold mb-6">Why Choose TrackCelebrations?</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                            <div>
                                <div className="text-4xl mb-4">🚀</div>
                                <h4 className="text-xl font-semibold mb-2">Never Forget Again</h4>
                                <p className="text-pink-100">Become the person everyone loves for remembering every special moment</p>
                            </div>
                            <div>
                                <div className="text-4xl mb-4">💝</div>
                                <h4 className="text-xl font-semibold mb-2">Strengthen Relationships</h4>
                                <p className="text-pink-100">Show you care with timely celebrations and thoughtful gestures</p>
                            </div>
                            <div>
                                <div className="text-4xl mb-4">⏰</div>
                                <h4 className="text-xl font-semibold mb-2">Save Time & Stress</h4>
                                <p className="text-pink-100">Automated reminders and planning tools handle everything for you</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Social Proof Section */}
            <section className="bg-white py-16 border-t">
                <div className="container mx-auto px-6 text-center max-w-6xl">
                    <h2 className="text-2xl font-bold mb-8 text-gray-800">
                        Trusted by thousands of families worldwide
                    </h2>
                    <div className="grid md:grid-cols-3 gap-8 mb-12">
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="text-yellow-500 mb-2">⭐⭐⭐⭐⭐</div>
                            <p className="text-gray-600 mb-4">"WeWish saved my relationships! I never forget birthdays anymore and my family loves the thoughtful gifts."</p>
                            <p className="font-semibold text-gray-800">- Sarah M.</p>
                        </div>
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="text-yellow-500 mb-2">⭐⭐⭐⭐⭐</div>
                            <p className="text-gray-600 mb-4">"The 14-day trial convinced me immediately. Now I'm the family member who remembers everyone's special day!"</p>
                            <p className="font-semibold text-gray-800">- Mike R.</p>
                        </div>
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="text-yellow-500 mb-2">⭐⭐⭐⭐⭐</div>
                            <p className="text-gray-600 mb-4">"Simple, effective, and the gift suggestions are spot-on. Worth every penny after the free trial."</p>
                            <p className="font-semibold text-gray-800">- Jennifer L.</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Trial CTA Section */}
            <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
                <div className="container mx-auto px-6 text-center max-w-4xl">
                    <h2 className="text-3xl font-bold mb-4">
                        Ready to Never Miss Another Birthday?
                    </h2>
                    <p className="text-xl mb-8 opacity-90">
                        Join over 10,000+ users who trust WeWish to keep their relationships strong
                    </p>

                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 mb-8 border border-white/20">
                        <div className="grid md:grid-cols-3 gap-6 text-center">
                            <div>
                                <div className="text-3xl font-bold text-yellow-400">14</div>
                                <div className="text-sm">Days Free Trial</div>
                            </div>
                            <div>
                                <div className="text-3xl font-bold text-yellow-400">£0</div>
                                <div className="text-sm">Setup Fee</div>
                            </div>
                            <div>
                                <div className="text-3xl font-bold text-yellow-400">∞</div>
                                <div className="text-sm">Contacts</div>
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Link
                            to="/signup"
                            className="bg-yellow-400 text-blue-900 font-bold px-10 py-4 rounded-lg shadow-lg hover:bg-yellow-300 transition text-center text-lg"
                        >
                            Start Your Free Trial Now
                        </Link>
                        <div className="text-sm opacity-75">
                            <div>✓ No credit card required</div>
                            <div>✓ Cancel anytime</div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-gray-900 text-gray-300 py-8 text-center">
                <div className="container mx-auto px-6">
                    <div className="mb-4">
                        <Link to="/" className="text-blue-400 hover:text-blue-300 mx-3">Home</Link>
                        <Link to="/subscription" className="text-blue-400 hover:text-blue-300 mx-3">Pricing</Link>
                        <Link to="/login" className="text-blue-400 hover:text-blue-300 mx-3">Login</Link>
                        <Link to="/signup" className="text-blue-400 hover:text-blue-300 mx-3">Sign Up</Link>
                    </div>
                    <p className="text-sm">&copy; {new Date().getFullYear()} TrackCelebrations. All rights reserved.</p>
                </div>
            </footer>
        </div>
    );
};

interface FeatureCardProps {
    title: string;
    description: string;
    icon: React.ReactNode;
    badge?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon, badge }) => (
    <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition cursor-default relative">
        {badge && (
            <div className="absolute top-4 right-4 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                {badge}
            </div>
        )}
        <div className="mb-4">{icon}</div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-gray-600">{description}</p>
    </div>
);

export default LandingPage;
