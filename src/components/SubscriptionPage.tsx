import { useAuth } from "../context/AuthContext";
import SimpleSubscriptionManagement from "./SimpleSubscriptionManagement";
import SubscriptionManagement from "./SubscriptionManagement";

const SubscriptionPage = () => {
    const { user } = useAuth();

    // If user is logged in, show the simple subscription management (your screenshot design)
    // If user is not logged in, show the full pricing page with all plans
    return user ? <SimpleSubscriptionManagement /> : <SubscriptionManagement />;
};

export default SubscriptionPage;
