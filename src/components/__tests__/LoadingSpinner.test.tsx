import { render } from '@testing-library/react';
import LoadingSpinner from '../LoadingSpinner';

describe('LoadingSpinner', () => {
    it('renders with default props', () => {
        const { container } = render(<LoadingSpinner />);
        
        const spinner = container.querySelector('svg');
        expect(spinner).toBeInTheDocument();
        expect(spinner).toHaveAttribute('role', 'status');
        expect(spinner).toHaveAttribute('aria-label', 'Loading');
    });

    it('applies correct size classes', () => {
        const { container: smallContainer } = render(<LoadingSpinner size="sm" />);
        const { container: mediumContainer } = render(<LoadingSpinner size="md" />);
        const { container: largeContainer } = render(<LoadingSpinner size="lg" />);
        const { container: xlContainer } = render(<LoadingSpinner size="xl" />);
        
        expect(smallContainer.querySelector('svg')).toHaveClass('w-4', 'h-4');
        expect(mediumContainer.querySelector('svg')).toHaveClass('w-6', 'h-6');
        expect(largeContainer.querySelector('svg')).toHaveClass('w-8', 'h-8');
        expect(xlContainer.querySelector('svg')).toHaveClass('w-12', 'h-12');
    });

    it('applies correct color classes', () => {
        const { container: blueContainer } = render(<LoadingSpinner color="blue" />);
        const { container: whiteContainer } = render(<LoadingSpinner color="white" />);
        const { container: grayContainer } = render(<LoadingSpinner color="gray" />);
        const { container: greenContainer } = render(<LoadingSpinner color="green" />);
        const { container: redContainer } = render(<LoadingSpinner color="red" />);
        
        expect(blueContainer.querySelector('svg')).toHaveClass('text-blue-600');
        expect(whiteContainer.querySelector('svg')).toHaveClass('text-white');
        expect(grayContainer.querySelector('svg')).toHaveClass('text-gray-600');
        expect(greenContainer.querySelector('svg')).toHaveClass('text-green-600');
        expect(redContainer.querySelector('svg')).toHaveClass('text-red-600');
    });

    it('applies custom className', () => {
        const { container } = render(<LoadingSpinner className="custom-class" />);
        
        const spinner = container.querySelector('svg');
        expect(spinner).toHaveClass('custom-class');
    });

    it('has animation class', () => {
        const { container } = render(<LoadingSpinner />);
        
        const spinner = container.querySelector('svg');
        expect(spinner).toHaveClass('animate-spin');
    });

    it('has correct SVG structure', () => {
        const { container } = render(<LoadingSpinner />);
        
        const spinner = container.querySelector('svg');
        const circle = spinner?.querySelector('circle');
        const path = spinner?.querySelector('path');
        
        expect(spinner).toHaveAttribute('viewBox', '0 0 24 24');
        expect(spinner).toHaveAttribute('fill', 'none');
        expect(circle).toBeInTheDocument();
        expect(path).toBeInTheDocument();
    });

    it('uses default size and color when not specified', () => {
        const { container } = render(<LoadingSpinner />);
        
        const spinner = container.querySelector('svg');
        expect(spinner).toHaveClass('w-6', 'h-6'); // Default md size
        expect(spinner).toHaveClass('text-blue-600'); // Default blue color
    });

    it('combines multiple classes correctly', () => {
        const { container } = render(
            <LoadingSpinner size="lg" color="red" className="extra-class" />
        );
        
        const spinner = container.querySelector('svg');
        expect(spinner).toHaveClass('w-8', 'h-8', 'text-red-600', 'extra-class', 'animate-spin');
    });
});
