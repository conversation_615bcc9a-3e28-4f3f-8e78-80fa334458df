import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FeatureGate from '../FeatureGate';
import { useFeatureAccess } from '../../hooks/useFeatureAccess';

// Mock the useFeatureAccess hook
jest.mock('../../hooks/useFeatureAccess');

const mockUseFeatureAccess = useFeatureAccess as jest.MockedFunction<typeof useFeatureAccess>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('FeatureGate', () => {
  const TestContent = () => <div data-testid="protected-content">Protected Content</div>;
  const TestFallback = () => <div data-testid="fallback-content">Upgrade Required</div>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('feature access control', () => {
    it('should render children when user has access', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      render(
        <FeatureGate feature="CONTACTS" action="ADD">
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('fallback-content')).not.toBeInTheDocument();
    });

    it('should render fallback when user lacks access', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(false),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      render(
        <FeatureGate 
          feature="ANALYTICS" 
          action="ADVANCED"
          fallback={<TestFallback />}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    });

    it('should render default upgrade message when no fallback provided', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(false),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      render(
        <FeatureGate feature="MESSAGING" action="BULK">
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByText(/upgrade your plan/i)).toBeInTheDocument();
    });

    it('should call hasAccess with correct parameters', () => {
      const mockHasAccess = jest.fn().mockReturnValue(true);
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: mockHasAccess,
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      render(
        <FeatureGate feature="GIFTS" action="ADVANCED">
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(mockHasAccess).toHaveBeenCalledWith('GIFTS', 'ADVANCED');
    });
  });

  describe('usage limit checking', () => {
    it('should render children when within usage limit', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: jest.fn().mockReturnValue(true),
      } as any);

      render(
        <FeatureGate 
          feature="CONTACTS" 
          action="ADD"
          usageKey="contacts"
          currentUsage={5}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should render fallback when usage limit exceeded', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: jest.fn().mockReturnValue(false),
      } as any);

      render(
        <FeatureGate 
          feature="CONTACTS" 
          action="ADD"
          usageKey="contacts"
          currentUsage={15}
          fallback={<TestFallback />}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    });

    it('should call isWithinUsageLimit with correct parameters', () => {
      const mockIsWithinUsageLimit = jest.fn().mockReturnValue(true);
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: mockIsWithinUsageLimit,
      } as any);

      render(
        <FeatureGate 
          feature="REMINDERS" 
          action="CREATE"
          usageKey="reminders"
          currentUsage={3}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(mockIsWithinUsageLimit).toHaveBeenCalledWith('reminders', 3);
    });

    it('should skip usage check when no usageKey provided', () => {
      const mockIsWithinUsageLimit = jest.fn();
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: mockIsWithinUsageLimit,
      } as any);

      render(
        <FeatureGate feature="ANALYTICS" action="BASIC">
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(mockIsWithinUsageLimit).not.toHaveBeenCalled();
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('combined access and usage checks', () => {
    it('should require both feature access and usage limit compliance', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(false),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: jest.fn().mockReturnValue(true),
      } as any);

      render(
        <FeatureGate 
          feature="MESSAGING" 
          action="BULK"
          usageKey="messages"
          currentUsage={5}
          fallback={<TestFallback />}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    });

    it('should deny access when feature allowed but usage exceeded', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: jest.fn().mockReturnValue(false),
      } as any);

      render(
        <FeatureGate 
          feature="CONTACTS" 
          action="ADD"
          usageKey="contacts"
          currentUsage={12}
          fallback={<TestFallback />}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle undefined currentUsage gracefully', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(10),
        isWithinUsageLimit: jest.fn().mockReturnValue(true),
      } as any);

      render(
        <FeatureGate 
          feature="CONTACTS" 
          action="ADD"
          usageKey="contacts"
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should handle unlimited usage (-1 limit)', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn().mockReturnValue(-1),
        isWithinUsageLimit: jest.fn().mockReturnValue(true),
      } as any);

      render(
        <FeatureGate 
          feature="CONTACTS" 
          action="ADD"
          usageKey="contacts"
          currentUsage={1000}
        >
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should render nothing when children is null', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(true),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      const { container } = render(
        <FeatureGate feature="CONTACTS" action="ADD">
          {null}
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('accessibility', () => {
    it('should maintain proper ARIA attributes in fallback content', () => {
      mockUseFeatureAccess.mockReturnValue({
        hasAccess: jest.fn().mockReturnValue(false),
        getUsageLimit: jest.fn(),
        isWithinUsageLimit: jest.fn(),
      } as any);

      render(
        <FeatureGate feature="ANALYTICS" action="ADVANCED">
          <TestContent />
        </FeatureGate>,
        { wrapper: createWrapper() }
      );

      const upgradeMessage = screen.getByText(/upgrade your plan/i);
      expect(upgradeMessage).toBeInTheDocument();
    });
  });
});
