import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import SignupForm from "../SignupForm";
import { AuthProvider } from "../../context/AuthContext";
import { MemoryRouter } from "react-router-dom";

describe("SignupForm", () => {
    test("renders all inputs and button", () => {
        render(
            <MemoryRouter>
                <AuthProvider>
                    <SignupForm />
                </AuthProvider>
            </MemoryRouter>
        );

        expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument();
        expect(screen.getByPlaceholderText(/^password$/i)).toBeInTheDocument();
        expect(screen.getByPlaceholderText(/confirm password/i)).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /sign up/i })).toBeInTheDocument();
    });

    test("shows error if passwords do not match", () => {
        render(
            <MemoryRouter>
                <AuthProvider>
                    <SignupForm />
                </AuthProvider>
            </MemoryRouter>
        );

        fireEvent.change(screen.getByPlaceholderText(/email/i), {
            target: { value: "<EMAIL>" },
        });
        fireEvent.change(screen.getByPlaceholderText(/^password$/i), {
            target: { value: "password1" },
        });
        fireEvent.change(screen.getByPlaceholderText(/confirm password/i), {
            target: { value: "password2" },
        });
        fireEvent.click(screen.getByRole("button", { name: /sign up/i }));

        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });

    // Add more tests as needed...
});
