import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ContactList from '../ContactList';
import { useContacts } from '../../hooks/useContacts';

// Mock dependencies
jest.mock('../../hooks/useContacts');
jest.mock('../../context/AuthContext', () => ({
  useAuth: () => ({
    user: { email: '<EMAIL>', uid: 'test-uid' },
    loading: false,
  }),
}));

const mockUseContacts = useContacts as jest.MockedFunction<typeof useContacts>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('ContactList', () => {
  const mockContacts = [
    {
      id: '1',
      name: '<PERSON>',
      birthday: '1990-05-15',
      category: 'Family',
      email: '<EMAIL>',
      phone: '+**********',
      user_email: '<EMAIL>',
    },
    {
      id: '2',
      name: 'Bob Smith',
      birthday: '1985-08-22',
      category: 'Friend',
      email: '<EMAIL>',
      phone: '+**********',
      user_email: '<EMAIL>',
    },
    {
      id: '3',
      name: 'Charlie Brown',
      birthday: '1992-12-03',
      category: 'Work',
      email: '<EMAIL>',
      user_email: '<EMAIL>',
    },
  ];

  const mockMutations = {
    updateContact: jest.fn(),
    deleteContact: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseContacts.mockReturnValue({
      data: mockContacts,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      ...mockMutations,
    } as any);
  });

  describe('basic rendering', () => {
    it('should render contact list with all contacts', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      expect(screen.getByText('Charlie Brown')).toBeInTheDocument();
    });

    it('should display contact details', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('+**********')).toBeInTheDocument();
      expect(screen.getByText('Family')).toBeInTheDocument();
    });

    it('should render empty state when no contacts', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        ...mockMutations,
      } as any);

      render(<ContactList />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/no contacts found/i)).toBeInTheDocument();
    });
  });

  describe('loading and error states', () => {
    it('should show loading state', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        refetch: jest.fn(),
        ...mockMutations,
      } as any);

      render(<ContactList />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should show error state', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load contacts'),
        refetch: jest.fn(),
        ...mockMutations,
      } as any);

      render(<ContactList />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/error loading contacts/i)).toBeInTheDocument();
    });

    it('should show retry button on error', () => {
      const mockRefetch = jest.fn();
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load contacts'),
        refetch: mockRefetch,
        ...mockMutations,
      } as any);

      render(<ContactList />, { wrapper: createWrapper() });
      
      const retryButton = screen.getByText(/try again/i);
      fireEvent.click(retryButton);
      
      expect(mockRefetch).toHaveBeenCalled();
    });
  });

  describe('search functionality', () => {
    it('should filter contacts by name', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      fireEvent.change(searchInput, { target: { value: 'Alice' } });
      
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.queryByText('Bob Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Charlie Brown')).not.toBeInTheDocument();
    });

    it('should filter contacts by email', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      fireEvent.change(searchInput, { target: { value: '<EMAIL>' } });
      
      expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });

    it('should filter contacts by category', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      fireEvent.change(searchInput, { target: { value: 'Work' } });
      
      expect(screen.getByText('Charlie Brown')).toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });

    it('should show no results message when search yields no matches', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } });
      
      expect(screen.getByText(/no contacts match your search/i)).toBeInTheDocument();
    });

    it('should clear search when input is cleared', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      fireEvent.change(searchInput, { target: { value: 'Alice' } });
      fireEvent.change(searchInput, { target: { value: '' } });
      
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      expect(screen.getByText('Charlie Brown')).toBeInTheDocument();
    });
  });

  describe('sorting functionality', () => {
    it('should sort contacts by name (default)', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const contactNames = screen.getAllByTestId(/contact-name/);
      expect(contactNames[0]).toHaveTextContent('Alice Johnson');
      expect(contactNames[1]).toHaveTextContent('Bob Smith');
      expect(contactNames[2]).toHaveTextContent('Charlie Brown');
    });

    it('should sort contacts by birthday when selected', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const sortSelect = screen.getByLabelText(/sort by/i);
      fireEvent.change(sortSelect, { target: { value: 'birthday' } });
      
      // Should be sorted by birthday (Bob: 1985, Alice: 1990, Charlie: 1992)
      const contactNames = screen.getAllByTestId(/contact-name/);
      expect(contactNames[0]).toHaveTextContent('Bob Smith');
      expect(contactNames[1]).toHaveTextContent('Alice Johnson');
      expect(contactNames[2]).toHaveTextContent('Charlie Brown');
    });

    it('should sort contacts by category when selected', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const sortSelect = screen.getByLabelText(/sort by/i);
      fireEvent.change(sortSelect, { target: { value: 'category' } });
      
      // Should be sorted by category (Family, Friend, Work)
      const contactNames = screen.getAllByTestId(/contact-name/);
      expect(contactNames[0]).toHaveTextContent('Alice Johnson'); // Family
      expect(contactNames[1]).toHaveTextContent('Bob Smith'); // Friend
      expect(contactNames[2]).toHaveTextContent('Charlie Brown'); // Work
    });
  });

  describe('contact actions', () => {
    it('should show edit button for each contact', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const editButtons = screen.getAllByText(/edit/i);
      expect(editButtons).toHaveLength(3);
    });

    it('should show delete button for each contact', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const deleteButtons = screen.getAllByText(/delete/i);
      expect(deleteButtons).toHaveLength(3);
    });

    it('should call onEdit when edit button is clicked', () => {
      const mockOnEdit = jest.fn();
      render(<ContactList onEdit={mockOnEdit} />, { wrapper: createWrapper() });
      
      const editButtons = screen.getAllByText(/edit/i);
      fireEvent.click(editButtons[0]);
      
      expect(mockOnEdit).toHaveBeenCalledWith(mockContacts[0]);
    });

    it('should show delete confirmation dialog', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const deleteButtons = screen.getAllByText(/delete/i);
      fireEvent.click(deleteButtons[0]);
      
      expect(screen.getByText(/are you sure/i)).toBeInTheDocument();
      expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument();
    });

    it('should delete contact when confirmed', async () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const deleteButtons = screen.getAllByText(/delete/i);
      fireEvent.click(deleteButtons[0]);
      
      const confirmButton = screen.getByText(/confirm/i);
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(mockMutations.deleteContact).toHaveBeenCalledWith('1');
      });
    });

    it('should cancel delete when cancelled', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const deleteButtons = screen.getAllByText(/delete/i);
      fireEvent.click(deleteButtons[0]);
      
      const cancelButton = screen.getByText(/cancel/i);
      fireEvent.click(cancelButton);
      
      expect(screen.queryByText(/are you sure/i)).not.toBeInTheDocument();
      expect(mockMutations.deleteContact).not.toHaveBeenCalled();
    });
  });

  describe('responsive design', () => {
    it('should render mobile-friendly layout', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const container = screen.getByTestId('contact-list-container');
      expect(container).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
    });

    it('should show compact view on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<ContactList />, { wrapper: createWrapper() });
      
      const contactCards = screen.getAllByTestId(/contact-card/);
      contactCards.forEach(card => {
        expect(card).toHaveClass('p-4');
      });
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByLabelText(/search contacts/i);
      expect(searchInput).toBeInTheDocument();
      
      const sortSelect = screen.getByLabelText(/sort by/i);
      expect(sortSelect).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      
      expect(editButtons).toHaveLength(3);
      expect(deleteButtons).toHaveLength(3);
    });

    it('should support keyboard navigation', () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      searchInput.focus();
      
      expect(document.activeElement).toBe(searchInput);
    });
  });

  describe('performance', () => {
    it('should handle large contact lists efficiently', () => {
      const largeContactList = Array.from({ length: 100 }, (_, i) => ({
        id: `contact-${i}`,
        name: `Contact ${i}`,
        birthday: '1990-01-01',
        category: 'Friend',
        email: `contact${i}@example.com`,
        user_email: '<EMAIL>',
      }));

      mockUseContacts.mockReturnValue({
        data: largeContactList,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        ...mockMutations,
      } as any);

      const startTime = performance.now();
      render(<ContactList />, { wrapper: createWrapper() });
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // Should render in less than 1 second
    });

    it('should debounce search input', async () => {
      render(<ContactList />, { wrapper: createWrapper() });
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      
      fireEvent.change(searchInput, { target: { value: 'A' } });
      fireEvent.change(searchInput, { target: { value: 'Al' } });
      fireEvent.change(searchInput, { target: { value: 'Ali' } });
      
      // Should only filter once after debounce
      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      });
    });
  });
});
