import { render } from '@testing-library/react';
import ErrorBoundary from '../ErrorBoundary';

// Component that throws an error
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
    if (shouldThrow) {
        throw new Error('Test error');
    }
    return <div>No error</div>;
};

// Component with custom fallback
const CustomFallback = () => <div>Custom error fallback</div>;

describe('ErrorBoundary', () => {
    beforeEach(() => {
        // Mock console.error to avoid noise in test output
        jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('renders children when there is no error', () => {
        const { getByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={false} />
            </ErrorBoundary>
        );
        
        expect(getByText('No error')).toBeInTheDocument();
    });

    it('renders default error UI when error occurs', () => {
        const { getByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(getByText('Oops! Something went wrong')).toBeInTheDocument();
        expect(getByText(/We're sorry, but something unexpected happened/)).toBeInTheDocument();
        expect(getByText('Try Again')).toBeInTheDocument();
        expect(getByText('Go Home')).toBeInTheDocument();
    });

    it('renders custom fallback when provided', () => {
        const { getByText } = render(
            <ErrorBoundary fallback={<CustomFallback />}>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(getByText('Custom error fallback')).toBeInTheDocument();
    });

    it('shows error details in development mode', () => {
        // Mock NODE_ENV to be development
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'development';
        
        const { getByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(getByText('Error Details (Development)')).toBeInTheDocument();
        
        // Restore original NODE_ENV
        process.env.NODE_ENV = originalEnv;
    });

    it('does not show error details in production mode', () => {
        // Mock NODE_ENV to be production
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'production';
        
        const { queryByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(queryByText('Error Details (Development)')).not.toBeInTheDocument();
        
        // Restore original NODE_ENV
        process.env.NODE_ENV = originalEnv;
    });

    it('has retry functionality', () => {
        const { getByText, queryByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        // Should show error UI
        expect(getByText('Oops! Something went wrong')).toBeInTheDocument();
        
        // Click retry button
        const retryButton = getByText('Try Again');
        retryButton.click();
        
        // Error UI should be gone (component will re-render)
        // Note: In a real scenario, the component might still throw an error
        // This test just verifies the retry button exists and is clickable
        expect(retryButton).toBeInTheDocument();
    });

    it('has go home functionality', () => {
        // Mock window.location.href
        const originalLocation = window.location;
        delete (window as any).location;
        window.location = { ...originalLocation, href: '' };
        
        const { getByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        const goHomeButton = getByText('Go Home');
        goHomeButton.click();
        
        expect(window.location.href).toBe('/');
        
        // Restore original location
        window.location = originalLocation;
    });

    it('logs error to console', () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        
        render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(consoleSpy).toHaveBeenCalledWith(
            'Error caught by boundary:',
            expect.any(Error),
            expect.any(Object)
        );
        
        consoleSpy.mockRestore();
    });

    it('has proper accessibility attributes', () => {
        const { container } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        const errorIcon = container.querySelector('svg');
        expect(errorIcon).toBeInTheDocument();
        
        const buttons = container.querySelectorAll('button');
        expect(buttons).toHaveLength(2); // Try Again and Go Home buttons
    });

    it('handles error info correctly', () => {
        // This test verifies that the component can handle error info
        // The actual error info testing would require more complex setup
        const { getByText } = render(
            <ErrorBoundary>
                <ThrowError shouldThrow={true} />
            </ErrorBoundary>
        );
        
        expect(getByText('Oops! Something went wrong')).toBeInTheDocument();
    });
});
