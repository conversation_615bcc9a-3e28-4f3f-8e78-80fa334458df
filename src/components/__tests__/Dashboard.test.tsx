import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Dashboard from '../Dashboard';
import { useAuth } from '../../context/AuthContext';
import { useContacts } from '../../hooks/useContacts';

// Mock dependencies
jest.mock('../../context/AuthContext');
jest.mock('../../hooks/useContacts');
jest.mock('../QuickStats', () => {
  return function MockQuickStats() {
    return <div data-testid="quick-stats">Quick Stats</div>;
  };
});
jest.mock('../TodaysBirthdays', () => {
  return function MockTodaysBirthdays() {
    return <div data-testid="todays-birthdays">Today's Birthdays</div>;
  };
});

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseContacts = useContacts as jest.MockedFunction<typeof useContacts>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('Dashboard', () => {
  const mockUser = {
    email: '<EMAIL>',
    uid: 'test-uid',
    displayName: 'Test User',
  };

  const mockContacts = [
    {
      id: '1',
      name: 'John Doe',
      birthday: '1990-05-15',
      category: 'Family',
      user_email: '<EMAIL>',
    },
    {
      id: '2',
      name: 'Jane Smith',
      birthday: '1985-08-22',
      category: 'Friend',
      user_email: '<EMAIL>',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      user: mockUser,
      loading: false,
      signup: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
    } as any);

    mockUseContacts.mockReturnValue({
      data: mockContacts,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);
  });

  describe('basic rendering', () => {
    it('should render dashboard with user greeting', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
      expect(screen.getByText('Test User')).toBeInTheDocument();
    });

    it('should render dashboard with email when no display name', () => {
      mockUseAuth.mockReturnValue({
        user: { ...mockUser, displayName: null },
        loading: false,
        signup: jest.fn(),
        login: jest.fn(),
        logout: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should render quick stats component', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByTestId('quick-stats')).toBeInTheDocument();
    });

    it('should render today\'s birthdays component', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByTestId('todays-birthdays')).toBeInTheDocument();
    });
  });

  describe('navigation', () => {
    it('should render navigation buttons', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/add contact/i)).toBeInTheDocument();
      expect(screen.getByText(/view all contacts/i)).toBeInTheDocument();
      expect(screen.getByText(/calendar/i)).toBeInTheDocument();
      expect(screen.getByText(/reminders/i)).toBeInTheDocument();
    });

    it('should call onNavigate when navigation buttons are clicked', () => {
      const mockOnNavigate = jest.fn();
      render(<Dashboard onNavigate={mockOnNavigate} />, { wrapper: createWrapper() });
      
      fireEvent.click(screen.getByText(/add contact/i));
      expect(mockOnNavigate).toHaveBeenCalledWith('add-contacts');

      fireEvent.click(screen.getByText(/view all contacts/i));
      expect(mockOnNavigate).toHaveBeenCalledWith('contacts');

      fireEvent.click(screen.getByText(/calendar/i));
      expect(mockOnNavigate).toHaveBeenCalledWith('calendar');

      fireEvent.click(screen.getByText(/reminders/i));
      expect(mockOnNavigate).toHaveBeenCalledWith('reminders');
    });

    it('should handle navigation without onNavigate prop', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(() => {
        fireEvent.click(screen.getByText(/add contact/i));
      }).not.toThrow();
    });
  });

  describe('loading states', () => {
    it('should show loading state when user is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: true,
        signup: jest.fn(),
        login: jest.fn(),
        logout: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should show loading state when contacts are loading', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        refetch: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });
  });

  describe('error states', () => {
    it('should handle missing user gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        signup: jest.fn(),
        login: jest.fn(),
        logout: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/welcome/i)).toBeInTheDocument();
    });

    it('should handle contacts error gracefully', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load contacts'),
        refetch: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText(/error loading contacts/i)).toBeInTheDocument();
    });
  });

  describe('responsive design', () => {
    it('should render mobile-friendly layout', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      const container = screen.getByRole('main');
      expect(container).toHaveClass('p-4', 'md:p-6');
    });

    it('should render grid layout for navigation cards', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      const navigationGrid = screen.getByTestId('navigation-grid');
      expect(navigationGrid).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4');
    });
  });

  describe('accessibility', () => {
    it('should have proper heading structure', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toBeInTheDocument();
      expect(mainHeading).toHaveTextContent(/welcome back/i);
    });

    it('should have proper button roles', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
      
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });
    });

    it('should have proper ARIA labels for navigation', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      const addContactButton = screen.getByLabelText(/add new contact/i);
      expect(addContactButton).toBeInTheDocument();
    });
  });

  describe('data integration', () => {
    it('should display contact count in quick stats', () => {
      render(<Dashboard />, { wrapper: createWrapper() });
      
      // Quick stats should receive contact data
      expect(screen.getByTestId('quick-stats')).toBeInTheDocument();
    });

    it('should handle empty contacts list', () => {
      mockUseContacts.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: jest.fn(),
      } as any);

      render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByTestId('quick-stats')).toBeInTheDocument();
      expect(screen.getByTestId('todays-birthdays')).toBeInTheDocument();
    });
  });

  describe('performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = render(<Dashboard />, { wrapper: createWrapper() });
      
      const initialHeading = screen.getByRole('heading', { level: 1 });
      
      rerender(<Dashboard />);
      
      const newHeading = screen.getByRole('heading', { level: 1 });
      expect(newHeading).toBe(initialHeading);
    });

    it('should update when user data changes', () => {
      const { rerender } = render(<Dashboard />, { wrapper: createWrapper() });
      
      expect(screen.getByText('Test User')).toBeInTheDocument();
      
      mockUseAuth.mockReturnValue({
        user: { ...mockUser, displayName: 'Updated User' },
        loading: false,
        signup: jest.fn(),
        login: jest.fn(),
        logout: jest.fn(),
      } as any);
      
      rerender(<Dashboard />);
      
      expect(screen.getByText('Updated User')).toBeInTheDocument();
    });
  });

  describe('integration', () => {
    it('should work with real query client', async () => {
      const realQueryClient = new QueryClient();
      
      render(
        <QueryClientProvider client={realQueryClient}>
          <Dashboard />
        </QueryClientProvider>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
      });
    });

    it('should handle rapid navigation clicks', () => {
      const mockOnNavigate = jest.fn();
      render(<Dashboard onNavigate={mockOnNavigate} />, { wrapper: createWrapper() });
      
      const addContactButton = screen.getByText(/add contact/i);
      
      fireEvent.click(addContactButton);
      fireEvent.click(addContactButton);
      fireEvent.click(addContactButton);
      
      expect(mockOnNavigate).toHaveBeenCalledTimes(3);
    });
  });
});
