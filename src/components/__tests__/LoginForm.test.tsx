import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import LoginForm from '../LoginForm';
import { AuthProvider } from '../../context/AuthContext';
import { ErrorProvider } from '../../context/ErrorContext';

// Mock navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockNavigate,
}));

const renderLoginForm = () => {
    return render(
        <BrowserRouter>
            <ErrorProvider>
                <AuthProvider>
                    <LoginForm />
                </AuthProvider>
            </ErrorProvider>
        </BrowserRouter>
    );
};

describe('LoginForm', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        localStorage.clear();
        sessionStorage.clear();
    });

    it('renders login form with all elements', () => {
        const { getByText, getByLabelText, getByRole } = renderLoginForm();

        expect(getByText('Log In')).toBeInTheDocument();
        expect(getByLabelText(/email/i)).toBeInTheDocument();
        expect(getByLabelText(/password/i)).toBeInTheDocument();
        expect(getByRole('button', { name: /log in/i })).toBeInTheDocument();
    });

    it('renders form fields with correct attributes', () => {
        const { getByLabelText } = renderLoginForm();

        const emailInput = getByLabelText(/email/i) as HTMLInputElement;
        const passwordInput = getByLabelText(/password/i) as HTMLInputElement;

        expect(emailInput.type).toBe('email');
        expect(passwordInput.type).toBe('password');
        expect(emailInput.required).toBe(true);
        expect(passwordInput.required).toBe(true);
    });

    it('has Google login button', () => {
        const { getByText } = renderLoginForm();

        expect(getByText(/sign in with google/i)).toBeInTheDocument();
    });

    it('has link to signup page', () => {
        const { getByText } = renderLoginForm();

        expect(getByText(/don't have an account/i)).toBeInTheDocument();
        expect(getByText(/sign up/i)).toBeInTheDocument();
    });

    it('disables submit button initially', () => {
        const { getByRole } = renderLoginForm();

        const submitButton = getByRole('button', { name: /log in/i });

        // Button should be disabled initially (form is invalid)
        expect(submitButton).toBeDisabled();
    });
});
