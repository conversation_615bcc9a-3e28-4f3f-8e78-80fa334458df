import React, { useState, useMemo } from 'react';
import { useContactsQuery } from '../hooks/useContacts';
import { useTemplatesQuery, useCreateTemplate, useUpdateTemplate, useCleanupStorage, useResetTemplates } from '../hooks/useMessaging';
import EditTemplateForm from './EditTemplateForm';
import TemplatePreview from './TemplatePreview';

// Anniversary type categories for grouping
const ANNIVERSARY_CATEGORIES = {
  wedding: { name: 'Wedding', icon: '💒', color: 'pink' },
  dating: { name: 'Dating', icon: '💕', color: 'rose' },
  business: { name: 'Business', icon: '🏢', color: 'blue' },
  engagement: { name: 'Engagement', icon: '💍', color: 'purple' },
  other: { name: 'Other', icon: '🎉', color: 'gray' }
} as const;

type AnniversaryCategory = keyof typeof ANNIVERSARY_CATEGORIES;

const AnniversaryMessagingSystem: React.FC = () => {
  const { data: contacts = [] } = useContactsQuery();
  const { data: allTemplates = [], isLoading: templatesLoading, error: templatesError } = useTemplatesQuery();
  const createTemplateMutation = useCreateTemplate();
  const updateTemplateMutation = useUpdateTemplate();
  const cleanupStorageMutation = useCleanupStorage();
  const resetTemplatesMutation = useResetTemplates();

  // Filter templates for anniversary type
  const templates = allTemplates.filter(template => template.type === 'anniversary');

  // Local UI state
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [activeCategory, setActiveCategory] = useState<AnniversaryCategory | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [templatesPerPage] = useState(12); // Increased to show more templates per page

  // Function to categorize templates based on their name
  const categorizeTemplate = (template: any): AnniversaryCategory => {
    const name = template.name.toLowerCase();
    if (name.includes('wedding') || name.includes('marriage')) return 'wedding';
    if (name.includes('dating') || name.includes('relationship')) return 'dating';
    if (name.includes('business') || name.includes('company') || name.includes('work')) return 'business';
    if (name.includes('engagement')) return 'engagement';
    return 'other';
  };

  // Group templates by category
  const groupedTemplates = useMemo(() => {
    const groups: Record<AnniversaryCategory, any[]> = {
      wedding: [],
      dating: [],
      business: [],
      engagement: [],
      other: []
    };

    templates.forEach(template => {
      const category = categorizeTemplate(template);
      groups[category].push(template);
    });

    return groups;
  }, [templates]);

  // Get filtered templates based on active category
  const filteredTemplates = useMemo(() => {
    if (activeCategory === 'all') {
      return templates;
    }
    return groupedTemplates[activeCategory] || [];
  }, [templates, groupedTemplates, activeCategory]);

  // Pagination logic
  const totalPages = Math.ceil(filteredTemplates.length / templatesPerPage);
  const startIndex = (currentPage - 1) * templatesPerPage;
  const endIndex = startIndex + templatesPerPage;
  const currentTemplates = filteredTemplates.slice(startIndex, endIndex);

  // Reset to page 1 when category changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeCategory]);

  // Get contacts with anniversaries
  const contactsWithAnniversaries = contacts.filter(contact =>
    contact.anniversaryDate && contact.anniversaryType
  );

  const upcomingAnniversaries = contactsWithAnniversaries.filter(contact => {
    if (!contact.anniversaryDate) return false;
    const today = new Date();
    const anniversary = new Date(contact.anniversaryDate);
    const thisYearAnniversary = new Date(today.getFullYear(), anniversary.getMonth(), anniversary.getDate());

    if (thisYearAnniversary < today) {
      thisYearAnniversary.setFullYear(today.getFullYear() + 1);
    }

    const daysUntil = Math.ceil((thisYearAnniversary.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntil <= 30;
  });

  const calculateYears = (anniversaryDate: string): number => {
    const today = new Date();
    const anniversary = new Date(anniversaryDate);
    return today.getFullYear() - anniversary.getFullYear();
  };

  const previewMessage = (template: AnniversaryTemplate, contact: any) => {
    const years = calculateYears(contact.anniversaryDate);
    return {
      subject: template.subject
        .replace('{name}', contact.name)
        .replace('{years}', years.toString()),
      message: template.message
        .replace(/{name}/g, contact.name)
        .replace(/{partnerName}/g, contact.partnerName || 'your partner')
        .replace(/{years}/g, years.toString())
    };
  };

  const toggleTemplate = async (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      try {
        await updateTemplateMutation.mutateAsync({
          id: templateId,
          updates: { isActive: !template.isActive }
        });
      } catch (error) {
        console.error('Failed to toggle template:', error);
      }
    }
  };

  const handleCleanupStorage = async () => {
    if (window.confirm('This will remove duplicate templates. Continue?')) {
      try {
        const result = await cleanupStorageMutation.mutateAsync();
        alert(`Cleanup complete! Removed ${result.removedDuplicates} duplicates. Total templates: ${result.totalTemplates}`);
      } catch (error) {
        console.error('Failed to cleanup storage:', error);
        alert('Failed to cleanup storage. Please try again.');
      }
    }
  };

  const handleResetTemplates = async () => {
    if (window.confirm('This will reset all templates to defaults and remove custom templates. This action cannot be undone. Continue?')) {
      try {
        await resetTemplatesMutation.mutateAsync();
        alert('Templates reset to defaults successfully!');
      } catch (error) {
        console.error('Failed to reset templates:', error);
        alert('Failed to reset templates. Please try again.');
      }
    }
  };

  const handleEditTemplate = (template: any) => {
    setSelectedTemplate(template);
    setIsEditing(true);
    setShowPreview(false);
  };

  const handlePreviewTemplate = (template: any) => {
    setSelectedTemplate(template);
    setShowPreview(true);
    setIsEditing(false);
  };

  const handleCloseModal = () => {
    setSelectedTemplate(null);
    setIsEditing(false);
    setShowPreview(false);
  };

  const handleSaveTemplate = async (updatedTemplate: any) => {
    try {
      await updateTemplateMutation.mutateAsync({
        id: selectedTemplate.id,
        updates: updatedTemplate
      });
      handleCloseModal();
    } catch (error) {
      console.error('Failed to save template:', error);
      alert('Failed to save template. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Loading State */}
      {templatesLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading anniversary templates...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {templatesError && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-2">
            <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Templates</h3>
          <p className="text-red-600 mb-4">There was an error loading anniversary templates. Please try again.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      )}

      {/* Content - only show when not loading and no errors */}
      {!templatesLoading && !templatesError && (
        <>
          {/* Header */}
          <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                  Anniversary Messaging System
                </h2>
                <p className="text-gray-600 mt-1">
                  Automated anniversary messages for weddings, dating, business, and more
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">{contactsWithAnniversaries.length}</div>
                  <div className="text-sm text-gray-500">Total Anniversaries</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-rose-600">{upcomingAnniversaries.length}</div>
                  <div className="text-sm text-gray-500">Upcoming (30 days)</div>
                </div>
              </div>
            </div>
          </div>

          {/* Upcoming Anniversaries */}
          {upcomingAnniversaries.length > 0 && (
            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="text-2xl mr-2">📅</span>
                Upcoming Anniversaries
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {upcomingAnniversaries.slice(0, 6).map(contact => {
                  const anniversary = new Date(contact.anniversaryDate!);
                  const thisYearAnniversary = new Date(new Date().getFullYear(), anniversary.getMonth(), anniversary.getDate());
                  const daysUntil = Math.ceil((thisYearAnniversary.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                  const years = calculateYears(contact.anniversaryDate!);

                  return (
                    <div key={contact.id} className="bg-gradient-to-r from-pink-50 to-rose-50 p-4 rounded-xl border border-pink-200">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{contact.name}</h4>
                        <span className="text-2xl">💕</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        {contact.anniversaryType} Anniversary
                      </p>
                      <p className="text-sm text-gray-600">
                        {contact.partnerName && `with ${contact.partnerName}`}
                      </p>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm font-medium text-pink-600">
                          {years} years
                        </span>
                        <span className="text-xs bg-pink-100 text-pink-700 px-2 py-1 rounded-full">
                          {daysUntil === 0 ? 'Today!' : `${daysUntil} days`}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Anniversary Templates */}
          <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <span className="text-2xl mr-2">📝</span>
                Anniversary Message Templates
                <span className="ml-2 text-sm text-gray-500">({filteredTemplates.length} templates)</span>
                {templates.length > 10 && (
                  <span className="ml-2 text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">
                    Many templates - consider cleanup
                  </span>
                )}
              </h3>
              <div className="flex items-center space-x-2">
                {templates.length > 10 && (
                  <button
                    onClick={handleCleanupStorage}
                    disabled={cleanupStorageMutation.isPending}
                    className="bg-yellow-500 text-white px-3 py-2 rounded-lg hover:bg-yellow-600 transition-colors text-sm font-medium disabled:opacity-50"
                  >
                    {cleanupStorageMutation.isPending ? 'Cleaning...' : 'Cleanup'}
                  </button>
                )}
                <button className="bg-gradient-to-r from-pink-600 to-rose-600 text-white px-4 py-2 rounded-xl hover:from-pink-700 hover:to-rose-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold">
                  + Add Template
                </button>
              </div>
            </div>

            {/* Category Filter Tabs */}
            <div className="flex flex-wrap gap-2 mb-6 p-1 bg-gray-100 rounded-xl">
              <button
                onClick={() => setActiveCategory('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeCategory === 'all'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }`}
              >
                All ({templates.length})
              </button>
              {Object.entries(ANNIVERSARY_CATEGORIES).map(([key, category]) => {
                const count = groupedTemplates[key as AnniversaryCategory]?.length || 0;
                if (count === 0) return null;

                return (
                  <button
                    key={key}
                    onClick={() => setActiveCategory(key as AnniversaryCategory)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${activeCategory === key
                      ? `bg-white text-gray-900 shadow-sm`
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }`}
                  >
                    <span>{category.icon}</span>
                    <span>{category.name} ({count})</span>
                  </button>
                );
              })}
            </div>

            {/* Templates Grid */}
            {currentTemplates.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {currentTemplates.map(template => {
                  const category = categorizeTemplate(template);
                  const categoryInfo = ANNIVERSARY_CATEGORIES[category];
                  const isDefault = template.id.startsWith('default_template_');

                  return (
                    <div key={template.id} className={`border rounded-xl p-4 hover:shadow-md transition-all duration-300 ${isDefault ? 'border-gray-200 bg-white' : 'border-blue-200 bg-blue-50/30'
                      }`}>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-semibold text-gray-900">{template.name}</h4>
                          <div className="flex items-center space-x-1">
                            <span className="text-sm">{categoryInfo.icon}</span>
                            <span className={`text-xs px-2 py-1 rounded-full ${categoryInfo.color === 'pink' ? 'bg-pink-100 text-pink-700' :
                              categoryInfo.color === 'rose' ? 'bg-rose-100 text-rose-700' :
                                categoryInfo.color === 'blue' ? 'bg-blue-100 text-blue-700' :
                                  categoryInfo.color === 'purple' ? 'bg-purple-100 text-purple-700' :
                                    'bg-gray-100 text-gray-700'
                              }`}>
                              {categoryInfo.name}
                            </span>
                            {!isDefault && (
                              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                Custom
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => toggleTemplate(template.id)}
                            className={`w-10 h-6 rounded-full transition-colors ${template.isActive ? 'bg-pink-600' : 'bg-gray-300'
                              }`}
                          >
                            <div className={`w-4 h-4 bg-white rounded-full transition-transform ${template.isActive ? 'translate-x-5' : 'translate-x-1'
                              }`} />
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div>
                          <span className="text-sm font-medium text-gray-600">Subject:</span>
                          <p className="text-sm text-gray-800">{template.subject}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Message:</span>
                          <p className="text-sm text-gray-800 line-clamp-3">{template.message}</p>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditTemplate(template)}
                          className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handlePreviewTemplate(template)}
                          className="flex-1 bg-pink-100 text-pink-700 px-3 py-2 rounded-lg hover:bg-pink-200 transition-colors text-sm font-medium"
                        >
                          Preview
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeCategory === 'all' ? 'No Templates Found' : `No ${ANNIVERSARY_CATEGORIES[activeCategory as AnniversaryCategory]?.name} Templates`}
                </h3>
                <p className="text-gray-600 mb-4">
                  {activeCategory === 'all'
                    ? 'Create your first anniversary template to get started.'
                    : `No templates found for ${ANNIVERSARY_CATEGORIES[activeCategory as AnniversaryCategory]?.name.toLowerCase()} anniversaries.`
                  }
                </p>
                <button className="bg-gradient-to-r from-pink-600 to-rose-600 text-white px-6 py-2 rounded-xl hover:from-pink-700 hover:to-rose-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold">
                  + Create Template
                </button>
              </div>
            )}

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  Showing {startIndex + 1}-{Math.min(endIndex, filteredTemplates.length)} of {filteredTemplates.length} templates
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    Previous
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`w-8 h-8 rounded-lg text-sm font-medium transition-colors ${currentPage === page
                          ? 'bg-pink-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Settings */}
          <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">⚙️</span>
              Anniversary Automation Settings
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Send Time
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
                    <option value="09:00">9:00 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="12:00">12:00 PM</option>
                    <option value="18:00">6:00 PM</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Days in Advance
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent">
                    <option value="0">On the day</option>
                    <option value="1">1 day before</option>
                    <option value="3">3 days before</option>
                    <option value="7">1 week before</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded border-gray-300 text-pink-600 focus:ring-pink-500" defaultChecked />
                    <span className="text-sm text-gray-700">Enable anniversary reminders</span>
                  </label>
                </div>

                <div>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded border-gray-300 text-pink-600 focus:ring-pink-500" defaultChecked />
                    <span className="text-sm text-gray-700">Include milestone anniversaries (5, 10, 25, 50 years)</span>
                  </label>
                </div>

                <div>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded border-gray-300 text-pink-600 focus:ring-pink-500" />
                    <span className="text-sm text-gray-700">Send admin notifications for upcoming anniversaries</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button className="bg-gradient-to-r from-pink-600 to-rose-600 text-white px-6 py-2 rounded-xl hover:from-pink-700 hover:to-rose-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold">
                Save Settings
              </button>
            </div>
          </div>

          {/* Admin Section - Only show if there are many templates */}
          {templates.length > 8 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold text-yellow-800 mb-4 flex items-center">
                <span className="text-2xl mr-2">🔧</span>
                Template Management
                <span className="ml-2 text-sm bg-yellow-200 text-yellow-700 px-2 py-1 rounded-full">
                  Admin
                </span>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-xl border border-yellow-200">
                  <h4 className="font-medium text-gray-900 mb-2">Clean Up Duplicates</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Remove duplicate templates and optimize storage. This will keep the most recent version of each template.
                  </p>
                  <button
                    onClick={handleCleanupStorage}
                    disabled={cleanupStorageMutation.isPending}
                    className="w-full bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors font-medium disabled:opacity-50"
                  >
                    {cleanupStorageMutation.isPending ? 'Cleaning Up...' : 'Clean Up Duplicates'}
                  </button>
                </div>

                <div className="bg-white p-4 rounded-xl border border-red-200">
                  <h4 className="font-medium text-gray-900 mb-2">Reset to Defaults</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Reset all templates to default settings. This will remove all custom templates permanently.
                  </p>
                  <button
                    onClick={handleResetTemplates}
                    disabled={resetTemplatesMutation.isPending}
                    className="w-full bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors font-medium disabled:opacity-50"
                  >
                    {resetTemplatesMutation.isPending ? 'Resetting...' : 'Reset to Defaults'}
                  </button>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-0.5">ℹ️</span>
                  <div className="text-sm text-blue-700">
                    <strong>Current Status:</strong> {templates.length} total templates,
                    {templates.filter(t => t.type === 'anniversary').length} anniversary templates.
                    {templates.length > 15 && (
                      <span className="block mt-1 font-medium">
                        ⚠️ High template count detected - cleanup recommended for better performance.
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Edit Template Modal */}
      {isEditing && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Edit Template</h3>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <EditTemplateForm
              template={selectedTemplate}
              onSave={handleSaveTemplate}
              onCancel={handleCloseModal}
              isLoading={updateTemplateMutation.isPending}
            />
          </div>
        </div>
      )}

      {/* Preview Template Modal */}
      {showPreview && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Preview Template</h3>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <TemplatePreview
              template={selectedTemplate}
              onClose={handleCloseModal}
              onEdit={() => handleEditTemplate(selectedTemplate)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AnniversaryMessagingSystem;
