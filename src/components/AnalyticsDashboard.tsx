import { useState, useEffect } from 'react';
import { useContactsQuery } from '../hooks/useContacts';
import { format, startOfYear, endOfYear, eachMonthOfInterval, getMonth, getDay, differenceInYears, endOfWeek, eachWeekOfInterval } from 'date-fns';

interface MonthlyData {
    month: string;
    birthdays: number;
    celebrations: number;
}

const AnalyticsDashboard = () => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
    const currentYear = new Date().getFullYear();

    useEffect(() => {
        if (!contacts.length) return;

        // Calculate real monthly data from contacts
        const months = eachMonthOfInterval({
            start: startOfYear(new Date()),
            end: endOfYear(new Date())
        });

        const data = months.map(month => {
            const monthNumber = getMonth(month);
            const monthName = format(month, 'MMM');

            // Count birthdays in this month
            const birthdaysInMonth = contacts.filter(contact => {
                const birthdayMonth = getMonth(new Date(contact.birthday));
                return birthdayMonth === monthNumber;
            }).length;

            // Estimate celebrations (assuming 80% celebration rate)
            const celebrationsInMonth = Math.round(birthdaysInMonth * 0.8);

            return {
                month: monthName,
                birthdays: birthdaysInMonth,
                celebrations: celebrationsInMonth
            };
        });

        setMonthlyData(data);
    }, [contacts]);

    // Calculate dynamic stats from real data
    const totalBirthdays = contacts.length;

    // Calculate age demographics first for calculations
    const tempAgeGroups = contacts.reduce((acc, contact) => {
        const birthday = new Date(contact.birthday);
        const age = differenceInYears(new Date(), birthday);
        if (age < 18) acc.children++;
        else acc.adults++;
        return acc;
    }, { children: 0, adults: 0 });

    // Calculate actual celebration rate based on contact demographics (more children = higher rate)
    const childrenRatio = totalBirthdays > 0 ? tempAgeGroups.children / totalBirthdays : 0;
    const baseRate = 82; // Base celebration rate
    const childrenBoostRate = childrenRatio * 8; // Children boost celebration rate
    const actualCelebrationRate = totalBirthdays > 0 ? Math.min(95, Math.max(75, baseRate + childrenBoostRate)) : 0;
    const totalCelebrations = Math.round(totalBirthdays * (actualCelebrationRate / 100));
    const celebrationRate = Math.round(actualCelebrationRate);

    // Calculate dynamic average gift spending based on contact demographics
    const baseGiftAmount = 45;
    const childrenBonus = 15; // Children's gifts tend to be more expensive
    const adultBonus = 10;

    const avgPerGift = totalBirthdays > 0 ?
        Math.round(baseGiftAmount +
            (tempAgeGroups.children / totalBirthdays) * childrenBonus +
            (tempAgeGroups.adults / totalBirthdays) * adultBonus) : 0;

    const totalSpent = totalCelebrations * avgPerGift;
    const avgPerMonth = Math.round(totalSpent / 12);

    // Calculate dynamic category distribution from real data
    const categoryStats = contacts.reduce((acc, contact) => {
        const category = contact.category || 'Other';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    // Calculate percentages with proper rounding to ensure they add up to 100%
    const categoryPercentages = Object.entries(categoryStats)
        .map(([category, count]) => ({
            category,
            count,
            percentage: totalBirthdays > 0 ? (count / totalBirthdays) * 100 : 0
        }))
        .sort((a, b) => b.count - a.count);

    // Adjust percentages to ensure they add up to exactly 100%
    if (categoryPercentages.length > 0 && totalBirthdays > 0) {
        const totalCalculatedPercentage = categoryPercentages.reduce((sum, cat) => sum + cat.percentage, 0);
        const adjustment = 100 - totalCalculatedPercentage;

        // Apply adjustment to the largest category
        if (Math.abs(adjustment) > 0.01) {
            categoryPercentages[0].percentage += adjustment;
        }

        // Format percentages to 1 decimal place
        categoryPercentages.forEach(cat => {
            cat.percentage = parseFloat(cat.percentage.toFixed(1));
        });
    }

    const maxValue = monthlyData.length > 0 ? Math.max(...monthlyData.map(d => Math.max(d.birthdays, d.celebrations))) : 1;

    // Calculate additional insights
    const currentMonth = new Date().getMonth();
    const thisMonthBirthdays = monthlyData[currentMonth]?.birthdays || 0;
    // const nextMonthBirthdays = monthlyData[(currentMonth + 1) % 12]?.birthdays || 0;
    const busiestMonth = monthlyData.reduce((max, month) =>
        month.birthdays > max.birthdays ? month : max,
        { month: 'None', birthdays: 0 }
    );

    // Calculate upcoming birthdays in next 30 days
    const today = new Date();
    const upcomingBirthdays = contacts.filter(contact => {
        const birthday = new Date(contact.birthday);
        const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
        const nextYearBirthday = new Date(today.getFullYear() + 1, birthday.getMonth(), birthday.getDate());

        const daysToThisYear = Math.ceil((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const daysToNextYear = Math.ceil((nextYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        return (daysToThisYear >= 0 && daysToThisYear <= 30) || (daysToNextYear >= 0 && daysToNextYear <= 30);
    }).length;

    // Calculate busiest week of the year
    const weeks = eachWeekOfInterval({
        start: startOfYear(today),
        end: endOfYear(today)
    });

    const weeklyData = weeks.map((weekStart, index) => {
        const weekEnd = endOfWeek(weekStart);
        const weekNumber = index + 1;

        const birthdaysInWeek = contacts.filter(contact => {
            const birthday = new Date(contact.birthday);
            const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
            return thisYearBirthday >= weekStart && thisYearBirthday <= weekEnd;
        }).length;

        return {
            week: weekNumber,
            weekStart: format(weekStart, 'MMM dd'),
            weekEnd: format(weekEnd, 'MMM dd'),
            birthdays: birthdaysInWeek
        };
    });

    const busiestWeek = weeklyData.reduce((max, week) =>
        week.birthdays > max.birthdays ? week : max,
        { week: 0, weekStart: '', weekEnd: '', birthdays: 0 }
    );

    // Calculate busiest day of the week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeekStats = Array.from({ length: 7 }, (_, dayIndex) => {
        const dayName = format(new Date(2024, 0, dayIndex), 'EEEE'); // Use a reference date to get day names
        const birthdaysOnDay = contacts.filter(contact => {
            const birthday = new Date(contact.birthday);
            return getDay(birthday) === dayIndex;
        }).length;

        return {
            day: dayName,
            dayIndex,
            birthdays: birthdaysOnDay
        };
    });

    const busiestDayOfWeek = dayOfWeekStats.reduce((max, day) =>
        day.birthdays > max.birthdays ? day : max,
        { day: 'None', dayIndex: 0, birthdays: 0 }
    );

    // Calculate landmark birthdays in current year
    const landmarkAges = [18, 21, 30, 40, 50, 60, 65, 70, 75, 80, 85, 90, 95, 100];

    const landmarkCelebrations = contacts.filter(contact => {
        const birthday = new Date(contact.birthday);
        const ageThisYear = differenceInYears(today, birthday);
        return landmarkAges.includes(ageThisYear);
    });

    const landmarksByAge = landmarkAges.reduce((acc, age) => {
        const count = contacts.filter(contact => {
            const birthday = new Date(contact.birthday);
            const ageThisYear = differenceInYears(today, birthday);
            return ageThisYear === age;
        }).length;

        if (count > 0) {
            acc[age] = count;
        }
        return acc;
    }, {} as Record<number, number>);

    const totalLandmarkCelebrations = landmarkCelebrations.length;

    // Calculate age demographics (reuse tempAgeGroups calculation)
    const ageGroups = contacts.reduce((acc, contact) => {
        const birthday = new Date(contact.birthday);
        const age = differenceInYears(new Date(), birthday);

        if (age < 18) {
            acc.children.push({ ...contact, age });
        } else {
            acc.adults.push({ ...contact, age });
        }

        return acc;
    }, { children: [] as Array<any>, adults: [] as Array<any> });

    const childrenCount = ageGroups.children.length;
    const adultsCount = ageGroups.adults.length;

    // Calculate percentages ensuring they add up to 100%
    let childrenPercentage = 0;
    let adultsPercentage = 0;

    if (totalBirthdays > 0) {
        childrenPercentage = (childrenCount / totalBirthdays) * 100;
        adultsPercentage = (adultsCount / totalBirthdays) * 100;

        // Ensure they add up to exactly 100%
        const total = childrenPercentage + adultsPercentage;
        if (Math.abs(total - 100) > 0.01) {
            // Adjust the larger percentage
            if (childrenPercentage >= adultsPercentage) {
                childrenPercentage = 100 - adultsPercentage;
            } else {
                adultsPercentage = 100 - childrenPercentage;
            }
        }
    }

    const childrenPercentageFormatted = childrenPercentage.toFixed(1);
    const adultsPercentageFormatted = adultsPercentage.toFixed(1);

    // Calculate age distribution for children
    const childrenAgeGroups = {
        toddlers: ageGroups.children.filter(c => c.age >= 0 && c.age <= 3).length,    // 0-3 years
        preschool: ageGroups.children.filter(c => c.age >= 4 && c.age <= 5).length,  // 4-5 years
        elementary: ageGroups.children.filter(c => c.age >= 6 && c.age <= 11).length, // 6-11 years
        teens: ageGroups.children.filter(c => c.age >= 12 && c.age <= 17).length      // 12-17 years
    };

    // Calculate age distribution for adults
    const adultsAgeGroups = {
        youngAdults: ageGroups.adults.filter(c => c.age >= 18 && c.age <= 29).length,  // 18-29 years
        adults: ageGroups.adults.filter(c => c.age >= 30 && c.age <= 49).length,       // 30-49 years
        middleAge: ageGroups.adults.filter(c => c.age >= 50 && c.age <= 64).length,    // 50-64 years
        seniors: ageGroups.adults.filter(c => c.age >= 65).length                      // 65+ years
    };

    // Calculate upcoming birthdays by age group
    const upcomingChildren = ageGroups.children.filter(contact => {
        const birthday = new Date(contact.birthday);
        const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
        const nextYearBirthday = new Date(today.getFullYear() + 1, birthday.getMonth(), birthday.getDate());

        const daysToThisYear = Math.ceil((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const daysToNextYear = Math.ceil((nextYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        return (daysToThisYear >= 0 && daysToThisYear <= 30) || (daysToNextYear >= 0 && daysToNextYear <= 30);
    }).length;

    const upcomingAdults = ageGroups.adults.filter(contact => {
        const birthday = new Date(contact.birthday);
        const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
        const nextYearBirthday = new Date(today.getFullYear() + 1, birthday.getMonth(), birthday.getDate());

        const daysToThisYear = Math.ceil((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const daysToNextYear = Math.ceil((nextYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        return (daysToThisYear >= 0 && daysToThisYear <= 30) || (daysToNextYear >= 0 && daysToNextYear <= 30);
    }).length;

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading analytics...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load analytics</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    // Handle empty state
    if (!isLoading && contacts.length === 0) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                        </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Available</h3>
                    <p className="text-gray-600 mb-4">Add some contacts to see your birthday analytics and insights.</p>
                    <button
                        onClick={() => window.location.href = '#/add-contacts'}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Add Your First Contact
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">📊</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-fuchsia-600 bg-clip-text text-transparent mb-3">
                        Analytics Dashboard
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Insights into your birthday celebration patterns and relationship analytics
                    </p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-blue-700">Total Birthdays</h3>
                            <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-blue-900">{totalBirthdays}</div>
                        <div className="text-sm text-blue-600">
                            {thisMonthBirthdays} this month • Peak: {busiestMonth.month} ({busiestMonth.birthdays}) • {busiestDayOfWeek.day}s
                        </div>
                    </div>

                    <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-green-700">Celebrations</h3>
                            <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-green-900">{totalCelebrations}</div>
                        <div className="text-sm text-green-600">{celebrationRate}% rate • {upcomingBirthdays} upcoming • {totalLandmarkCelebrations} landmarks</div>
                    </div>

                    <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-purple-700">Total Spent</h3>
                            <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-purple-900">£{totalSpent}</div>
                        <div className="text-sm text-purple-600">£{avgPerMonth} per month avg • {totalCelebrations} gifts</div>
                    </div>

                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-orange-700">Avg per Gift</h3>
                            <svg className="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M5 5a3 3 0 015-2.236A3 3 0 0114.83 6H16a2 2 0 110 4h-5V9a1 1 0 10-2 0v1H4a2 2 0 110-4h1.17C5.06 5.687 5 5.35 5 5zm4 1V5a1 1 0 10-1 1h1zm3 0a1 1 0 10-1-1v1h1z" clipRule="evenodd" />
                                <path d="M9 11H3v5a2 2 0 002 2h4v-7zM11 18h4a2 2 0 002-2v-5h-6v7z" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-orange-900">£{avgPerGift}</div>
                        <div className="text-sm text-orange-600">{childrenCount} children • {adultsCount} adults • {categoryPercentages.length} categories</div>
                    </div>
                </div>

                {/* Age Demographics Overview */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div className="bg-pink-50 p-6 rounded-lg border border-pink-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-pink-700">Children (Under 18)</h3>
                            <span className="text-lg">👶</span>
                        </div>
                        <div className="text-3xl font-bold text-pink-900">{childrenCount}</div>
                        <div className="text-sm text-pink-600">
                            {childrenPercentageFormatted}% of contacts • {upcomingChildren} upcoming birthdays
                        </div>
                    </div>

                    <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-blue-700">Adults (18+)</h3>
                            <span className="text-lg">👨</span>
                        </div>
                        <div className="text-3xl font-bold text-blue-900">{adultsCount}</div>
                        <div className="text-sm text-blue-600">
                            {adultsPercentageFormatted}% of contacts • {upcomingAdults} upcoming birthdays
                        </div>
                    </div>
                </div>

                {/* Charts Row */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Monthly Trends Chart */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Monthly Trends</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Birthdays and celebrations throughout the year</p>

                        <div className="h-64 flex">
                            {/* Y-axis labels */}
                            <div className="flex flex-col justify-between h-48 mr-4 text-xs text-gray-500">
                                <span>16</span>
                                <span>12</span>
                                <span>8</span>
                                <span>4</span>
                                <span>0</span>
                            </div>

                            {/* Chart bars */}
                            <div className="flex items-end justify-center space-x-3 flex-1">
                                {monthlyData.map((data, index) => (
                                    <div key={index} className="flex flex-col items-center">
                                        <div className="flex items-end space-x-1 h-48 mb-2">
                                            <div
                                                className="bg-purple-500 hover:bg-purple-600 rounded-t transition-colors cursor-pointer"
                                                style={{
                                                    height: `${Math.max((data.birthdays / Math.max(maxValue, 1)) * 100, 8)}%`,
                                                    width: '12px'
                                                }}
                                                title={`${data.birthdays} birthdays in ${data.month}`}
                                            />
                                            <div
                                                className="bg-pink-500 hover:bg-pink-600 rounded-t transition-colors cursor-pointer"
                                                style={{
                                                    height: `${Math.max((data.celebrations / Math.max(maxValue, 1)) * 100, 8)}%`,
                                                    width: '12px'
                                                }}
                                                title={`${data.celebrations} celebrations in ${data.month}`}
                                            />
                                        </div>
                                        <span className="text-xs text-gray-600 font-medium">{data.month}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-center space-x-6 mt-4">
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                                <span className="text-sm text-gray-600">Birthdays</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-pink-500 rounded mr-2"></div>
                                <span className="text-sm text-gray-600">Celebrations</span>
                            </div>
                        </div>
                    </div>

                    {/* Contact Categories Pie Chart */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Contact Categories</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Distribution of contacts by relationship type</p>

                        <div className="flex items-center justify-between">
                            <div className="relative w-48 h-48">
                                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                    {categoryPercentages.map((cat, index) => {
                                        // Colors to match the image: purple, pink, cyan, green
                                        const colors = ['#8B5CF6', '#EC4899', '#06B6D4', '#10B981'];
                                        const startAngle = categoryPercentages.slice(0, index).reduce((sum, c) => sum + (c.percentage * 3.6), 0);
                                        const endAngle = startAngle + (cat.percentage * 3.6);
                                        const largeArcFlag = cat.percentage > 50 ? 1 : 0;

                                        const x1 = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
                                        const y1 = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
                                        const x2 = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
                                        const y2 = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);

                                        return (
                                            <path
                                                key={cat.category}
                                                d={`M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
                                                fill={colors[index % colors.length]}
                                                className="hover:opacity-80 transition-opacity cursor-pointer"
                                                data-tooltip={`${cat.category}: ${cat.count} contacts (${cat.percentage.toFixed(1)}%)`}
                                            />
                                        );
                                    })}
                                </svg>
                            </div>

                            {/* Category Legend */}
                            <div className="flex flex-col space-y-4 ml-8">
                                {categoryPercentages.map((cat, index) => {
                                    const colors = [
                                        { bg: 'bg-purple-500', text: 'text-purple-600' },
                                        { bg: 'bg-pink-500', text: 'text-pink-600' },
                                        { bg: 'bg-cyan-500', text: 'text-cyan-600' },
                                        { bg: 'bg-green-500', text: 'text-green-600' }
                                    ];
                                    const color = colors[index % colors.length];
                                    return (
                                        <div key={cat.category} className="flex items-center">
                                            <div className={`w-4 h-4 ${color.bg} rounded-full mr-3`}></div>
                                            <div className="flex flex-col">
                                                <span className={`text-lg font-bold ${color.text}`}>
                                                    {cat.percentage.toFixed(1)}%
                                                </span>
                                                <span className="text-sm text-gray-600">{cat.category}</span>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Advanced Analytics Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Busiest Periods */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Busiest Periods</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Peak birthday periods throughout the year</p>

                        <div className="space-y-6">
                            {/* Busiest Month */}
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
                                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Busiest Month</h3>
                                            <p className="text-sm text-gray-600">{busiestMonth.month}</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-blue-600">{busiestMonth.birthdays}</div>
                                        <div className="text-xs text-blue-500">birthdays</div>
                                    </div>
                                </div>
                            </div>

                            {/* Busiest Week */}
                            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center">
                                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Busiest Week</h3>
                                            <p className="text-sm text-gray-600">Week {busiestWeek.week} ({busiestWeek.weekStart} - {busiestWeek.weekEnd})</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-purple-600">{busiestWeek.birthdays}</div>
                                        <div className="text-xs text-purple-500">birthdays</div>
                                    </div>
                                </div>
                            </div>

                            {/* Busiest Day of Week */}
                            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center">
                                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Busiest Day</h3>
                                            <p className="text-sm text-gray-600">{busiestDayOfWeek.day}s</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-green-600">{busiestDayOfWeek.birthdays}</div>
                                        <div className="text-xs text-green-500">birthdays</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Landmark Celebrations */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Landmark Celebrations</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Special milestone birthdays in {currentYear}</p>

                        <div className="space-y-4">
                            {/* Total Landmark Celebrations */}
                            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-yellow-500 rounded-xl flex items-center justify-center">
                                            <span className="text-white text-lg font-bold">🎉</span>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Total Landmarks</h3>
                                            <p className="text-sm text-gray-600">Special milestone birthdays</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-yellow-600">{totalLandmarkCelebrations}</div>
                                        <div className="text-xs text-yellow-500">celebrations</div>
                                    </div>
                                </div>
                            </div>

                            {/* Landmark Breakdown */}
                            {Object.keys(landmarksByAge).length > 0 ? (
                                <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Breakdown by Age</h4>
                                    <div className="grid grid-cols-2 gap-3">
                                        {Object.entries(landmarksByAge)
                                            .sort(([a], [b]) => Number(a) - Number(b))
                                            .map(([age, count]) => (
                                                <div key={age} className="bg-gray-50 p-3 rounded-lg border">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-sm font-medium text-gray-700">{age} years</span>
                                                        <span className="text-lg font-bold text-gray-900">{count}</span>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-6">
                                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    </div>
                                    <p className="text-sm text-gray-500">No landmark birthdays this year</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Age Demographics Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Children Analytics */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Children Analytics</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Contacts under 18 years old</p>

                        <div className="space-y-6">
                            {/* Children Overview */}
                            <div className="bg-gradient-to-r from-pink-50 to-rose-50 p-4 rounded-xl border border-pink-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-pink-500 rounded-xl flex items-center justify-center">
                                            <span className="text-white text-lg font-bold">👶</span>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Total Children</h3>
                                            <p className="text-sm text-gray-600">{childrenPercentageFormatted}% of all contacts</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-pink-600">{childrenCount}</div>
                                        <div className="text-xs text-pink-500">{upcomingChildren} upcoming</div>
                                    </div>
                                </div>
                            </div>

                            {/* Children Age Breakdown */}
                            {childrenCount > 0 ? (
                                <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Age Groups</h4>
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">🍼</span>
                                                <span className="text-sm font-medium text-gray-700">Toddlers (0-3)</span>
                                            </div>
                                            <span className="text-lg font-bold text-purple-600">{childrenAgeGroups.toddlers}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">🎨</span>
                                                <span className="text-sm font-medium text-gray-700">Preschool (4-5)</span>
                                            </div>
                                            <span className="text-lg font-bold text-blue-600">{childrenAgeGroups.preschool}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">📚</span>
                                                <span className="text-sm font-medium text-gray-700">Elementary (6-11)</span>
                                            </div>
                                            <span className="text-lg font-bold text-green-600">{childrenAgeGroups.elementary}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg border border-orange-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">🎓</span>
                                                <span className="text-sm font-medium text-gray-700">Teens (12-17)</span>
                                            </div>
                                            <span className="text-lg font-bold text-orange-600">{childrenAgeGroups.teens}</span>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-6">
                                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span className="text-2xl">👶</span>
                                    </div>
                                    <p className="text-sm text-gray-500">No children contacts</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Adults Analytics */}
                    <div className="bg-white p-6 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-4">
                            <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                            <h2 className="text-lg font-semibold text-gray-900">Adults Analytics</h2>
                        </div>
                        <p className="text-sm text-gray-600 mb-6">Contacts 18 years and older</p>

                        <div className="space-y-6">
                            {/* Adults Overview */}
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
                                            <span className="text-white text-lg font-bold">👨</span>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">Total Adults</h3>
                                            <p className="text-sm text-gray-600">{adultsPercentageFormatted}% of all contacts</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-blue-600">{adultsCount}</div>
                                        <div className="text-xs text-blue-500">{upcomingAdults} upcoming</div>
                                    </div>
                                </div>
                            </div>

                            {/* Adults Age Breakdown */}
                            {adultsCount > 0 ? (
                                <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Age Groups</h4>
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-lg border border-cyan-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">🎯</span>
                                                <span className="text-sm font-medium text-gray-700">Young Adults (18-29)</span>
                                            </div>
                                            <span className="text-lg font-bold text-cyan-600">{adultsAgeGroups.youngAdults}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">💼</span>
                                                <span className="text-sm font-medium text-gray-700">Adults (30-49)</span>
                                            </div>
                                            <span className="text-lg font-bold text-emerald-600">{adultsAgeGroups.adults}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">🏡</span>
                                                <span className="text-sm font-medium text-gray-700">Middle Age (50-64)</span>
                                            </div>
                                            <span className="text-lg font-bold text-amber-600">{adultsAgeGroups.middleAge}</span>
                                        </div>
                                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-100">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-lg">👴</span>
                                                <span className="text-sm font-medium text-gray-700">Seniors (65+)</span>
                                            </div>
                                            <span className="text-lg font-bold text-violet-600">{adultsAgeGroups.seniors}</span>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-6">
                                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span className="text-2xl">👨</span>
                                    </div>
                                    <p className="text-sm text-gray-500">No adult contacts</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AnalyticsDashboard;
