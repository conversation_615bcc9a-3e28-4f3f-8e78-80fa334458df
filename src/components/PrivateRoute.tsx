import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import LoadingOverlay from "./LoadingOverlay";
import type { ReactNode } from "react";

interface PrivateRouteProps {
    children: ReactNode;
}

const PrivateRoute = ({ children }: PrivateRouteProps) => {
    const { user, isAuthLoading } = useAuth();

    if (isAuthLoading) {
        return <LoadingOverlay isVisible={true} message="Checking authentication..." />;
    }

    return user ? children : <Navigate to="/login" replace />;
};

export default PrivateRoute;
