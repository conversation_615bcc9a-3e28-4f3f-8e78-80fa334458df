import React, { useEffect, useState } from "react";

interface Backup {
    name: string;
    date: string; // parsed from filename
}

const BackupDashboard: React.FC = () => {
    const [backups, setBackups] = useState<Backup[]>([]);
    const [selected, setSelected] = useState<string | null>(null);
    const [status, setStatus] = useState<string>("");

    // Fetch backup list from your backend API or static file
    // For MVP, simulate with dummy data or call your own API
    useEffect(() => {
        // Replace this with API call in real app
        const dummyBackups = [
            { name: "backup_birthday_saas_2025-06-30-15:30:00.tar.gz", date: "2025-06-30 15:30:00" },
            { name: "backup_birthday_saas_2025-06-29-14:00:00.tar.gz", date: "2025-06-29 14:00:00" },
        ];
        setBackups(dummyBackups);
    }, []);

    const triggerRollback = async () => {
        if (!selected) {
            setStatus("Please select a backup to rollback.");
            return;
        }

        setStatus("Triggering rollback...");

        try {
            // Call your GitHub Actions workflow dispatch API or backend endpoint here
            // Example: POST to GitHub API to trigger workflow_dispatch with backup filename
            // For MVP, simulate success:

            await new Promise((r) => setTimeout(r, 2000));
            setStatus(`Rollback triggered for ${selected}`);
        } catch (error) {
            setStatus("Failed to trigger rollback.");
        }
    };

    return (
        <div className="max-w-xl mx-auto p-6 border rounded shadow mt-8">
            <h2 className="text-2xl font-bold mb-4">Backup & Rollback Dashboard</h2>

            <div className="mb-4">
                <label className="block font-semibold mb-2">Available Backups:</label>
                <select
                    className="w-full border p-2 rounded"
                    value={selected ?? ""}
                    onChange={(e) => setSelected(e.target.value)}
                >
                    <option value="" disabled>
                        -- Select a backup --
                    </option>
                    {backups.map((b) => (
                        <option key={b.name} value={b.name}>
                            {b.date} — {b.name}
                        </option>
                    ))}
                </select>
            </div>

            <button
                onClick={triggerRollback}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
                Rollback to Selected Backup
            </button>

            {status && <p className="mt-4 text-gray-700">{status}</p>}
        </div>
    );
};

export default BackupDashboard;
