import { useState, useEffect } from "react";
import { useContactsQuery, useDeleteContact, useUpdateContact } from "../hooks/useContacts";
import { useUsageTracking } from "../hooks/useFeatureAccess";
import { UsageLimit } from "./FeatureGate";
import { type Contact } from "../types";
import {
    useContactGiftHistoryQuery,
    useContactGiftReminderQuery,
    useGiftSuggestionsQuery,
    useAddGiftToHistory,
    useCreateGiftReminder,
    useDeleteGiftReminder
} from "../hooks/useGifts";

interface ContactsPageProps {
    onNavigate: (page: string) => void;
}

const ContactsPage = ({ onNavigate }: ContactsPageProps) => {
    // Use React Query hooks instead of context
    const { data: contacts = [], isLoading, error, refetch } = useContactsQuery();
    const deleteContactMutation = useDeleteContact();
    const updateContactMutation = useUpdateContact();
    const contactsUsage = useUsageTracking('CONTACTS');
    const addGiftToHistoryMutation = useAddGiftToHistory();
    const createGiftReminderMutation = useCreateGiftReminder();
    // const updateGiftReminderMutation = useUpdateGiftReminder(); // Not used in current implementation
    const deleteGiftReminderMutation = useDeleteGiftReminder();

    const [searchTerm, setSearchTerm] = useState("");
    const [activeFilter, setActiveFilter] = useState("All");
    const [currentPage, setCurrentPage] = useState(1);
    const [contactsPerPage, setContactsPerPage] = useState(6); // Show 6 contacts per page

    // Modal states for Message and Gift functionality
    const [showMessageModal, setShowMessageModal] = useState(false);
    const [showGiftModal, setShowGiftModal] = useState(false);
    const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

    // Gift modal sub-states
    const [giftModalView, setGiftModalView] = useState<'main' | 'browse' | 'history' | 'reminder'>('main');

    // Edit and Delete states
    const [editingContact, setEditingContact] = useState<Contact | null>(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);
    const [editForm, setEditForm] = useState({
        name: '',
        birthday: '',
        category: 'Friends' as Contact['category'],
        anniversaryDate: '',
        anniversaryType: 'Wedding' as Contact['anniversaryType'],
        partnerName: '',
        anniversaryNotes: ''
    });

    // Gift data using React Query
    const { data: contactGiftHistory = [] } = useContactGiftHistoryQuery(selectedContact?.id || '');
    const { data: contactGiftReminder } = useContactGiftReminderQuery(selectedContact?.id || '');
    const { data: giftSuggestions = [] } = useGiftSuggestionsQuery();

    const filters = ["All", "Family", "Friends", "Work", "Clients", "Members"];

    // Reset to first page when filters or contacts per page change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, activeFilter, contactsPerPage]);

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading contacts...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load contacts</p>
                    <button
                        onClick={() => refetch()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    // Handler functions for Message and Gift buttons
    const handleMessageClick = (contact: Contact) => {
        setSelectedContact(contact);
        setShowMessageModal(true);
    };

    // CSV Export function
    const exportToCSV = () => {
        const csvHeaders = [
            'Name',
            'Birthday',
            'Category',
            'Anniversary Date',
            'Anniversary Type',
            'Partner Name',
            'Anniversary Notes'
        ];

        const csvData = contacts.map(contact => [
            contact.name,
            contact.birthday,
            contact.category,
            contact.anniversaryDate || '',
            contact.anniversaryType || '',
            contact.partnerName || '',
            contact.anniversaryNotes || ''
        ]);

        const csvContent = [
            csvHeaders.join(','),
            ...csvData.map(row => row.map(field => `"${field}"`).join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `contacts_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Edit contact functions
    const handleEditContact = (contact: Contact) => {
        setEditingContact(contact);
        setEditForm({
            name: contact.name,
            birthday: contact.birthday,
            category: contact.category,
            anniversaryDate: contact.anniversaryDate || '',
            anniversaryType: contact.anniversaryType || 'Wedding',
            partnerName: contact.partnerName || '',
            anniversaryNotes: contact.anniversaryNotes || ''
        });
        setShowEditModal(true);
    };

    const handleSaveEdit = async () => {
        if (!editingContact) return;

        try {
            await updateContactMutation.mutateAsync({
                contactId: editingContact.id,
                updates: {
                    name: editForm.name,
                    birthday: editForm.birthday,
                    category: editForm.category,
                    anniversaryDate: editForm.anniversaryDate,
                    anniversaryType: editForm.anniversaryType,
                    partnerName: editForm.partnerName,
                    anniversaryNotes: editForm.anniversaryNotes
                }
            });
            setShowEditModal(false);
            setEditingContact(null);
        } catch (error) {
            console.error('Failed to update contact:', error);
        }
    };

    const handleCancelEdit = () => {
        setShowEditModal(false);
        setEditingContact(null);
        setEditForm({
            name: '',
            birthday: '',
            category: 'Friends',
            anniversaryDate: '',
            anniversaryType: 'Wedding',
            partnerName: '',
            anniversaryNotes: ''
        });
    };

    // Enhanced delete with confirmation modal
    const handleDeleteContact = (contact: Contact) => {
        setContactToDelete(contact);
        setShowDeleteModal(true);
    };

    const confirmDelete = async () => {
        if (!contactToDelete) return;

        try {
            await deleteContactMutation.mutateAsync(contactToDelete.id);
            setShowDeleteModal(false);
            setContactToDelete(null);
        } catch (error) {
            console.error('Failed to delete contact:', error);
        }
    };

    const cancelDelete = () => {
        setShowDeleteModal(false);
        setContactToDelete(null);
    };

    const handleGiftClick = (contact: Contact) => {
        setSelectedContact(contact);
        setGiftModalView('main');
        setShowGiftModal(true);
        // Gift data will be automatically loaded by React Query hooks
    };

    const sendMessage = (method: string) => {
        if (selectedContact) {
            switch (method) {
                case 'sms':
                    alert(`SMS sent to ${selectedContact.name}: "Hi ${selectedContact.name}! Hope you're having a great day! 😊"`);
                    break;
                case 'email':
                    alert(`Email sent to ${selectedContact.name}: "Hello ${selectedContact.name}, just wanted to reach out and say hi!"`);
                    break;
                case 'whatsapp':
                    alert(`WhatsApp message sent to ${selectedContact.name}: "Hey ${selectedContact.name}! 👋 How are you doing?"`);
                    break;
                case 'call':
                    alert(`Calling ${selectedContact.name}... 📞`);
                    break;
                default:
                    alert(`Message sent to ${selectedContact.name}! 💬`);
            }
            setShowMessageModal(false);
            setSelectedContact(null);
        }
    };

    const handleGiftAction = (action: string) => {
        if (selectedContact) {
            switch (action) {
                case 'browse':
                    setGiftModalView('browse');
                    break;
                case 'history':
                    setGiftModalView('history');
                    break;
                case 'reminder':
                    setGiftModalView('reminder');
                    break;
                case 'back':
                    setGiftModalView('main');
                    break;
                default:
                    setShowGiftModal(false);
                    setSelectedContact(null);
                    setGiftModalView('main');
            }
        }
    };

    const handleAddToHistory = async (giftName: string, amount: number, category: string, notes?: string) => {
        if (selectedContact) {
            try {
                await addGiftToHistoryMutation.mutateAsync({
                    giftName,
                    recipient: selectedContact.name,
                    recipientId: selectedContact.id,
                    date: new Date().toISOString().split('T')[0],
                    amount,
                    category,
                    notes
                });

                alert(`Added "${giftName}" to gift history for ${selectedContact.name}! 🎁`);
            } catch (error) {
                alert(`Failed to add gift to history. Please try again.`);
                console.error('Error adding gift to history:', error);
            }
        }
    };

    const handleCreateReminder = async (reminderDate: string, giftIdeas: string[], budget?: number, notes?: string) => {
        if (selectedContact) {
            try {
                // Delete existing reminder if any
                if (contactGiftReminder) {
                    await deleteGiftReminderMutation.mutateAsync({
                        reminderId: contactGiftReminder.id,
                        contactId: selectedContact.id
                    });
                }

                await createGiftReminderMutation.mutateAsync({
                    contactId: selectedContact.id,
                    contactName: selectedContact.name,
                    reminderDate,
                    eventDate: selectedContact.birthday,
                    giftIdeas,
                    budget,
                    notes,
                    isActive: true
                });

                alert(`Gift reminder created for ${selectedContact.name}'s birthday! 🎁`);
                setGiftModalView('main');
            } catch (error) {
                alert(`Failed to create gift reminder. Please try again.`);
                console.error('Error creating gift reminder:', error);
            }
        }
    };

    // Function to calculate days until next birthday or anniversary
    const calculateDaysUntilBirthday = (birthdayString: string): number => {
        const today = new Date();
        const currentYear = today.getFullYear();

        // Parse the birthday string - handle both formats
        let birthdayDate: Date;
        if (birthdayString.includes('-')) {
            // Format: YYYY-MM-DD
            birthdayDate = new Date(birthdayString);
        } else {
            // Format: "Month DD, YYYY"
            birthdayDate = new Date(birthdayString);
        }

        // Create this year's birthday
        const thisYearBirthday = new Date(currentYear, birthdayDate.getMonth(), birthdayDate.getDate());

        // If this year's birthday has passed, calculate for next year
        let nextBirthday = thisYearBirthday;
        if (thisYearBirthday < today) {
            nextBirthday = new Date(currentYear + 1, birthdayDate.getMonth(), birthdayDate.getDate());
        }

        // Calculate days difference
        const timeDiff = nextBirthday.getTime() - today.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        return daysDiff;
    };

    // Function specifically for calculating days until anniversary
    const calculateDaysUntilAnniversary = (anniversaryString: string): number => {
        if (!anniversaryString) return 0;
        return calculateDaysUntilBirthday(anniversaryString);
    };

    // Helper function to generate a birthday date X days from today
    const getUpcomingBirthdayDate = (daysFromToday: number): string => {
        const today = new Date();
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() + daysFromToday);

        // Format as "Month DD, YYYY" to match existing format
        return targetDate.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        });
    };

    // Sample contact data to match the design
    const sampleContactsData: Array<{
        id: string;
        name: string;
        category: string;
        age: number;
        birthday: string;
        email: string;
        phone: string;
        avatar: string;
        color: string;
        notes: string;
        tags: string[];
        anniversaryDate?: string;
        anniversaryType?: string;
        partnerName?: string;
        anniversaryNotes?: string;
    }> = [
            {
                id: "1",
                name: "Sarah Johnson",
                category: "Friends",
                age: 35,
                birthday: "March 15, 1990",
                email: "<EMAIL>",
                phone: "+1234567890",
                avatar: "SJ",
                color: "bg-purple-500",
                notes: "Loves chocolate and books",
                tags: ["Friends"],
                anniversaryDate: "2020-06-20",
                anniversaryType: "Wedding",
                partnerName: "David Johnson",
                anniversaryNotes: "Met at college"
            },
            {
                id: "2",
                name: "Mike Chen",
                category: "Colleague",
                age: 39,
                birthday: getUpcomingBirthdayDate(3), // 3 days from today
                email: "<EMAIL>",
                phone: "",
                avatar: "MC",
                color: "bg-purple-500",
                notes: "Tech enthusiast, coffee lover",
                tags: ["Work"]
            },
            {
                id: "3",
                name: "Emma Wilson",
                category: "Sister",
                age: 32,
                birthday: "December 8, 1992",
                email: "<EMAIL>",
                phone: "",
                avatar: "EW",
                color: "bg-gray-500",
                notes: "Fitness enthusiast, loves yoga",
                tags: ["Family"],
                anniversaryDate: getUpcomingBirthdayDate(5), // 5 days from today
                anniversaryType: "Dating",
                partnerName: "Mark Wilson",
                anniversaryNotes: "Started dating in college"
            },
            {
                id: "4",
                name: "Alex Rodriguez",
                category: "Friends",
                age: 28,
                birthday: getUpcomingBirthdayDate(1), // Tomorrow
                email: "<EMAIL>",
                phone: "+1555123456",
                avatar: "AR",
                color: "bg-green-500",
                notes: "Birthday tomorrow! 🎂",
                tags: ["Friends"]
            },
            {
                id: "5",
                name: "Lisa Chen",
                category: "Colleagues",
                age: 31,
                birthday: "September 12, 1993",
                email: "<EMAIL>",
                phone: "+1555987654",
                avatar: "LC",
                color: "bg-indigo-500",
                notes: "Marketing specialist",
                tags: ["Work"],
                anniversaryDate: getUpcomingBirthdayDate(3), // 3 days from today
                anniversaryType: "Business",
                partnerName: "TechCorp Inc",
                anniversaryNotes: "Started working together"
            },
            {
                id: "6",
                name: "David Kim",
                category: "Family",
                age: 45,
                birthday: "February 28, 1979",
                email: "<EMAIL>",
                phone: "+1555456789",
                avatar: "DK",
                color: "bg-red-500",
                notes: "Uncle, loves fishing",
                tags: ["Family"]
            },
            {
                id: "7",
                name: "Maria Garcia",
                category: "Clients",
                age: 38,
                birthday: "October 5, 1986",
                email: "<EMAIL>",
                phone: "+1555321654",
                avatar: "MG",
                color: "bg-yellow-500",
                notes: "Important client, prefers email",
                tags: ["Clients"]
            },
            {
                id: "8",
                name: "James Wilson",
                category: "Friends",
                age: 29,
                birthday: getUpcomingBirthdayDate(15), // 15 days from today
                email: "<EMAIL>",
                phone: "+1555789123",
                avatar: "JW",
                color: "bg-pink-500",
                notes: "Gym buddy, loves sports",
                tags: ["Friends"]
            },
            {
                id: "9",
                name: "Anna Thompson",
                category: "Colleagues",
                age: 33,
                birthday: "May 20, 1991",
                email: "<EMAIL>",
                phone: "+1555654987",
                avatar: "AT",
                color: "bg-teal-500",
                notes: "HR manager, very organized",
                tags: ["Work"]
            },
            {
                id: "10",
                name: "Robert Davis",
                category: "Family",
                age: 52,
                birthday: "August 14, 1972",
                email: "<EMAIL>",
                phone: "+1555147258",
                avatar: "RD",
                color: "bg-orange-500",
                notes: "Father-in-law, retired teacher",
                tags: ["Family"]
            }
        ];

    // Add calculated days until birthday and anniversary to sample contacts
    const sampleContacts = sampleContactsData.map(contact => {
        const originalAnniversaryDate = contact.anniversaryDate;
        return {
            ...contact,
            daysUntilBirthday: calculateDaysUntilBirthday(contact.birthday),
            // Anniversary fields - preserve original data and add formatted date
            anniversaryDate: originalAnniversaryDate ? new Date(originalAnniversaryDate).toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            }) : null,
            anniversaryType: contact.anniversaryType || null,
            partnerName: contact.partnerName || null,
            anniversaryNotes: contact.anniversaryNotes || null,
            daysUntilAnniversary: originalAnniversaryDate ? calculateDaysUntilAnniversary(originalAnniversaryDate) : null
        };
    });

    // Combine real contacts with sample data for display
    const allContacts = [
        ...sampleContacts,
        ...contacts.map(contact => ({
            id: contact.id,
            name: contact.name,
            category: contact.category,
            age: new Date().getFullYear() - new Date(contact.birthday).getFullYear(),
            birthday: new Date(contact.birthday).toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            }),
            email: `${contact.name.toLowerCase().replace(' ', '.')}@example.com`,
            phone: "",
            avatar: contact.name.split(' ').map(n => n[0]).join('').toUpperCase(),
            color: "bg-purple-500",
            notes: `Contact added via form`,
            daysUntilBirthday: calculateDaysUntilBirthday(contact.birthday),
            // Anniversary fields
            anniversaryDate: contact.anniversaryDate ? new Date(contact.anniversaryDate).toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            }) : null,
            anniversaryType: contact.anniversaryType,
            partnerName: contact.partnerName,
            anniversaryNotes: contact.anniversaryNotes,
            daysUntilAnniversary: contact.anniversaryDate ? calculateDaysUntilAnniversary(contact.anniversaryDate) : null,
            tags: contact.category === "Family" ? ["Family"] :
                contact.category === "Friends" ? ["Friends"] :
                    contact.category === "Colleagues" ? ["Work"] : []
        }))
    ];



    const filteredContacts = allContacts
        .filter(contact => {
            const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesFilter = activeFilter === "All" ||
                (activeFilter === "Family" && contact.tags?.includes("Family")) ||
                (activeFilter === "Friends" && (contact.category === "Friends" || contact.tags?.includes("Friends"))) ||
                (activeFilter === "Work" && contact.tags?.includes("Work")) ||
                (activeFilter === "Clients" && contact.category === "Clients") ||
                (activeFilter === "Members" && contact.category === "Members");
            return matchesSearch && matchesFilter;
        })
        .sort((a, b) => a.daysUntilBirthday - b.daysUntilBirthday);

    // Pagination calculations
    const totalContacts = filteredContacts.length;
    const totalPages = Math.ceil(totalContacts / contactsPerPage);
    const startIndex = (currentPage - 1) * contactsPerPage;
    const endIndex = startIndex + contactsPerPage;
    const paginatedContacts = filteredContacts.slice(startIndex, endIndex);



    const getTagColor = (category: string) => {
        switch (category) {
            case "Friends":
                return "bg-blue-100 text-blue-600";
            case "Colleague":
                return "bg-green-100 text-green-600";
            case "Sister":
                return "bg-red-100 text-red-600";
            default:
                return "bg-gray-100 text-gray-600";
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">👥</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
                        Contacts
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Manage your birthday contacts with React Query
                    </p>
                    <p className="text-sm text-gray-500 mt-2">📅 Sorted by upcoming birthdays</p>

                    {/* Usage Limit Display */}
                    <div className="mt-4 flex justify-center">
                        <div className="max-w-xs">
                            <UsageLimit
                                feature="CONTACTS"
                                currentUsage={contacts.length}
                                showUpgradeWhenFull={true}
                            />
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-center mb-8">
                    <div className="flex gap-4">
                        <div className="flex gap-2">
                            <button
                                onClick={() => refetch()}
                                disabled={isLoading}
                                className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-2xl hover:bg-white/90 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center disabled:opacity-50 border border-white/20 font-semibold"
                            >
                                {isLoading ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                                ) : (
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                                    </svg>
                                )}
                                Refresh
                            </button>
                            <button
                                onClick={exportToCSV}
                                disabled={contacts.length === 0}
                                className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-2xl hover:bg-white/90 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center disabled:opacity-50 border border-white/20 font-semibold"
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export CSV
                            </button>
                            <button
                                onClick={() => onNavigate('add-contacts')}
                                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center font-semibold"
                            >
                                <span className="mr-2">+</span>
                                Add Contacts
                            </button>
                        </div>
                    </div>
                </div>

                {/* Search Bar */}
                <div className="mb-6">
                    <div className="relative max-w-md">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <input
                            type="text"
                            placeholder="Search contacts..."
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                </div>

                {/* Filter Tabs */}
                <div className="flex space-x-1 mb-8">
                    {filters.map((filter) => (
                        <button
                            key={filter}
                            onClick={() => setActiveFilter(filter)}
                            className={`px-4 py-2 rounded-lg font-medium transition-colors ${activeFilter === filter
                                ? "bg-purple-600 text-white"
                                : "bg-white text-gray-600 hover:bg-gray-50"
                                }`}
                        >
                            {filter}
                        </button>
                    ))}
                </div>

                {/* Contacts Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {paginatedContacts.map((contact) => (
                        <div key={contact.id} className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
                            {/* Contact Header */}
                            <div className="flex items-start justify-between mb-4">
                                <div className="flex items-center">
                                    <div className={`w-12 h-12 ${contact.color} rounded-full flex items-center justify-center text-white font-semibold text-lg mr-3`}>
                                        {contact.avatar}
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 text-lg">{contact.name}</h3>
                                        <p className="text-sm text-gray-500">{contact.category}</p>
                                    </div>
                                </div>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => handleEditContact(contact as Contact)}
                                        className="text-gray-400 hover:text-blue-600 transition-colors"
                                        title="Edit contact"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                    <button
                                        onClick={() => handleDeleteContact(contact as Contact)}
                                        disabled={deleteContactMutation.isPending}
                                        className="text-gray-400 hover:text-red-600 disabled:opacity-50 transition-colors"
                                        title="Delete contact"
                                    >
                                        {deleteContactMutation.isPending ? (
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
                                        ) : (
                                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 0 100-2H9z" clipRule="evenodd" />
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clipRule="evenodd" />
                                            </svg>
                                        )}
                                    </button>
                                </div>
                            </div>

                            {/* Tags */}
                            {contact.tags && (
                                <div className="mb-3">
                                    {contact.tags.map((tag) => (
                                        <span key={tag} className={`inline-block px-2 py-1 rounded text-xs font-medium mr-2 ${getTagColor(tag)}`}>
                                            {tag}
                                        </span>
                                    ))}
                                </div>
                            )}

                            {/* Age */}
                            <div className="flex items-center mb-3">
                                <span className="bg-purple-100 text-purple-600 px-2 py-1 rounded text-sm font-medium">
                                    Age {contact.age}
                                </span>
                            </div>

                            {/* Contact Info */}
                            <div className="space-y-2 mb-4">
                                <div className="flex items-center text-sm text-gray-600">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    🎂 {contact.birthday}
                                </div>
                                {contact.anniversaryDate && (
                                    <div className="flex items-center text-sm text-gray-600">
                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                        </svg>
                                        💕 {contact.anniversaryType || 'Anniversary'}: {contact.anniversaryDate}
                                        {contact.partnerName && ` with ${contact.partnerName}`}
                                    </div>
                                )}

                                <div className="flex items-center text-sm text-gray-600">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    {contact.email}
                                </div>
                                {contact.phone && (
                                    <div className="flex items-center text-sm text-gray-600">
                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                        </svg>
                                        {contact.phone}
                                    </div>
                                )}
                            </div>

                            {/* Notes */}
                            {contact.notes && (
                                <p className="text-sm text-gray-600 mb-4">{contact.notes}</p>
                            )}

                            {/* Days until birthday and anniversary */}
                            <div className="mb-4 space-y-2">
                                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${contact.daysUntilBirthday === 0 ? 'bg-red-100 text-red-700' :
                                    contact.daysUntilBirthday === 1 ? 'bg-orange-100 text-orange-700' :
                                        contact.daysUntilBirthday <= 7 ? 'bg-yellow-100 text-yellow-700' :
                                            contact.daysUntilBirthday <= 30 ? 'bg-blue-100 text-blue-700' :
                                                'bg-gray-100 text-gray-600'
                                    }`}>
                                    {contact.daysUntilBirthday === 0 ? '🎉 Birthday Today!' :
                                        contact.daysUntilBirthday === 1 ? '🎂 Birthday Tomorrow' :
                                            contact.daysUntilBirthday <= 7 ? `🗓️ Birthday in ${contact.daysUntilBirthday} days` :
                                                `📅 Birthday in ${contact.daysUntilBirthday} days`}
                                </div>
                                {contact.daysUntilAnniversary !== null && contact.daysUntilAnniversary !== undefined && contact.anniversaryDate && (
                                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${contact.daysUntilAnniversary === 0 ? 'bg-pink-100 text-pink-700' :
                                        contact.daysUntilAnniversary === 1 ? 'bg-rose-100 text-rose-700' :
                                            contact.daysUntilAnniversary <= 7 ? 'bg-purple-100 text-purple-700' :
                                                contact.daysUntilAnniversary <= 30 ? 'bg-indigo-100 text-indigo-700' :
                                                    'bg-gray-100 text-gray-600'
                                        }`}>
                                        {contact.daysUntilAnniversary === 0 ? '💕 Anniversary Today!' :
                                            contact.daysUntilAnniversary === 1 ? '💖 Anniversary Tomorrow' :
                                                contact.daysUntilAnniversary <= 7 ? `💝 Anniversary in ${contact.daysUntilAnniversary} days` :
                                                    `💐 Anniversary in ${contact.daysUntilAnniversary} days`}
                                    </div>
                                )}
                            </div>

                            {/* Action Buttons */}
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleMessageClick(contact)}
                                    className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors flex items-center justify-center"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                    Message
                                </button>
                                <button
                                    onClick={() => handleGiftClick(contact)}
                                    className="bg-purple-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-purple-700 transition-colors flex items-center justify-center"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM14 9a1 1 0 100 2h2a1 1 0 100-2h-2zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                                    </svg>
                                    Gifts
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Pagination Controls */}
                {(totalPages > 1 || totalContacts > 6) && (
                    <div className="mt-8 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center text-sm text-gray-700">
                                <span>
                                    Showing {startIndex + 1} to {Math.min(endIndex, totalContacts)} of {totalContacts} contacts
                                </span>
                            </div>

                            {/* Contacts per page selector */}
                            <div className="flex items-center space-x-2 text-sm text-gray-700">
                                <label htmlFor="contactsPerPage">Show:</label>
                                <select
                                    id="contactsPerPage"
                                    value={contactsPerPage}
                                    onChange={(e) => setContactsPerPage(Number(e.target.value))}
                                    className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-purple-500 focus:border-purple-500"
                                >
                                    <option value={6}>6</option>
                                    <option value={12}>12</option>
                                    <option value={24}>24</option>
                                    <option value={50}>50</option>
                                </select>
                                <span>per page</span>
                            </div>
                        </div>

                        {totalPages > 1 && (
                            <div className="flex items-center space-x-2">
                                {/* Previous Button */}
                                <button
                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Previous
                                </button>

                                {/* Page Numbers */}
                                <div className="flex items-center space-x-1">
                                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => {
                                        // Show first page, last page, current page, and pages around current
                                        const showPage = pageNum === 1 ||
                                            pageNum === totalPages ||
                                            Math.abs(pageNum - currentPage) <= 1;

                                        if (!showPage) {
                                            // Show ellipsis for gaps
                                            if (pageNum === 2 && currentPage > 4) {
                                                return <span key={pageNum} className="px-2 text-gray-400">...</span>;
                                            }
                                            if (pageNum === totalPages - 1 && currentPage < totalPages - 3) {
                                                return <span key={pageNum} className="px-2 text-gray-400">...</span>;
                                            }
                                            return null;
                                        }

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => setCurrentPage(pageNum)}
                                                className={`px-3 py-2 text-sm font-medium rounded-md ${currentPage === pageNum
                                                    ? 'bg-purple-600 text-white'
                                                    : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                                                    }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}
                                </div>

                                {/* Next Button */}
                                <button
                                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Next
                                </button>
                            </div>
                        )}
                    </div>
                )}

                {/* Empty State */}
                {filteredContacts.length === 0 && (
                    <div className="text-center py-12">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No contacts found</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by adding a new contact.</p>
                    </div>
                )}

                {/* Message Modal */}
                {showMessageModal && selectedContact && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Send Message to {selectedContact.name}
                                </h3>
                                <button
                                    onClick={() => setShowMessageModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div className="space-y-3">
                                <p className="text-sm font-medium text-gray-700 mb-3">Choose how to send your message:</p>

                                <button
                                    onClick={() => sendMessage('sms')}
                                    className="w-full flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                                >
                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                    Send SMS
                                </button>

                                <button
                                    onClick={() => sendMessage('email')}
                                    className="w-full flex items-center justify-center px-4 py-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                                >
                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    Send Email
                                </button>

                                <button
                                    onClick={() => sendMessage('whatsapp')}
                                    className="w-full flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                                >
                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                    Send WhatsApp
                                </button>

                                <button
                                    onClick={() => sendMessage('call')}
                                    className="w-full flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                                >
                                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                    </svg>
                                    Make a Call
                                </button>
                            </div>

                            <div className="flex justify-end mt-6">
                                <button
                                    onClick={() => setShowMessageModal(false)}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Enhanced Gift Modal */}
                {showGiftModal && selectedContact && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {giftModalView === 'main' && `Gift Options for ${selectedContact.name}`}
                                    {giftModalView === 'browse' && `Gift Suggestions for ${selectedContact.name}`}
                                    {giftModalView === 'history' && `Gift History for ${selectedContact.name}`}
                                    {giftModalView === 'reminder' && `Set Gift Reminder for ${selectedContact.name}`}
                                </h3>
                                <button
                                    onClick={() => {
                                        setShowGiftModal(false);
                                        setGiftModalView('main');
                                    }}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            {/* Main Gift Options View */}
                            {giftModalView === 'main' && (
                                <div className="space-y-3">
                                    <p className="text-sm font-medium text-gray-700 mb-3">What would you like to do?</p>

                                    <button
                                        onClick={() => handleGiftAction('browse')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM14 9a1 1 0 100 2h2a1 1 0 100-2h-2zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                                        </svg>
                                        Browse Gift Ideas ({giftSuggestions.length} suggestions)
                                    </button>

                                    <button
                                        onClick={() => handleGiftAction('history')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                        </svg>
                                        View Gift History ({contactGiftHistory.length} gifts)
                                    </button>

                                    <button
                                        onClick={() => handleGiftAction('reminder')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                        {contactGiftReminder ? 'Update Gift Reminder' : 'Set Gift Reminder'}
                                    </button>

                                    <div className="flex justify-end mt-6">
                                        <button
                                            onClick={() => setShowGiftModal(false)}
                                            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Browse Gift Suggestions View */}
                            {giftModalView === 'browse' && (
                                <div>
                                    <div className="mb-4">
                                        <button
                                            onClick={() => handleGiftAction('back')}
                                            className="flex items-center text-purple-600 hover:text-purple-700 mb-4"
                                        >
                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                            </svg>
                                            Back to Options
                                        </button>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {giftSuggestions.map((gift) => (
                                            <div key={gift.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                                <div className="flex justify-between items-start mb-2">
                                                    <h4 className="font-semibold text-gray-900">{gift.name}</h4>
                                                    <span className="text-lg font-bold text-green-600">${gift.price}</span>
                                                </div>
                                                <p className="text-sm text-gray-600 mb-2">{gift.description}</p>
                                                <div className="flex items-center justify-between">
                                                    <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                                        {gift.category}
                                                    </span>
                                                    <div className="flex items-center">
                                                        <div className="flex text-yellow-400 mr-1">
                                                            {[...Array(5)].map((_, i) => (
                                                                <svg key={i} className={`w-3 h-3 ${i < Math.floor(gift.rating) ? 'fill-current' : 'text-gray-300'}`} viewBox="0 0 20 20">
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                            ))}
                                                        </div>
                                                        <span className="text-xs text-gray-600">{gift.rating}</span>
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => handleAddToHistory(gift.name, gift.price, gift.category, `Suggested gift for ${selectedContact.name}`)}
                                                    className="w-full mt-3 bg-purple-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-purple-700 transition-colors"
                                                >
                                                    Add to History
                                                </button>
                                            </div>
                                        ))}
                                    </div>

                                    {giftSuggestions.length === 0 && (
                                        <div className="text-center py-8">
                                            <p className="text-gray-500">No gift suggestions available at the moment.</p>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Gift History View */}
                            {giftModalView === 'history' && (
                                <div>
                                    <div className="mb-4">
                                        <button
                                            onClick={() => handleGiftAction('back')}
                                            className="flex items-center text-purple-600 hover:text-purple-700 mb-4"
                                        >
                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                            </svg>
                                            Back to Options
                                        </button>
                                    </div>

                                    <div className="space-y-4">
                                        {contactGiftHistory.map((gift) => (
                                            <div key={gift.id} className="border rounded-lg p-4 bg-gray-50">
                                                <div className="flex justify-between items-start mb-2">
                                                    <h4 className="font-semibold text-gray-900">{gift.giftName}</h4>
                                                    <span className="text-lg font-bold text-green-600">${gift.amount}</span>
                                                </div>
                                                <div className="flex items-center justify-between text-sm text-gray-600">
                                                    <span>{new Date(gift.date).toLocaleDateString()}</span>
                                                    <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs">
                                                        {gift.category}
                                                    </span>
                                                </div>
                                                {gift.notes && (
                                                    <p className="text-sm text-gray-600 mt-2 italic">{gift.notes}</p>
                                                )}
                                            </div>
                                        ))}
                                    </div>

                                    {contactGiftHistory.length === 0 && (
                                        <div className="text-center py-8">
                                            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p className="text-gray-500">No gift history for {selectedContact.name} yet.</p>
                                            <p className="text-sm text-gray-400 mt-1">Gifts you add will appear here.</p>
                                        </div>
                                    )}

                                    {/* Add Custom Gift to History */}
                                    <div className="mt-6 pt-4 border-t">
                                        <h5 className="font-medium text-gray-900 mb-3">Add Custom Gift to History</h5>
                                        <form onSubmit={(e) => {
                                            e.preventDefault();
                                            const formData = new FormData(e.target as HTMLFormElement);
                                            const giftName = formData.get('giftName') as string;
                                            const amount = parseFloat(formData.get('amount') as string);
                                            const category = formData.get('category') as string;
                                            const notes = formData.get('notes') as string;

                                            if (giftName && amount > 0) {
                                                handleAddToHistory(giftName, amount, category, notes);
                                                (e.target as HTMLFormElement).reset();
                                            }
                                        }}>
                                            <div className="grid grid-cols-2 gap-3 mb-3">
                                                <input
                                                    type="text"
                                                    name="giftName"
                                                    placeholder="Gift name"
                                                    className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                                                    required
                                                />
                                                <input
                                                    type="number"
                                                    name="amount"
                                                    placeholder="Amount ($)"
                                                    step="0.01"
                                                    min="0"
                                                    className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                                                    required
                                                />
                                            </div>
                                            <div className="grid grid-cols-2 gap-3 mb-3">
                                                <select
                                                    name="category"
                                                    className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                                                    required
                                                >
                                                    <option value="">Select category</option>
                                                    <option value="Electronics">Electronics</option>
                                                    <option value="Clothing">Clothing</option>
                                                    <option value="Books">Books</option>
                                                    <option value="Food & Beverage">Food & Beverage</option>
                                                    <option value="Home & Garden">Home & Garden</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                                <input
                                                    type="text"
                                                    name="notes"
                                                    placeholder="Notes (optional)"
                                                    className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                                                />
                                            </div>
                                            <button
                                                type="submit"
                                                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-blue-700 transition-colors"
                                            >
                                                Add to History
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            )}

                            {/* Gift Reminder View */}
                            {giftModalView === 'reminder' && (
                                <div>
                                    <div className="mb-4">
                                        <button
                                            onClick={() => handleGiftAction('back')}
                                            className="flex items-center text-purple-600 hover:text-purple-700 mb-4"
                                        >
                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                            </svg>
                                            Back to Options
                                        </button>
                                    </div>

                                    {/* Current Reminder Display */}
                                    {contactGiftReminder && (
                                        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                            <h5 className="font-medium text-green-900 mb-2">Current Reminder</h5>
                                            <p className="text-sm text-green-700">
                                                Reminder set for: {new Date(contactGiftReminder.reminderDate).toLocaleDateString()}
                                            </p>
                                            <p className="text-sm text-green-700">
                                                Birthday: {new Date(contactGiftReminder.eventDate).toLocaleDateString()}
                                            </p>
                                            {contactGiftReminder.giftIdeas.length > 0 && (
                                                <p className="text-sm text-green-700">
                                                    Ideas: {contactGiftReminder.giftIdeas.join(', ')}
                                                </p>
                                            )}
                                            {contactGiftReminder.budget && (
                                                <p className="text-sm text-green-700">
                                                    Budget: ${contactGiftReminder.budget}
                                                </p>
                                            )}
                                        </div>
                                    )}

                                    {/* Reminder Form */}
                                    <form onSubmit={(e) => {
                                        e.preventDefault();
                                        const formData = new FormData(e.target as HTMLFormElement);
                                        const reminderDate = formData.get('reminderDate') as string;
                                        const giftIdeas = (formData.get('giftIdeas') as string).split(',').map(idea => idea.trim()).filter(Boolean);
                                        const budget = formData.get('budget') ? parseFloat(formData.get('budget') as string) : undefined;
                                        const notes = formData.get('notes') as string;

                                        if (reminderDate) {
                                            handleCreateReminder(reminderDate, giftIdeas, budget, notes);
                                        }
                                    }}>
                                        <div className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reminder Date
                                                </label>
                                                <input
                                                    type="date"
                                                    name="reminderDate"
                                                    defaultValue={contactGiftReminder?.reminderDate || ''}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                                    required
                                                />
                                                <p className="text-xs text-gray-500 mt-1">
                                                    {selectedContact.name}'s birthday is on {new Date(selectedContact.birthday).toLocaleDateString()}
                                                </p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Gift Ideas (comma-separated)
                                                </label>
                                                <input
                                                    type="text"
                                                    name="giftIdeas"
                                                    defaultValue={contactGiftReminder?.giftIdeas.join(', ') || ''}
                                                    placeholder="e.g., book, headphones, coffee subscription"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Budget (optional)
                                                </label>
                                                <input
                                                    type="number"
                                                    name="budget"
                                                    defaultValue={contactGiftReminder?.budget || ''}
                                                    placeholder="Enter budget amount"
                                                    step="0.01"
                                                    min="0"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Notes (optional)
                                                </label>
                                                <textarea
                                                    name="notes"
                                                    defaultValue={contactGiftReminder?.notes || ''}
                                                    placeholder="Any additional notes about gift preferences..."
                                                    rows={3}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                                />
                                            </div>

                                            <div className="flex space-x-3">
                                                <button
                                                    type="submit"
                                                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                                                >
                                                    {contactGiftReminder ? 'Update Reminder' : 'Create Reminder'}
                                                </button>
                                                {contactGiftReminder && (
                                                    <button
                                                        type="button"
                                                        onClick={async () => {
                                                            if (contactGiftReminder) {
                                                                try {
                                                                    await deleteGiftReminderMutation.mutateAsync({
                                                                        reminderId: contactGiftReminder.id,
                                                                        contactId: selectedContact.id
                                                                    });
                                                                    alert(`Gift reminder deleted for ${selectedContact.name}!`);
                                                                } catch (error) {
                                                                    alert('Failed to delete gift reminder. Please try again.');
                                                                    console.error('Error deleting gift reminder:', error);
                                                                }
                                                            }
                                                        }}
                                                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                                    >
                                                        Delete
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            )}

                        </div>
                    </div>
                )}

                {/* Edit Contact Modal */}
                {showEditModal && editingContact && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                            <div className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                        Edit Contact
                                    </h3>
                                    <button
                                        onClick={handleCancelEdit}
                                        className="text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Name *
                                        </label>
                                        <input
                                            type="text"
                                            value={editForm.name}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Birthday *
                                        </label>
                                        <input
                                            type="date"
                                            value={editForm.birthday}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, birthday: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Category
                                        </label>
                                        <select
                                            value={editForm.category}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value as Contact['category'] }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        >
                                            <option value="Family">Family</option>
                                            <option value="Friends">Friends</option>
                                            <option value="Colleagues">Colleagues</option>
                                            <option value="Clients">Clients</option>
                                            <option value="Members">Members</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Anniversary Date (Optional)
                                        </label>
                                        <input
                                            type="date"
                                            value={editForm.anniversaryDate}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryDate: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Anniversary Type
                                        </label>
                                        <select
                                            value={editForm.anniversaryType}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryType: e.target.value as Contact['anniversaryType'] }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        >
                                            <option value="Wedding">💒 Wedding</option>
                                            <option value="Dating">💕 Dating</option>
                                            <option value="Engagement">💍 Engagement</option>
                                            <option value="Business">🤝 Business</option>
                                            <option value="Other">🎉 Other</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Partner Name (Optional)
                                        </label>
                                        <input
                                            type="text"
                                            value={editForm.partnerName}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, partnerName: e.target.value }))}
                                            placeholder="Enter partner's name"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Anniversary Notes (Optional)
                                        </label>
                                        <textarea
                                            value={editForm.anniversaryNotes}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryNotes: e.target.value }))}
                                            placeholder="Add special notes about this anniversary..."
                                            rows={3}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                                        />
                                    </div>
                                </div>

                                <div className="flex justify-end space-x-3 mt-6">
                                    <button
                                        onClick={handleCancelEdit}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleSaveEdit}
                                        disabled={!editForm.name || !editForm.birthday || updateContactMutation.isPending}
                                        className="px-6 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                                    >
                                        {updateContactMutation.isPending ? 'Saving...' : 'Save Changes'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Delete Confirmation Modal */}
                {showDeleteModal && contactToDelete && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-2xl shadow-xl max-w-md w-full">
                            <div className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h3 className="text-xl font-bold text-red-600">
                                        Confirm Delete
                                    </h3>
                                    <button
                                        onClick={cancelDelete}
                                        className="text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <div className="mb-6">
                                    <div className="flex items-center mb-4">
                                        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900">
                                                Delete {contactToDelete.name}?
                                            </h4>
                                            <p className="text-gray-600">
                                                This action cannot be undone. All data for this contact will be permanently removed.
                                            </p>
                                        </div>
                                    </div>

                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <div className="text-sm text-gray-600">
                                            <p><strong>Name:</strong> {contactToDelete.name}</p>
                                            <p><strong>Birthday:</strong> {contactToDelete.birthday}</p>
                                            <p><strong>Category:</strong> {contactToDelete.category}</p>
                                            {contactToDelete.anniversaryDate && (
                                                <p><strong>Anniversary:</strong> {contactToDelete.anniversaryDate}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-end space-x-3">
                                    <button
                                        onClick={cancelDelete}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={confirmDelete}
                                        disabled={deleteContactMutation.isPending}
                                        className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                                    >
                                        {deleteContactMutation.isPending ? 'Deleting...' : 'Delete Contact'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

            </div>
        </div>
    );
};

export default ContactsPage;
