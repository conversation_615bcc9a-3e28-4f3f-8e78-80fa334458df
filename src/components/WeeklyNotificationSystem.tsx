import React, { useState, useEffect } from 'react';
import { format, addDays, startOfWeek, endOfWeek, differenceInYears } from 'date-fns';
import { useContactsQuery } from '../hooks/useContacts';
import { 
  useGenerateWeeklyNotification, 
  useSettingsQuery, 
  useUpdateSettings,
  useLandmarkBirthday 
} from '../hooks/useMessaging';

interface WeeklyNotificationSystemProps {
  onNavigate?: (page: string) => void;
}

const WeeklyNotificationSystem: React.FC<WeeklyNotificationSystemProps> = ({ onNavigate }) => {
  const { data: contacts = [] } = useContactsQuery();
  const { data: settings } = useSettingsQuery();
  const generateWeeklyNotification = useGenerateWeeklyNotification();
  const updateSettings = useUpdateSettings();
  
  const [previewMode, setPreviewMode] = useState(false);
  const [weeklyPreview, setWeeklyPreview] = useState<any>(null);

  // Generate preview of upcoming birthdays
  const generatePreview = () => {
    const today = new Date();
    const weekStart = startOfWeek(today, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
    
    const upcomingBirthdays = contacts
      .filter(contact => {
        const birthday = new Date(contact.birthday);
        const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
        return thisYearBirthday >= weekStart && thisYearBirthday <= weekEnd;
      })
      .map(contact => {
        const age = differenceInYears(today, new Date(contact.birthday)) + 1;
        const { isLandmark, landmark } = useLandmarkBirthday(age);
        
        return {
          ...contact,
          age,
          isLandmark,
          landmark: landmark?.name,
          formattedDate: format(new Date(contact.birthday), 'EEEE, MMMM do'),
        };
      })
      .sort((a, b) => new Date(a.birthday).getTime() - new Date(b.birthday).getTime());

    setWeeklyPreview({
      weekStart: format(weekStart, 'MMMM do, yyyy'),
      weekEnd: format(weekEnd, 'MMMM do, yyyy'),
      upcomingBirthdays,
      landmarkCount: upcomingBirthdays.filter(b => b.isLandmark).length,
    });
  };

  const handleSendWeeklyNotification = async () => {
    try {
      await generateWeeklyNotification.mutateAsync(contacts);
      alert('Weekly notification sent successfully!');
    } catch (error) {
      alert('Failed to send weekly notification');
      console.error(error);
    }
  };

  const handleSettingsUpdate = async (newSettings: any) => {
    try {
      await updateSettings.mutateAsync({
        weeklyAdminNotifications: {
          ...settings?.weeklyAdminNotifications,
          ...newSettings,
        },
      });
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  };

  useEffect(() => {
    if (previewMode) {
      generatePreview();
    }
  }, [previewMode, contacts]);

  if (!settings) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Weekly Admin Notifications</h3>
          <p className="text-sm text-gray-600">Automated weekly summaries of upcoming birthdays</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs rounded-full ${
            settings.weeklyAdminNotifications.enabled 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {settings.weeklyAdminNotifications.enabled ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>

      {/* Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notification Day
          </label>
          <select
            value={settings.weeklyAdminNotifications.dayOfWeek}
            onChange={(e) => handleSettingsUpdate({ dayOfWeek: parseInt(e.target.value) })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value={0}>Sunday</option>
            <option value={1}>Monday</option>
            <option value={2}>Tuesday</option>
            <option value={3}>Wednesday</option>
            <option value={4}>Thursday</option>
            <option value={5}>Friday</option>
            <option value={6}>Saturday</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notification Time
          </label>
          <input
            type="time"
            value={settings.weeklyAdminNotifications.time}
            onChange={(e) => handleSettingsUpdate({ time: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Admin Email
          </label>
          <input
            type="email"
            value={settings.weeklyAdminNotifications.email}
            onChange={(e) => handleSettingsUpdate({ email: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg mb-6">
        <div>
          <h4 className="font-medium text-gray-900">Enable Weekly Notifications</h4>
          <p className="text-sm text-gray-600">Automatically send weekly birthday summaries</p>
        </div>
        <button
          onClick={() => handleSettingsUpdate({ 
            enabled: !settings.weeklyAdminNotifications.enabled 
          })}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.weeklyAdminNotifications.enabled ? 'bg-purple-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.weeklyAdminNotifications.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 mb-6">
        <button
          onClick={() => setPreviewMode(!previewMode)}
          className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
        >
          {previewMode ? 'Hide Preview' : 'Preview This Week'}
        </button>
        <button
          onClick={handleSendWeeklyNotification}
          disabled={generateWeeklyNotification.isPending}
          className="flex-1 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
        >
          {generateWeeklyNotification.isPending ? 'Sending...' : 'Send Now'}
        </button>
      </div>

      {/* Preview */}
      {previewMode && weeklyPreview && (
        <div className="border-t pt-6">
          <h4 className="font-medium text-gray-900 mb-4">
            Weekly Summary Preview: {weeklyPreview.weekStart} - {weeklyPreview.weekEnd}
          </h4>
          
          {weeklyPreview.upcomingBirthdays.length === 0 ? (
            <p className="text-gray-600 italic">No birthdays this week</p>
          ) : (
            <div className="space-y-3">
              {weeklyPreview.upcomingBirthdays.map((birthday: any) => (
                <div
                  key={birthday.id}
                  className={`p-3 rounded-lg border-l-4 ${
                    birthday.isLandmark 
                      ? 'border-yellow-400 bg-yellow-50' 
                      : 'border-purple-400 bg-purple-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{birthday.name}</p>
                      <p className="text-sm text-gray-600">
                        {birthday.formattedDate} • Turning {birthday.age}
                        {birthday.isLandmark && (
                          <span className="ml-2 px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded-full">
                            🎊 {birthday.landmark}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{birthday.category}</p>
                    </div>
                  </div>
                </div>
              ))}
              
              {weeklyPreview.landmarkCount > 0 && (
                <div className="mt-4 p-3 bg-yellow-100 rounded-lg">
                  <p className="text-sm font-medium text-yellow-800">
                    🎊 {weeklyPreview.landmarkCount} landmark birthday{weeklyPreview.landmarkCount !== 1 ? 's' : ''} this week!
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WeeklyNotificationSystem;
