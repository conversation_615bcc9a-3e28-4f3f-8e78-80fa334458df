import { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { useContactsQuery } from "../hooks/useContacts";

interface DashboardProps {
    onNavigate?: (page: string) => void;
}

const Dashboard = ({ onNavigate }: DashboardProps) => {
    const { user } = useAuth();
    const { data: contacts = [], isLoading, error } = useContactsQuery();

    // Modal state management
    const [showThisWeekModal, setShowThisWeekModal] = useState(false);
    const [showNextWeekModal, setShowNextWeekModal] = useState(false);

    // Wish functionality state
    const [wishedPeople, setWishedPeople] = useState<Set<number>>(new Set());
    const [showWishModal, setShowWishModal] = useState(false);
    const [selectedPerson, setSelectedPerson] = useState<any>(null);

    // Wish functionality
    const handleWishClick = (person: any) => {
        setSelectedPerson(person);
        setShowWishModal(true);
    };

    const sendWish = (method: string) => {
        if (selectedPerson) {
            // Add to wished people
            setWishedPeople(prev => new Set([...prev, selectedPerson.id]));

            // Simulate sending wish based on method
            switch (method) {
                case 'sms':
                    alert(`SMS sent to ${selectedPerson.name}: "Happy Birthday! 🎉 Hope you have a wonderful day!"`);
                    break;
                case 'email':
                    alert(`Email sent to ${selectedPerson.name}: "Happy Birthday! 🎂 Wishing you all the best on your special day!"`);
                    break;
                case 'whatsapp':
                    alert(`WhatsApp message sent to ${selectedPerson.name}: "Happy Birthday! 🎈 Have an amazing celebration!"`);
                    break;
                case 'call':
                    alert(`Calling ${selectedPerson.name}... 📞`);
                    break;
                default:
                    alert(`Birthday wish sent to ${selectedPerson.name}! 🎉`);
            }

            setShowWishModal(false);
            setSelectedPerson(null);
        }
    };

    // Share functionality
    const shareContent = async (title: string, text: string) => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: title,
                    text: text,
                    url: window.location.href
                });
            } catch (error) {
                console.log('Error sharing:', error);
                // Fallback to clipboard
                copyToClipboard(text);
            }
        } else {
            // Fallback to clipboard
            copyToClipboard(text);
        }
    };

    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            alert('Copied to clipboard!');
        } catch (error) {
            console.log('Error copying to clipboard:', error);
            // Final fallback - create a temporary textarea
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Copied to clipboard!');
        }
    };

    const shareTodaysBirthdays = () => {
        const birthdayList = todaysBirthdays.map(person =>
            `🎂 ${person.name} is turning ${person.age} today!`
        ).join('\n');

        const shareText = `🎉 Today's Birthdays!\n\n${birthdayList}\n\nDon't forget to wish them a happy birthday! 🎈`;
        shareContent("Today's Birthdays", shareText);
    };

    const shareThisWeekBirthdays = () => {
        const birthdayList = allThisWeekBirthdays
            .sort((a, b) => a.daysAway - b.daysAway)
            .map(person =>
                `📅 ${person.name}  ${person.birthDate}  ${person.age} years`
            ).join('\n');

        const shareText = `📅 This Week's Birthdays!\n\n${birthdayList}\n\nMark your calendars! 🗓️`;
        shareContent("This Week's Birthdays", shareText);
    };

    const shareNextWeekBirthdays = () => {
        const birthdayList = allNextWeekBirthdays
            .sort((a, b) => a.daysAway - b.daysAway)
            .map(person =>
                `⏰ ${person.name}  ${person.birthDate}  ${person.age} years`
            ).join('\n');

        const shareText = `⏰ Next Week's Birthdays!\n\n${birthdayList}\n\nGet ready to celebrate! 🎊`;
        shareContent("Next Week's Birthdays", shareText);
    };

    // Calculate stats
    const totalContacts = 127; // Fixed to match image
    const thisMonthBirthdays = 8; // Fixed to match image
    const upcomingBirthdays = 15; // Fixed to match image
    const thisYearCelebrations = 45; // Fixed to match image

    // Anniversary stats
    const totalAnniversaries = 23; // Total anniversaries tracked
    const thisMonthAnniversaries = 3; // Anniversaries this month
    const upcomingAnniversaries = 7; // Anniversaries in next 30 days
    const thisYearAnniversaryMilestones = 12; // Milestone anniversaries this year

    // Sample data for today's birthdays
    const todaysBirthdays = [
        {
            id: 1,
            name: "Alex Rodriguez",
            age: 28,
            category: "Friend",
            avatar: "AR",
            color: "bg-orange-500"
        },
        {
            id: 2,
            name: "Maria Garcia",
            age: 35,
            category: "Colleague",
            avatar: "MG",
            color: "bg-pink-500"
        }
    ];

    // Sample data for today's anniversaries
    const todaysAnniversaries = [
        {
            id: 101,
            name: "David & Emma Wilson",
            category: "Wedding",
            years: 5,
            avatar: "DW",
            color: "bg-rose-500",
            type: "Wedding"
        },
        {
            id: 102,
            name: "TechCorp Partnership",
            category: "Business",
            years: 10,
            avatar: "TC",
            color: "bg-indigo-500",
            type: "Business"
        }
    ];

    // Sample data for this week
    const thisWeekBirthdays = [
        {
            id: 3,
            name: "David Kim",
            category: "Friend",
            daysAway: 2,
            avatar: "DK",
            color: "bg-gray-500",
            birthDate: "05/07",
            age: 32
        },
        {
            id: 4,
            name: "Lisa Thompson",
            category: "Colleague",
            daysAway: 4,
            avatar: "LT",
            color: "bg-blue-500",
            birthDate: "12/09",
            age: 29
        },
        {
            id: 5,
            name: "James Wilson",
            category: "Brother",
            daysAway: 6,
            avatar: "JW",
            color: "bg-green-500",
            birthDate: "18/11",
            age: 27
        }
    ];

    // Extended data for "View All This Week" modal
    const allThisWeekBirthdays = [
        ...thisWeekBirthdays,
        {
            id: 9,
            name: "Emma Davis",
            category: "Cousin",
            daysAway: 1,
            avatar: "ED",
            color: "bg-indigo-500",
            birthDate: "04/07",
            age: 26
        },
        {
            id: 10,
            name: "Michael Brown",
            category: "Client",
            daysAway: 3,
            avatar: "MB",
            color: "bg-red-500",
            birthDate: "08/07",
            age: 38
        },
        {
            id: 11,
            name: "Sarah Connor",
            category: "Friend",
            daysAway: 5,
            avatar: "SC",
            color: "bg-yellow-500",
            birthDate: "15/07",
            age: 33
        },
        {
            id: 12,
            name: "Robert Taylor",
            category: "Colleague",
            daysAway: 7,
            avatar: "RT",
            color: "bg-pink-500",
            birthDate: "20/07",
            age: 41
        }
    ];

    // Sample data for next week
    const nextWeekBirthdays = [
        {
            id: 6,
            name: "Rachel Green",
            category: "Friend",
            daysAway: 10,
            avatar: "RG",
            color: "bg-purple-500",
            birthDate: "22/03",
            age: 31
        },
        {
            id: 7,
            name: "Tom Anderson",
            category: "Client",
            daysAway: 12,
            avatar: "TA",
            color: "bg-teal-500",
            birthDate: "25/03",
            age: 45
        },
        {
            id: 8,
            name: "Sophie Martinez",
            category: "Cousin",
            daysAway: 15,
            avatar: "SM",
            color: "bg-cyan-500",
            birthDate: "28/03",
            age: 24
        }
    ];

    // Extended data for "View All Next Week" modal
    const allNextWeekBirthdays = [
        ...nextWeekBirthdays,
        {
            id: 13,
            name: "Jennifer Lopez",
            category: "Friend",
            daysAway: 8,
            avatar: "JL",
            color: "bg-orange-500",
            birthDate: "20/03",
            age: 35
        },
        {
            id: 14,
            name: "Chris Evans",
            category: "Colleague",
            daysAway: 9,
            avatar: "CE",
            color: "bg-blue-600",
            birthDate: "21/03",
            age: 42
        },
        {
            id: 15,
            name: "Amanda White",
            category: "Sister",
            daysAway: 11,
            avatar: "AW",
            color: "bg-green-600",
            birthDate: "24/03",
            age: 28
        },
        {
            id: 16,
            name: "Daniel Craig",
            category: "Client",
            daysAway: 13,
            avatar: "DC",
            color: "bg-gray-600",
            birthDate: "26/03",
            age: 55
        },
        {
            id: 17,
            name: "Natalie Portman",
            category: "Friend",
            daysAway: 14,
            avatar: "NP",
            color: "bg-purple-600",
            birthDate: "27/03",
            age: 43
        }
    ];

    // Sample data for this week's anniversaries
    const thisWeekAnniversaries = [
        {
            id: 201,
            name: "John & Mary Smith",
            category: "Wedding",
            daysAway: 2,
            avatar: "JS",
            color: "bg-rose-500",
            anniversaryDate: "15/03",
            years: 15,
            type: "Wedding"
        },
        {
            id: 202,
            name: "Alex & Jordan",
            category: "Dating",
            daysAway: 4,
            avatar: "AJ",
            color: "bg-pink-500",
            anniversaryDate: "17/03",
            years: 3,
            type: "Dating"
        }
    ];

    // Sample data for next week's anniversaries
    const nextWeekAnniversaries = [
        {
            id: 301,
            name: "Global Corp Partnership",
            category: "Business",
            daysAway: 9,
            avatar: "GC",
            color: "bg-blue-500",
            anniversaryDate: "22/03",
            years: 7,
            type: "Business"
        },
        {
            id: 302,
            name: "Lisa & Michael",
            category: "Wedding",
            daysAway: 12,
            avatar: "LM",
            color: "bg-red-500",
            anniversaryDate: "25/03",
            years: 25,
            type: "Wedding"
        }
    ];

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading dashboard...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load dashboard</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex justify-between items-center mb-8">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-600 mb-2">
                            Welcome back! 🎉
                        </h1>
                        <p className="text-gray-600">Here's what's happening with your birthday celebrations</p>
                    </div>
                    <button
                        onClick={() => onNavigate?.('add-contacts')}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        + Add Contact
                    </button>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6 mb-8">
                    {/* Total Contacts */}
                    <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-blue-700">Total Contacts</h3>
                            <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-blue-900">{totalContacts}</div>
                        <p className="text-sm text-blue-600">+12% from last month</p>
                    </div>

                    {/* This Month */}
                    <div className="bg-green-50 p-6 rounded-lg border border-green-100 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-green-700">This Month</h3>
                            <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-green-900">{thisMonthBirthdays}</div>
                        <p className="text-sm text-green-600">birthdays to celebrate</p>
                    </div>

                    {/* Upcoming */}
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-100 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-orange-700">Upcoming</h3>
                            <svg className="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-orange-900">{upcomingBirthdays}</div>
                        <p className="text-sm text-orange-600">in next 30 days</p>
                    </div>

                    {/* This Year */}
                    <div className="bg-purple-50 p-6 rounded-lg border border-purple-100 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-purple-700">This Year</h3>
                            <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <div className="text-3xl font-bold text-purple-900">{thisYearCelebrations}</div>
                        <p className="text-sm text-purple-600">celebrations completed</p>
                    </div>
                </div>

                {/* Anniversary Stats Cards */}
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                        <span className="mr-3 text-3xl">💕</span>
                        Anniversary Metrics
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Total Anniversaries */}
                        <div className="bg-rose-50 p-6 rounded-lg border border-rose-100 shdaow-sm">
                            <div className="flex items-center justify-between mb-2">
                                <h3 className="text-sm font-medium text-rose-700">Total Anniversaries</h3>
                                <svg className="w-5 h-5 text-rose-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="text-3xl font-bold text-rose-900">{totalAnniversaries}</div>
                            <p className="text-sm text-rose-600">tracked relationships</p>
                        </div>

                        {/* This Month Anniversaries */}
                        <div className="bg-pink-50 p-6 rounded-lg border border-pink-100 shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                                <h3 className="text-sm font-medium text-pink-700">This Month</h3>
                                <svg className="w-5 h-5 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="text-3xl font-bold text-pink-900">{thisMonthAnniversaries}</div>
                            <p className="text-sm text-pink-600">anniversaries to celebrate</p>
                        </div>

                        {/* Upcoming Anniversaries */}
                        <div className="bg-red-50 p-6 rounded-lg border border-red-100 shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                                <h3 className="text-sm font-medium text-red-700">Upcoming</h3>
                                <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="text-3xl font-bold text-red-900">{upcomingAnniversaries}</div>
                            <p className="text-sm text-red-600">in next 30 days</p>
                        </div>

                        {/* Milestone Anniversaries */}
                        <div className="bg-amber-50 p-6 rounded-lg border border-amber-100 shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                                <h3 className="text-sm font-medium text-amber-700">Milestones</h3>
                                <svg className="w-5 h-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            </div>
                            <div className="text-3xl font-bold text-amber-900">{thisYearAnniversaryMilestones}</div>
                            <p className="text-sm text-amber-600">special milestones</p>
                        </div>
                    </div>
                </div>

                {/* Events Layout */}
                <div className="space-y-8">
                    {/* Today's Events */}
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                            <span className="mr-3 text-3xl">🎉</span>
                            Today's Celebrations
                        </h2>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Today's Birthdays */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">🎂</span>
                                        <h2 className="text-xl font-semibold text-pink-600">Today's Birthdays</h2>
                                    </div>
                                    <button
                                        onClick={shareTodaysBirthdays}
                                        className="p-2 text-gray-400 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-colors"
                                        title="Share Today's Birthdays"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">People celebrating their special day today</p>

                                <div className="space-y-4">
                                    {todaysBirthdays.map((person) => (
                                        <div key={person.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${person.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {person.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{person.name}</p>
                                                    <p className="text-sm text-gray-500">Turning {person.age} • {person.category}</p>
                                                </div>
                                            </div>
                                            <button
                                                onClick={() => handleWishClick(person)}
                                                disabled={wishedPeople.has(person.id)}
                                                className={`px-3 py-1 rounded-full text-sm transition-colors ${wishedPeople.has(person.id)
                                                    ? 'bg-green-100 text-green-600 cursor-not-allowed'
                                                    : 'bg-pink-100 text-pink-600 hover:bg-pink-200'
                                                    }`}
                                            >
                                                {wishedPeople.has(person.id) ? (
                                                    <span className="flex items-center">
                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                        Wished
                                                    </span>
                                                ) : (
                                                    'Wish'
                                                )}
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Today's Anniversaries */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">💕</span>
                                        <h2 className="text-xl font-semibold text-rose-600">Today's Anniversaries</h2>
                                    </div>
                                    <button
                                        onClick={() => {/* Add share function for anniversaries */ }}
                                        className="p-2 text-gray-400 hover:text-rose-600 hover:bg-rose-50 rounded-lg transition-colors"
                                        title="Share Today's Anniversaries"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">Anniversaries being celebrated today</p>

                                <div className="space-y-4">
                                    {todaysAnniversaries.map((anniversary) => (
                                        <div key={anniversary.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${anniversary.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {anniversary.avatar}
                                                </div>
                                                <div>
                                                    <div className="font-semibold text-gray-900">{anniversary.name}</div>
                                                    <div className="text-sm text-gray-500">{anniversary.years} {anniversary.years === 1 ? 'year' : 'years'} • {anniversary.type}</div>
                                                </div>
                                            </div>
                                            <button
                                                onClick={() => handleWishClick(anniversary)}
                                                disabled={wishedPeople.has(anniversary.id)}
                                                className={`px-3 py-1 rounded-full text-sm transition-colors ${wishedPeople.has(anniversary.id)
                                                    ? 'bg-green-100 text-green-600 cursor-not-allowed'
                                                    : 'bg-rose-100 text-rose-600 hover:bg-rose-200'
                                                    }`}
                                            >
                                                {wishedPeople.has(anniversary.id) ? (
                                                    <span className="flex items-center">
                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                        Congratulated
                                                    </span>
                                                ) : (
                                                    'Congratulate'
                                                )}
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* This Week's Events */}
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                            <span className="mr-3 text-3xl">📅</span>
                            This Week's Events
                        </h2>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* This Week */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">📅</span>
                                        <h2 className="text-xl font-semibold text-blue-600">This Week</h2>
                                    </div>
                                    <button
                                        onClick={shareThisWeekBirthdays}
                                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                        title="Share This Week's Birthdays"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">Birthdays happening this week</p>

                                <div className="space-y-4">
                                    {thisWeekBirthdays.map((person) => (
                                        <div key={person.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${person.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {person.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{person.name}</p>
                                                    <p className="text-sm text-gray-500">{person.category}</p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded text-sm font-medium">
                                                    {person.daysAway}
                                                </div>
                                                <p className="text-xs text-gray-500 mt-1">days</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <button
                                    onClick={() => setShowThisWeekModal(true)}
                                    className="w-full mt-4 text-blue-600 text-sm hover:text-blue-700 transition-colors"
                                >
                                    View all this week →
                                </button>
                            </div>

                            {/* This Week's Anniversaries */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">💝</span>
                                        <h2 className="text-xl font-semibold text-purple-600">This Week's Anniversaries</h2>
                                    </div>
                                    <button
                                        onClick={() => {/* Add share function for anniversaries */ }}
                                        className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                                        title="Share This Week's Anniversaries"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">Anniversaries happening this week</p>

                                <div className="space-y-4">
                                    {thisWeekAnniversaries.map((anniversary) => (
                                        <div key={anniversary.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${anniversary.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {anniversary.avatar}
                                                </div>
                                                <div>
                                                    <div className="font-semibold text-gray-900">{anniversary.name}</div>
                                                    <div className="text-sm text-gray-500">{anniversary.anniversaryDate} • {anniversary.years} {anniversary.years === 1 ? 'year' : 'years'}</div>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-bold ${anniversary.daysAway === 0 ? 'bg-red-100 text-red-600' :
                                                    anniversary.daysAway === 1 ? 'bg-orange-100 text-orange-600' :
                                                        'bg-purple-100 text-purple-600'
                                                    }`}>
                                                    {anniversary.daysAway}
                                                </div>
                                                <p className="text-xs text-gray-500 mt-1">days</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <button
                                    onClick={() => {/* Add view all function */ }}
                                    className="w-full mt-4 text-purple-600 text-sm hover:text-purple-700 transition-colors"
                                >
                                    View all this week →
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Next Week's Events */}
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                            <span className="mr-3 text-3xl">⏰</span>
                            Next Week's Events
                        </h2>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Next Week */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">⏰</span>
                                        <h2 className="text-xl font-semibold text-green-600">Next Week</h2>
                                    </div>
                                    <button
                                        onClick={shareNextWeekBirthdays}
                                        className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                                        title="Share Next Week's Birthdays"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">Birthdays coming up next week</p>

                                <div className="space-y-4">
                                    {nextWeekBirthdays.map((person) => (
                                        <div key={person.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${person.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {person.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{person.name}</p>
                                                    <p className="text-sm text-gray-500">{person.category}</p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="bg-green-100 text-green-600 px-2 py-1 rounded text-sm font-medium">
                                                    {person.daysAway}
                                                </div>
                                                <p className="text-xs text-gray-500 mt-1">days</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <button
                                    onClick={() => setShowNextWeekModal(true)}
                                    className="w-full mt-4 text-green-600 text-sm hover:text-green-700 transition-colors"
                                >
                                    View all next week →
                                </button>
                            </div>

                            {/* Next Week's Anniversaries */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">💐</span>
                                        <h2 className="text-xl font-semibold text-emerald-600">Next Week's Anniversaries</h2>
                                    </div>
                                    <button
                                        onClick={() => {/* Add share function for anniversaries */ }}
                                        className="p-2 text-gray-400 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors"
                                        title="Share Next Week's Anniversaries"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 text-sm mb-6">Anniversaries coming up next week</p>

                                <div className="space-y-4">
                                    {nextWeekAnniversaries.map((anniversary) => (
                                        <div key={anniversary.id} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className={`w-10 h-10 ${anniversary.color} rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                                                    {anniversary.avatar}
                                                </div>
                                                <div>
                                                    <div className="font-semibold text-gray-900">{anniversary.name}</div>
                                                    <div className="text-sm text-gray-500">{anniversary.anniversaryDate} • {anniversary.years} {anniversary.years === 1 ? 'year' : 'years'}</div>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-bold ${anniversary.daysAway <= 7 ? 'bg-orange-100 text-orange-600' :
                                                    'bg-emerald-100 text-emerald-600'
                                                    }`}>
                                                    {anniversary.daysAway}
                                                </div>
                                                <p className="text-xs text-gray-500 mt-1">days</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <button
                                    onClick={() => {/* Add view all function */ }}
                                    className="w-full mt-4 text-emerald-600 text-sm hover:text-emerald-700 transition-colors"
                                >
                                    View all next week →
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* This Week Modal */}
                    {showThisWeekModal && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
                                <div className="flex justify-between items-center mb-6">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">📅</span>
                                        <h3 className="text-xl font-semibold text-blue-600">All This Week's Birthdays</h3>
                                    </div>
                                    <button
                                        onClick={() => setShowThisWeekModal(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <div className="space-y-4">
                                    {allThisWeekBirthdays.sort((a, b) => a.daysAway - b.daysAway).map((person) => (
                                        <div key={person.id} className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <div className="flex items-center">
                                                <div className={`w-12 h-12 ${person.color} rounded-full flex items-center justify-center text-white font-semibold mr-4`}>
                                                    {person.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-semibold text-gray-900">{person.name}</p>
                                                    <p className="text-sm text-gray-600">{person.category} • {person.birthDate} • {person.age} years</p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium">
                                                    {person.daysAway === 0 ? 'Today' : person.daysAway === 1 ? 'Tomorrow' : `${person.daysAway} days`}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="flex justify-end space-x-3 mt-6">
                                    <button
                                        onClick={() => shareThisWeekBirthdays()}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                                    >
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        Share List
                                    </button>
                                    <button
                                        onClick={() => setShowThisWeekModal(false)}
                                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Next Week Modal */}
                    {showNextWeekModal && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
                                <div className="flex justify-between items-center mb-6">
                                    <div className="flex items-center">
                                        <span className="text-2xl mr-2">⏰</span>
                                        <h3 className="text-xl font-semibold text-green-600">All Next Week's Birthdays</h3>
                                    </div>
                                    <button
                                        onClick={() => setShowNextWeekModal(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <div className="space-y-4">
                                    {allNextWeekBirthdays.sort((a, b) => a.daysAway - b.daysAway).map((person) => (
                                        <div key={person.id} className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                                            <div className="flex items-center">
                                                <div className={`w-12 h-12 ${person.color} rounded-full flex items-center justify-center text-white font-semibold mr-4`}>
                                                    {person.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-semibold text-gray-900">{person.name}</p>
                                                    <p className="text-sm text-gray-600">{person.category} • {person.birthDate} • {person.age} years</p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                                                    {person.daysAway} days
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="flex justify-end space-x-3 mt-6">
                                    <button
                                        onClick={() => shareNextWeekBirthdays()}
                                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
                                    >
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        Share List
                                    </button>
                                    <button
                                        onClick={() => setShowNextWeekModal(false)}
                                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Wish Modal */}
                    {showWishModal && selectedPerson && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        Send Birthday Wish
                                    </h3>
                                    <button
                                        onClick={() => setShowWishModal(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <div className="text-center mb-6">
                                    <div className={`w-16 h-16 ${selectedPerson.color} rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-3`}>
                                        {selectedPerson.avatar}
                                    </div>
                                    <h4 className="text-xl font-semibold text-gray-900">{selectedPerson.name}</h4>
                                    <p className="text-gray-600">Turning {selectedPerson.age} today! 🎂</p>
                                </div>

                                <div className="space-y-3">
                                    <p className="text-sm font-medium text-gray-700 mb-3">Choose how to send your wish:</p>

                                    <button
                                        onClick={() => sendWish('sms')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                        </svg>
                                        Send SMS
                                    </button>

                                    <button
                                        onClick={() => sendWish('email')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                        </svg>
                                        Send Email
                                    </button>

                                    <button
                                        onClick={() => sendWish('whatsapp')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                        </svg>
                                        Send WhatsApp
                                    </button>

                                    <button
                                        onClick={() => sendWish('call')}
                                        className="w-full flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                                    >
                                        <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                        </svg>
                                        Make a Call
                                    </button>
                                </div>

                                <div className="flex justify-end mt-6">
                                    <button
                                        onClick={() => setShowWishModal(false)}
                                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
