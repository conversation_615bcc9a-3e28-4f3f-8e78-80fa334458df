import React, { useState } from 'react';

interface TemplatePreviewProps {
  template: any;
  onClose: () => void;
  onEdit: () => void;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  onClose,
  onEdit
}) => {
  const [sampleData, setSampleData] = useState({
    name: '<PERSON>',
    partnerName: '<PERSON>',
    years: '5',
    senderName: 'Your Name'
  });

  // Function to replace variables in text with sample data
  const replaceVariables = (text: string) => {
    return text
      .replace(/\{name\}/g, sampleData.name)
      .replace(/\{partnerName\}/g, sampleData.partnerName)
      .replace(/\{years\}/g, sampleData.years)
      .replace(/\{senderName\}/g, sampleData.senderName);
  };

  const previewSubject = replaceVariables(template.subject || '');
  const previewMessage = replaceVariables(template.message || '');

  return (
    <div className="p-6">
      {/* Template Info */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Template Information</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Name:</span>
            <span className="ml-2 font-medium">{template.name}</span>
          </div>
          <div>
            <span className="text-gray-600">Type:</span>
            <span className="ml-2 font-medium capitalize">{template.type}</span>
          </div>
          <div>
            <span className="text-gray-600">Status:</span>
            <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
              template.isActive 
                ? 'bg-green-100 text-green-700' 
                : 'bg-red-100 text-red-700'
            }`}>
              {template.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Variables:</span>
            <span className="ml-2 text-xs text-gray-500">
              {template.variables?.join(', ') || 'None'}
            </span>
          </div>
        </div>
      </div>

      {/* Sample Data Editor */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">Sample Data for Preview</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={sampleData.name}
              onChange={(e) => setSampleData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Partner Name</label>
            <input
              type="text"
              value={sampleData.partnerName}
              onChange={(e) => setSampleData(prev => ({ ...prev, partnerName: e.target.value }))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Years</label>
            <input
              type="text"
              value={sampleData.years}
              onChange={(e) => setSampleData(prev => ({ ...prev, years: e.target.value }))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Sender Name</label>
            <input
              type="text"
              value={sampleData.senderName}
              onChange={(e) => setSampleData(prev => ({ ...prev, senderName: e.target.value }))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Email Preview */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 mb-3">Email Preview</h4>
        <div className="border border-gray-300 rounded-lg overflow-hidden">
          {/* Email Header */}
          <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <span className="text-gray-600">From:</span>
                <span className="font-medium">{sampleData.senderName} &lt;<EMAIL>&gt;</span>
              </div>
              <div className="text-gray-500">
                {new Date().toLocaleDateString()}
              </div>
            </div>
            <div className="mt-2 flex items-center space-x-4 text-sm">
              <span className="text-gray-600">To:</span>
              <span className="font-medium">{sampleData.name} &lt;{sampleData.name.toLowerCase().replace(' ', '.')}@example.com&gt;</span>
            </div>
            <div className="mt-2 flex items-center space-x-4 text-sm">
              <span className="text-gray-600">Subject:</span>
              <span className="font-medium">{previewSubject}</span>
            </div>
          </div>

          {/* Email Body */}
          <div className="p-4 bg-white">
            <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
              {previewMessage}
            </div>
          </div>
        </div>
      </div>

      {/* Raw Template */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 mb-3">Raw Template</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Subject Template</label>
            <div className="p-3 bg-gray-50 rounded border text-sm font-mono text-gray-800">
              {template.subject}
            </div>
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Message Template</label>
            <div className="p-3 bg-gray-50 rounded border text-sm font-mono text-gray-800 whitespace-pre-wrap max-h-32 overflow-y-auto">
              {template.message}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Close
        </button>
        <button
          onClick={onEdit}
          className="px-4 py-2 text-sm font-medium text-white bg-pink-600 border border-transparent rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500"
        >
          Edit Template
        </button>
      </div>
    </div>
  );
};

export default TemplatePreview;
