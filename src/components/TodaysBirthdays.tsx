import React from "react";
import { useContactsQuery } from "../hooks/useContacts";
import { format } from "date-fns";

const TodaysBirthdays = () => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const todayKey = format(new Date(), "MM-dd");

    const todays = contacts.filter(
        (c) => format(new Date(c.birthday), "MM-dd") === todayKey
    );

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Today's Birthdays</h2>
                <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
                    <p className="text-gray-500">Loading...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Today's Birthdays</h2>
                <div className="text-center py-8 text-red-500">
                    <div className="text-2xl mb-2">⚠️</div>
                    <p>Failed to load birthdays</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Today's Birthdays</h2>
            {todays.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-2">🎂</div>
                    <p>No birthdays today</p>
                    <p className="text-sm mt-1">Check back tomorrow!</p>
                </div>
            ) : (
                <div className="space-y-3">
                    {todays.map((c) => {
                        const age = new Date().getFullYear() - new Date(c.birthday).getFullYear();
                        return (
                            <div key={c.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                                <div className="flex items-center space-x-3">
                                    <div className="text-2xl">🎉</div>
                                    <div>
                                        <div className="font-semibold text-gray-800">{c.name}</div>
                                        <div className="text-sm text-gray-600">
                                            Turning {age} today • {c.category}
                                        </div>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <div className="text-sm font-medium text-orange-600">
                                        {format(new Date(c.birthday), "MMM d")}
                                    </div>
                                    <div className="text-xs text-gray-500">Birthday</div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
};

export default TodaysBirthdays;
