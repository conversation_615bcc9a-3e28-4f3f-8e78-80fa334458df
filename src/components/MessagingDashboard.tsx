import React, { useState } from 'react';
import { format } from 'date-fns';
import FeatureGate from './FeatureGate';
import {
  useMessagingStatsQuery,
  useMessageHistoryQuery,
  useScheduledMessagesQuery
} from '../hooks/useMessaging';
import MessageTemplateManager from './MessageTemplateManager';
import WeeklyNotificationSystem from './WeeklyNotificationSystem';
import AutomatedMessagingSystem from './AutomatedMessagingSystem';
import AnniversaryMessagingSystem from './AnniversaryMessagingSystem';
import LandmarkBirthdayDetector from './LandmarkBirthdayDetector';

interface MessagingDashboardProps {
  onNavigate?: (page: string) => void;
}

const MessagingDashboard: React.FC<MessagingDashboardProps> = ({ onNavigate }) => {
  const { data: stats } = useMessagingStatsQuery();
  const { data: messageHistory = [] } = useMessageHistoryQuery();
  const { data: scheduledMessages = [] } = useScheduledMessagesQuery();

  const [activeTab, setActiveTab] = useState<'overview' | 'templates' | 'automation' | 'anniversaries' | 'weekly' | 'landmarks' | 'history'>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'automation', label: 'Birthday Messages', icon: '🎂' },
    { id: 'anniversaries', label: 'Anniversary Messages', icon: '💕' },
    { id: 'landmarks', label: 'Landmark Birthdays', icon: '🎊' },
    { id: 'weekly', label: 'Weekly Notifications', icon: '📅' },
    { id: 'templates', label: 'Templates', icon: '📝' },
    { id: 'history', label: 'Message History', icon: '📋' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'birthday': return '🎂';
      case 'anniversary': return '💕';
      case 'landmark': return '🎊';
      case 'reminder': return '⏰';
      case 'weekly_admin': return '📅';
      default: return '📧';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <span className="text-2xl">📧</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Messages</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalMessagesSent}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <span className="text-2xl">🎂</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Birthday Messages</p>
                <p className="text-2xl font-bold text-gray-900">{stats.birthdayMessagesSent}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <span className="text-2xl">🎊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Landmark Messages</p>
                <p className="text-2xl font-bold text-gray-900">{stats.landmarkMessagesSent}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <span className="text-2xl">📈</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.successRate.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Messages */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
          <div className="space-y-3">
            {messageHistory.slice(0, 5).map(message => (
              <div key={message.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getTypeIcon(message.type)}</span>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{message.subject}</p>
                    <p className="text-xs text-gray-600">
                      {format(new Date(message.sentAt), 'MMM d, h:mm a')}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(message.status)}`}>
                  {message.status}
                </span>
              </div>
            ))}
            {messageHistory.length === 0 && (
              <p className="text-gray-500 text-center py-4">No messages sent yet</p>
            )}
          </div>
        </div>

        {/* Scheduled Messages */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Scheduled Messages</h3>
          <div className="space-y-3">
            {scheduledMessages.filter(msg => msg.status === 'pending').slice(0, 5).map(message => (
              <div key={message.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getTypeIcon(message.type)}</span>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {message.type.charAt(0).toUpperCase() + message.type.slice(1)} Message
                    </p>
                    <p className="text-xs text-gray-600">
                      Scheduled for {format(new Date(message.scheduledFor), 'MMM d, h:mm a')}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(message.status)}`}>
                  {message.status}
                </span>
              </div>
            ))}
            {scheduledMessages.filter(msg => msg.status === 'pending').length === 0 && (
              <p className="text-gray-500 text-center py-4">No scheduled messages</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderHistory = () => (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Message History</h3>

      {messageHistory.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-4">📧</div>
          <p className="text-lg font-medium mb-2">No messages sent yet</p>
          <p className="text-sm">Messages will appear here once you start sending birthday notifications.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {messageHistory.map(message => (
            <div key={message.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <span className="text-xl">{getTypeIcon(message.type)}</span>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900">{message.subject}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(message.status)}`}>
                        {message.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      To: {message.recipient}
                    </p>
                    <p className="text-sm text-gray-700 line-clamp-2">
                      {message.content.substring(0, 150)}
                      {message.content.length > 150 && '...'}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      Sent: {format(new Date(message.sentAt), 'MMMM d, yyyy at h:mm a')}
                    </p>
                    {message.errorMessage && (
                      <p className="text-xs text-red-600 mt-1">
                        Error: {message.errorMessage}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl mb-4 shadow-lg">
            <span className="text-3xl">📱</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent mb-3">
            Messaging Dashboard
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Manage automated birthday messages, templates, and intelligent notifications
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8 flex justify-center">
          <div className="flex space-x-2 bg-white/80 backdrop-blur-sm p-2 rounded-2xl shadow-lg border border-white/20">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${activeTab === tab.id
                  ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }`}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="min-h-96">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'templates' && (
            <FeatureGate feature="MESSAGE_TEMPLATES">
              <MessageTemplateManager />
            </FeatureGate>
          )}
          {activeTab === 'automation' && (
            <FeatureGate feature="AUTOMATED_MESSAGING">
              <AutomatedMessagingSystem />
            </FeatureGate>
          )}
          {activeTab === 'anniversaries' && (
            <FeatureGate feature="AUTOMATED_MESSAGING">
              <AnniversaryMessagingSystem />
            </FeatureGate>
          )}
          {activeTab === 'weekly' && (
            <FeatureGate feature="WEEKLY_NOTIFICATIONS">
              <WeeklyNotificationSystem onNavigate={onNavigate} />
            </FeatureGate>
          )}
          {activeTab === 'landmarks' && (
            <FeatureGate feature="LANDMARK_BIRTHDAYS">
              <LandmarkBirthdayDetector />
            </FeatureGate>
          )}
          {activeTab === 'history' && renderHistory()}
        </div>
      </div>
    </div>
  );
};

export default MessagingDashboard;
