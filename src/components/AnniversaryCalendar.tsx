import React, { useState, useMemo } from 'react';
import { useContactsQuery, useUpdateContact } from '../hooks/useContacts';
import { type Contact } from '../types';

interface AnniversaryEvent {
    id: string;
    name: string;
    partnerName?: string;
    anniversaryDate: string;
    anniversaryType: string;
    anniversaryNotes?: string;
    daysUntil: number;
    yearsMarried?: number;
}

const AnniversaryCalendar: React.FC = () => {
    const { data: contacts = [], isLoading } = useContactsQuery();
    const updateContactMutation = useUpdateContact();
    const [viewMode, setViewMode] = useState<'upcoming' | 'calendar' | 'all'>('upcoming');
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

    // Anniversary editing state
    const [editingAnniversary, setEditingAnniversary] = useState<Contact | null>(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editForm, setEditForm] = useState({
        anniversaryDate: '',
        anniversaryType: 'Wedding' as Contact['anniversaryType'],
        partnerName: '',
        anniversaryNotes: ''
    });

    // Calculate days until anniversary
    const calculateDaysUntilAnniversary = (anniversaryDate: string): number => {
        const today = new Date();
        const anniversary = new Date(anniversaryDate);

        // Set anniversary to current year
        anniversary.setFullYear(today.getFullYear());

        // If anniversary has passed this year, set to next year
        if (anniversary < today) {
            anniversary.setFullYear(today.getFullYear() + 1);
        }

        const diffTime = anniversary.getTime() - today.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    // Calculate years since anniversary
    const calculateYearsMarried = (anniversaryDate: string): number => {
        const today = new Date();
        const anniversary = new Date(anniversaryDate);
        return today.getFullYear() - anniversary.getFullYear();
    };

    // Sample anniversary data for demonstration
    const sampleAnniversaries: AnniversaryEvent[] = [
        {
            id: 'sample-1',
            name: 'John & Mary Smith',
            partnerName: 'Mary Smith',
            anniversaryDate: '2010-06-15',
            anniversaryType: 'Wedding',
            anniversaryNotes: 'Met at college',
            daysUntil: calculateDaysUntilAnniversary('2010-06-15'),
            yearsMarried: calculateYearsMarried('2010-06-15')
        },
        {
            id: 'sample-2',
            name: 'Alex & Jordan',
            partnerName: 'Jordan',
            anniversaryDate: '2020-03-20',
            anniversaryType: 'Dating',
            anniversaryNotes: 'Started dating during lockdown',
            daysUntil: calculateDaysUntilAnniversary('2020-03-20'),
            yearsMarried: calculateYearsMarried('2020-03-20')
        },
        {
            id: 'sample-3',
            name: 'TechCorp Partnership',
            anniversaryDate: '2018-09-10',
            anniversaryType: 'Business',
            anniversaryNotes: 'Strategic partnership began',
            daysUntil: calculateDaysUntilAnniversary('2018-09-10'),
            yearsMarried: calculateYearsMarried('2018-09-10')
        },
        {
            id: 'sample-4',
            name: 'David & Emma Wilson',
            partnerName: 'Emma Wilson',
            anniversaryDate: '2015-12-25',
            anniversaryType: 'Wedding',
            anniversaryNotes: 'Christmas wedding',
            daysUntil: calculateDaysUntilAnniversary('2015-12-25'),
            yearsMarried: calculateYearsMarried('2015-12-25')
        },
        {
            id: 'sample-5',
            name: 'Lisa & Michael',
            partnerName: 'Michael',
            anniversaryDate: '1999-07-04',
            anniversaryType: 'Wedding',
            anniversaryNotes: 'Independence Day wedding',
            daysUntil: calculateDaysUntilAnniversary('1999-07-04'),
            yearsMarried: calculateYearsMarried('1999-07-04')
        }
    ];

    // Get anniversary events from contacts and combine with sample data
    const anniversaryEvents: AnniversaryEvent[] = useMemo(() => {
        const realAnniversaries = contacts
            .filter(contact => contact.anniversaryDate)
            .map(contact => ({
                id: contact.id,
                name: contact.name,
                partnerName: contact.partnerName,
                anniversaryDate: contact.anniversaryDate!,
                anniversaryType: contact.anniversaryType || 'Wedding',
                anniversaryNotes: contact.anniversaryNotes,
                daysUntil: calculateDaysUntilAnniversary(contact.anniversaryDate!),
                yearsMarried: calculateYearsMarried(contact.anniversaryDate!)
            }));

        return [...sampleAnniversaries, ...realAnniversaries]
            .sort((a, b) => a.daysUntil - b.daysUntil);
    }, [contacts]);

    // Filter events based on view mode
    const filteredEvents = useMemo(() => {
        switch (viewMode) {
            case 'upcoming':
                return anniversaryEvents.filter(event => event.daysUntil <= 90);
            case 'calendar':
                return anniversaryEvents.filter(event => {
                    const eventDate = new Date(event.anniversaryDate);
                    return eventDate.getMonth() === selectedMonth;
                });
            case 'all':
                return anniversaryEvents;
            default:
                return anniversaryEvents;
        }
    }, [anniversaryEvents, viewMode, selectedMonth]);

    // Get milestone anniversaries (special years)
    const getMilestoneIcon = (years: number): string => {
        if (years === 1) return '🥇'; // First anniversary
        if (years === 5) return '🌟'; // 5 years
        if (years === 10) return '💎'; // 10 years
        if (years === 15) return '🌹'; // 15 years
        if (years === 20) return '🏆'; // 20 years
        if (years === 25) return '🥈'; // Silver
        if (years === 50) return '🥇'; // Golden
        if (years >= 60) return '💍'; // Diamond+
        return '💕'; // Regular anniversary
    };

    const getAnniversaryTypeIcon = (type: string): string => {
        switch (type) {
            case 'Wedding': return '💒';
            case 'Dating': return '💕';
            case 'Engagement': return '💍';
            case 'Business': return '🤝';
            default: return '🎉';
        }
    };

    // Anniversary editing functions
    const handleEditAnniversary = (contact: Contact) => {
        setEditingAnniversary(contact);
        setEditForm({
            anniversaryDate: contact.anniversaryDate || '',
            anniversaryType: contact.anniversaryType || 'Wedding',
            partnerName: contact.partnerName || '',
            anniversaryNotes: contact.anniversaryNotes || ''
        });
        setShowEditModal(true);
    };

    const handleSaveAnniversary = async () => {
        if (!editingAnniversary) return;

        try {
            await updateContactMutation.mutateAsync({
                contactId: editingAnniversary.id,
                updates: {
                    anniversaryDate: editForm.anniversaryDate,
                    anniversaryType: editForm.anniversaryType,
                    partnerName: editForm.partnerName,
                    anniversaryNotes: editForm.anniversaryNotes
                }
            });
            setShowEditModal(false);
            setEditingAnniversary(null);
        } catch (error) {
            console.error('Failed to update anniversary:', error);
        }
    };

    const handleCancelEdit = () => {
        setShowEditModal(false);
        setEditingAnniversary(null);
        setEditForm({
            anniversaryDate: '',
            anniversaryType: 'Wedding',
            partnerName: '',
            anniversaryNotes: ''
        });
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-6xl mx-auto">
                    <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                        <div className="space-y-4">
                            {[...Array(5)].map((_, i) => (
                                <div key={i} className="h-20 bg-gray-200 rounded"></div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 p-6">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-rose-500 to-pink-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">💕</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 bg-clip-text text-transparent mb-3">
                        Anniversary Calendar
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Track and celebrate important relationship milestones with beautiful anniversary management
                    </p>
                </div>

                {/* View Mode Selector */}
                <div className="mb-8 flex justify-center">
                    <div className="flex space-x-2 bg-white/80 backdrop-blur-sm p-2 rounded-2xl shadow-lg border border-white/20">
                        {[
                            { id: 'upcoming', label: 'Upcoming', icon: '📅', color: 'from-blue-500 to-indigo-500' },
                            { id: 'calendar', label: 'This Month', icon: '🗓️', color: 'from-purple-500 to-pink-500' },
                            { id: 'all', label: 'All Anniversaries', icon: '💕', color: 'from-rose-500 to-red-500' }
                        ].map((mode) => (
                            <button
                                key={mode.id}
                                onClick={() => setViewMode(mode.id as any)}
                                className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${viewMode === mode.id
                                    ? `bg-gradient-to-r ${mode.color} text-white shadow-lg`
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                    }`}
                            >
                                <span className="mr-2 text-lg">{mode.icon}</span>
                                {mode.label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Month Selector for Calendar View */}
                {viewMode === 'calendar' && (
                    <div className="mb-8 flex justify-center">
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-2">
                            <select
                                value={selectedMonth}
                                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                                className="px-6 py-3 bg-transparent border-none text-lg font-semibold text-gray-700 focus:outline-none focus:ring-0 cursor-pointer"
                            >
                                {Array.from({ length: 12 }, (_, i) => (
                                    <option key={i} value={i}>
                                        {new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'long' })} {selectedYear}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                )}

                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-rose-500 to-pink-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">💕</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Anniversaries</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">{anniversaryEvents.length}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">🎉</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">This Month</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                                    {anniversaryEvents.filter(event => {
                                        const eventDate = new Date(event.anniversaryDate);
                                        return eventDate.getMonth() === new Date().getMonth();
                                    }).length}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">⏰</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Next 30 Days</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                                    {anniversaryEvents.filter(event => event.daysUntil <= 30).length}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-yellow-500 to-amber-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">🏆</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Milestones</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-amber-600 bg-clip-text text-transparent">
                                    {anniversaryEvents.filter(event =>
                                        [1, 5, 10, 15, 20, 25, 50].includes(event.yearsMarried || 0)
                                    ).length}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Anniversary List */}
                {filteredEvents.length === 0 ? (
                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-12 text-center">
                        <div className="text-8xl mb-6">💕</div>
                        <h3 className="text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent mb-4">No Anniversaries Found</h3>
                        <p className="text-lg text-gray-600 max-w-md mx-auto">
                            {viewMode === 'upcoming'
                                ? "No anniversaries in the next 90 days. Your relationships are all set for now!"
                                : viewMode === 'calendar'
                                    ? "No anniversaries this month. Check other months for upcoming celebrations."
                                    : "No anniversary data available. Add anniversary information to your contacts to start tracking special moments!"}
                        </p>
                    </div>
                ) : (
                    <div className="space-y-6">
                        {filteredEvents.map((event) => (
                            <div key={event.id} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-6">
                                        <div className="relative">
                                            <div className="w-20 h-20 bg-gradient-to-br from-rose-500 via-pink-500 to-purple-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                                                {event.name.charAt(0).toUpperCase()}
                                            </div>
                                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center shadow-lg">
                                                <span className="text-sm">{getAnniversaryTypeIcon(event.anniversaryType)}</span>
                                            </div>
                                        </div>
                                        <div>
                                            <h3 className="text-xl font-bold text-gray-900 mb-1">
                                                {event.name}
                                                {event.partnerName && ` & ${event.partnerName}`}
                                            </h3>
                                            <div className="flex items-center space-x-3 text-sm font-medium text-gray-600 mb-2">
                                                <div className="flex items-center space-x-1">
                                                    <span className="text-lg">{getAnniversaryTypeIcon(event.anniversaryType)}</span>
                                                    <span>{event.anniversaryType} Anniversary</span>
                                                </div>
                                                <span className="text-gray-400">•</span>
                                                <span className="text-gray-700">{new Date(event.anniversaryDate).toLocaleDateString('en-US', {
                                                    month: 'long',
                                                    day: 'numeric',
                                                    year: 'numeric'
                                                })}</span>
                                            </div>
                                            {event.anniversaryNotes && (
                                                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 mt-2">
                                                    <p className="text-sm text-gray-700 italic">"{event.anniversaryNotes}"</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <div className="text-right flex flex-col items-end space-y-3">
                                        <div className="flex items-center space-x-3">
                                            <span className="text-3xl">{getMilestoneIcon(event.yearsMarried || 0)}</span>
                                            <div className="text-right">
                                                <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                                    {event.yearsMarried}
                                                </div>
                                                <div className="text-sm font-medium text-gray-600">
                                                    {event.yearsMarried === 1 ? 'Year' : 'Years'}
                                                </div>
                                            </div>
                                        </div>
                                        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-bold shadow-lg ${event.daysUntil === 0 ? 'bg-gradient-to-r from-pink-500 to-rose-500 text-white' :
                                            event.daysUntil === 1 ? 'bg-gradient-to-r from-rose-500 to-red-500 text-white' :
                                                event.daysUntil <= 7 ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' :
                                                    event.daysUntil <= 30 ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' :
                                                        'bg-gradient-to-r from-gray-400 to-gray-500 text-white'
                                            }`}>
                                            {event.daysUntil === 0 ? '💕 Today!' :
                                                event.daysUntil === 1 ? '💖 Tomorrow' :
                                                    `💝 ${event.daysUntil} days`}
                                        </div>

                                        {/* Edit Button - Only show for real contacts (not sample data) */}
                                        {!event.id.startsWith('sample-') && (
                                            <button
                                                onClick={() => {
                                                    const contact = contacts.find(c => c.id === event.id);
                                                    if (contact) handleEditAnniversary(contact);
                                                }}
                                                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                                            >
                                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                Edit
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Anniversary Edit Modal */}
            {showEditModal && editingAnniversary && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">
                                    Edit Anniversary Details
                                </h3>
                                <button
                                    onClick={handleCancelEdit}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Name
                                    </label>
                                    <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-600">
                                        {editingAnniversary.name}
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Anniversary Date
                                    </label>
                                    <input
                                        type="date"
                                        value={editForm.anniversaryDate}
                                        onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryDate: e.target.value }))}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Anniversary Type
                                    </label>
                                    <select
                                        value={editForm.anniversaryType}
                                        onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryType: e.target.value as Contact['anniversaryType'] }))}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    >
                                        <option value="Wedding">💒 Wedding</option>
                                        <option value="Dating">💕 Dating</option>
                                        <option value="Engagement">💍 Engagement</option>
                                        <option value="Business">🤝 Business</option>
                                        <option value="Other">🎉 Other</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Partner Name (Optional)
                                    </label>
                                    <input
                                        type="text"
                                        value={editForm.partnerName}
                                        onChange={(e) => setEditForm(prev => ({ ...prev, partnerName: e.target.value }))}
                                        placeholder="Enter partner's name"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Anniversary Notes (Optional)
                                    </label>
                                    <textarea
                                        value={editForm.anniversaryNotes}
                                        onChange={(e) => setEditForm(prev => ({ ...prev, anniversaryNotes: e.target.value }))}
                                        placeholder="Add special notes about this anniversary..."
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end space-x-3 mt-6">
                                <button
                                    onClick={handleCancelEdit}
                                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleSaveAnniversary}
                                    disabled={!editForm.anniversaryDate || updateContactMutation.isPending}
                                    className="px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                                >
                                    {updateContactMutation.isPending ? 'Saving...' : 'Save Anniversary'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnniversaryCalendar;
