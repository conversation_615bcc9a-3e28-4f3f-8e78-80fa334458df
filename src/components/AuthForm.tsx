import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { useFormValidation } from "../hooks/useFormValidation";
import <PERSON><PERSON>ield from "./FormField";

interface AuthFormData {
    email: string;
    password: string;
}

const AuthForm = () => {
    const { login, signup } = useAuth();
    const [isSignup, setIsSignup] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const {
        values,
        errors,
        touched,
        isValid,
        isSubmitting,
        setValue,
        setFieldTouched,
        handleSubmit,
        resetForm,
    } = useFormValidation<AuthFormData>(
        { email: '', password: '' },
        {
            email: {
                required: true,
                email: true,
            },
            password: {
                required: true,
                minLength: 6,
            },
        }
    );

    const onSubmit = async (formData: AuthFormData) => {
        try {
            let success = false;
            if (isSignup) {
                success = await signup(formData.email, formData.password);
                if (!success) {
                    throw new Error("User already exists");
                }
            } else {
                success = await login(formData.email, formData.password);
                if (!success) {
                    throw new Error("Invalid email or password");
                }
            }
        } catch (error) {
            console.error("Auth error:", error);
            throw error; // Let the form validation handle the error display
        }
    };

    const toggleMode = () => {
        setIsSignup(!isSignup);
        resetForm();
    };

    const fillTestCredentials = (userType: 'admin' | 'church' | 'test') => {
        if (userType === 'admin') {
            setValue('email', '<EMAIL>');
            setValue('password', 'admin123');
        } else if (userType === 'church') {
            setValue('email', '<EMAIL>');
            setValue('password', 'church123');
        } else {
            setValue('email', '<EMAIL>');
            setValue('password', 'TestUser123');
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="bg-white p-8 rounded-lg shadow-md">
                    <h2 className="text-2xl font-semibold mb-6 text-center">{isSignup ? "Sign Up" : "Log In"}</h2>

                    {/* Test Credentials Section */}
                    {!isSignup && (
                        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h3 className="text-sm font-medium text-blue-800 mb-3">🧪 Test Credentials</h3>
                            <div className="space-y-2">
                                <button
                                    type="button"
                                    onClick={() => fillTestCredentials('admin')}
                                    className="w-full text-left px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 rounded border border-blue-300 transition-colors"
                                >
                                    <div className="font-medium text-blue-900">System Administrator</div>
                                    <div className="text-blue-700 text-xs"><EMAIL></div>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => fillTestCredentials('church')}
                                    className="w-full text-left px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 rounded border border-blue-300 transition-colors"
                                >
                                    <div className="font-medium text-blue-900">Church Administrator</div>
                                    <div className="text-blue-700 text-xs"><EMAIL></div>
                                </button>
                            </div>
                        </div>
                    )}

                    {isSignup && (
                        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <h3 className="text-sm font-medium text-green-800 mb-3">🧪 Test Signup</h3>
                            <button
                                type="button"
                                onClick={() => fillTestCredentials('test')}
                                className="w-full text-left px-3 py-2 text-sm bg-green-100 hover:bg-green-200 rounded border border-green-300 transition-colors"
                            >
                                <div className="font-medium text-green-900">Fill Test User Data</div>
                                <div className="text-green-700 text-xs"><EMAIL></div>
                            </button>
                        </div>
                    )}
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            label="Email"
                            name="email"
                            type="email"
                            value={values.email}
                            onChange={(value) => setValue('email', value)}
                            onBlur={() => setFieldTouched('email')}
                            error={errors.email}
                            touched={touched.email}
                            required
                            placeholder="Enter your email"
                        />

                        <div className="relative">
                            <FormField
                                label="Password"
                                name="password"
                                type={showPassword ? "text" : "password"}
                                value={values.password}
                                onChange={(value) => setValue('password', value)}
                                onBlur={() => setFieldTouched('password')}
                                error={errors.password}
                                touched={touched.password}
                                required
                                minLength={6}
                                placeholder="Enter your password"
                            />
                            <div className="absolute right-0 bottom-0 mb-4 mr-3 flex items-center">
                                <label className="flex items-center text-sm text-gray-600 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className="mr-1 h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                        checked={showPassword}
                                        onChange={() => setShowPassword(!showPassword)}
                                    />
                                    Show
                                </label>
                            </div>
                        </div>

                        <button
                            type="submit"
                            disabled={isSubmitting || !isValid}
                            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting ? (isSignup ? "Creating Account..." : "Logging in...") : (isSignup ? "Sign Up" : "Log In")}
                        </button>
                    </form>
                    <p className="mt-4 text-center">
                        {isSignup ? "Already have an account?" : "Don't have an account?"}{" "}
                        <button
                            className="text-blue-600 underline hover:text-blue-800"
                            onClick={toggleMode}
                            type="button"
                        >
                            {isSignup ? "Log In" : "Sign Up"}
                        </button>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default AuthForm;
