import { useState, useMemo, useEffect } from 'react';
import { useContactsQuery, useUpdateContact, useCreateContact, useDeleteContact } from '../hooks/useContacts';
import { type Contact } from '../types';

interface ValidationPageProps {
    onNavigate?: (page: string) => void;
}

type ValidationStatus = 'all' | 'confirmed' | 'unconfirmed';

const ValidationPage = ({ onNavigate }: ValidationPageProps) => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const updateContactMutation = useUpdateContact();
    const createContactMutation = useCreateContact();
    const deleteContactMutation = useDeleteContact();

    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState<ValidationStatus>('all');
    const [editingContact, setEditingContact] = useState<Contact | null>(null);
    const [newBirthday, setNewBirthday] = useState('');
    const [editingCategory, setEditingCategory] = useState<Contact | null>(null);
    const [newCategory, setNewCategory] = useState<Contact['category']>('Friends');
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [showAddContactDialog, setShowAddContactDialog] = useState(false);
    const [newContactData, setNewContactData] = useState({
        name: '',
        birthday: '',
        category: 'Friends' as Contact['category']
    });
    const [duplicates, setDuplicates] = useState<Contact[][]>([]);
    const [showDuplicatesModal, setShowDuplicatesModal] = useState(false);
    const [isCheckingDuplicates, setIsCheckingDuplicates] = useState(false);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [contactsPerPage, setContactsPerPage] = useState(10);

    // Filter and search contacts
    const filteredContacts = useMemo(() => {
        let filtered = contacts;

        // Apply status filter
        if (statusFilter === 'confirmed') {
            filtered = filtered.filter(contact => contact.confirmed === true);
        } else if (statusFilter === 'unconfirmed') {
            filtered = filtered.filter(contact => contact.confirmed !== true);
        }

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(contact =>
                contact.name.toLowerCase().includes(query)
            );
        }

        return filtered.sort((a, b) => a.name.localeCompare(b.name));
    }, [contacts, searchQuery, statusFilter]);

    // Statistics
    const stats = useMemo(() => {
        const total = contacts.length;
        const confirmed = contacts.filter(c => c.confirmed === true).length;
        const unconfirmed = total - confirmed;
        const confirmationRate = total > 0 ? Math.round((confirmed / total) * 100) : 0;

        return { total, confirmed, unconfirmed, confirmationRate };
    }, [contacts]);

    // Pagination calculations
    const totalPages = Math.ceil(filteredContacts.length / contactsPerPage);
    const startIndex = (currentPage - 1) * contactsPerPage;
    const endIndex = startIndex + contactsPerPage;
    const paginatedContacts = filteredContacts.slice(startIndex, endIndex);

    // Reset to first page when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchQuery, statusFilter, contactsPerPage]);

    const handleCorrect = async (contact: Contact) => {
        try {
            await updateContactMutation.mutateAsync({
                contactId: contact.id,
                updates: { confirmed: true }
            });
        } catch (error) {
            console.error('Failed to confirm contact:', error);
        }
    };

    const handleNotCorrect = (contact: Contact) => {
        setEditingContact(contact);
        setNewBirthday(contact.birthday);
        setShowConfirmDialog(true);
    };

    const handleBirthdayUpdate = async () => {
        if (!editingContact || !newBirthday) return;

        try {
            await updateContactMutation.mutateAsync({
                contactId: editingContact.id,
                updates: {
                    birthday: newBirthday,
                    confirmed: true
                }
            });
            setShowConfirmDialog(false);
            setEditingContact(null);
            setNewBirthday('');
        } catch (error) {
            console.error('Failed to update birthday:', error);
        }
    };

    // Duplicate detection functions
    const findDuplicates = () => {
        setIsCheckingDuplicates(true);

        const duplicateGroups: Contact[][] = [];
        const processed = new Set<string>();

        contacts.forEach((contact, index) => {
            if (processed.has(contact.id)) return;

            const duplicatesForContact = contacts.filter((otherContact, otherIndex) => {
                if (index === otherIndex || processed.has(otherContact.id)) return false;

                // Check for exact name match
                const nameMatch = contact.name.toLowerCase().trim() === otherContact.name.toLowerCase().trim();

                // Check for similar names (fuzzy matching)
                const nameSimilarity = calculateSimilarity(contact.name.toLowerCase(), otherContact.name.toLowerCase());
                const similarName = nameSimilarity > 0.8;

                // Check for same birthday
                const birthdayMatch = contact.birthday === otherContact.birthday;

                return (nameMatch || similarName) && birthdayMatch;
            });

            if (duplicatesForContact.length > 0) {
                const group = [contact, ...duplicatesForContact];
                duplicateGroups.push(group);

                // Mark all contacts in this group as processed
                group.forEach(c => processed.add(c.id));
            }
        });

        setDuplicates(duplicateGroups);
        setIsCheckingDuplicates(false);
        setShowDuplicatesModal(true);
    };

    // Simple string similarity calculation (Levenshtein distance)
    const calculateSimilarity = (str1: string, str2: string): number => {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

        for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j][i - 1] + 1,
                    matrix[j - 1][i] + 1,
                    matrix[j - 1][i - 1] + indicator
                );
            }
        }

        const distance = matrix[str2.length][str1.length];
        const maxLength = Math.max(str1.length, str2.length);
        return maxLength === 0 ? 1 : 1 - distance / maxLength;
    };

    const handlePurgeDuplicate = async (contactId: string) => {
        try {
            await deleteContactMutation.mutateAsync(contactId);

            // Update duplicates list by removing the deleted contact
            setDuplicates(prev => {
                const updatedGroups = prev.map(group => group.filter(c => c.id !== contactId))
                    .filter(group => group.length > 1); // Remove groups with only one contact left

                return updatedGroups;
            });
        } catch (error) {
            console.error('Failed to delete duplicate contact:', error);
        }
    };

    const handleEditCategory = (contact: Contact) => {
        setEditingCategory(contact);
        setNewCategory(contact.category);
    };

    const handleCategoryUpdate = async () => {
        if (!editingCategory) return;

        try {
            await updateContactMutation.mutateAsync({
                contactId: editingCategory.id,
                updates: { category: newCategory }
            });
            setEditingCategory(null);
        } catch (error) {
            console.error('Failed to update category:', error);
        }
    };

    const handleAddContact = async () => {
        if (!newContactData.name || !newContactData.birthday) return;

        try {
            await createContactMutation.mutateAsync({
                ...newContactData,
                confirmed: true
            });
            setShowAddContactDialog(false);
            setNewContactData({ name: '', birthday: '', category: 'Friends' });
        } catch (error) {
            console.error('Failed to add contact:', error);
        }
    };

    const formatBirthday = (birthday: string) => {
        try {
            const date = new Date(birthday);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        } catch {
            return birthday;
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading contacts for validation...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load contacts</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-50 p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">✅</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-cyan-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                        Data Validation
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Verify and confirm birthday information after data migration
                    </p>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">👥</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Contacts</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">{stats.total}</p>
                                <p className="text-sm text-gray-500">Contacts to validate</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">✅</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Confirmed</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">{stats.confirmed}</p>
                                <p className="text-sm text-gray-500">{stats.confirmationRate}% completion rate</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">⏳</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Pending</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">{stats.unconfirmed}</p>
                                <p className="text-sm text-gray-500">Awaiting validation</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <div className="flex items-center">
                            <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                                <span className="text-2xl">📊</span>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Progress</p>
                                <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">{stats.confirmationRate}%</p>
                                <p className="text-sm text-gray-500">Validation complete</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Search and Filter Controls */}
                <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                    <div className="flex flex-col sm:flex-row gap-4 items-center">
                        {/* Results Count */}
                        <div className="text-sm text-gray-600 font-medium">
                            {filteredContacts.length} of {contacts.length} contacts
                        </div>
                        {/* Search */}
                        <div className="flex-1">
                            <div className="relative">
                                <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input
                                    type="text"
                                    placeholder="Search contacts by name..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                                />
                            </div>
                        </div>

                        {/* Status Filter */}
                        <div className="sm:w-48">
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value as ValidationStatus)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                            >
                                <option value="all">All Contacts</option>
                                <option value="unconfirmed">Pending Validation</option>
                                <option value="confirmed">Confirmed</option>
                            </select>
                        </div>

                        {/* Add Missing Contact Button */}
                        <button
                            onClick={() => setShowAddContactDialog(true)}
                            className="px-6 py-3 bg-gradient-to-r from-cyan-600 to-blue-600 text-white rounded-xl hover:from-cyan-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center space-x-2 font-semibold"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span>Add Missing Contact</span>
                        </button>

                        {/* Check Duplicates Button */}
                        <button
                            onClick={findDuplicates}
                            disabled={isCheckingDuplicates}
                            className="px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-xl hover:from-orange-700 hover:to-red-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center space-x-2 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isCheckingDuplicates ? (
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                            ) : (
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                            )}
                            <span>{isCheckingDuplicates ? 'Checking...' : 'Check Duplicates'}</span>
                        </button>
                    </div>
                </div>

                {/* Contacts List */}
                <div className="bg-white rounded-lg shadow-sm border">
                    <div className="p-6 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">
                            Contacts Validation ({filteredContacts.length})
                        </h2>
                        <p className="text-sm text-gray-600 mt-1">
                            Review and confirm birthday information for each contact
                        </p>
                    </div>

                    {filteredContacts.length === 0 ? (
                        <div className="p-12 text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                {searchQuery ? 'No contacts found' : 'No contacts to validate'}
                            </h3>
                            <p className="text-gray-600">
                                {searchQuery
                                    ? 'Try adjusting your search terms or filters.'
                                    : 'All contacts have been validated or no contacts exist.'
                                }
                            </p>
                        </div>
                    ) : (
                        <div className="divide-y divide-gray-200">
                            {paginatedContacts.map((contact) => (
                                <div key={contact.id} className="p-6 hover:bg-gray-50 transition-colors">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-4">
                                                <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold ${contact.confirmed
                                                    ? 'bg-green-500'
                                                    : 'bg-orange-500'
                                                    }`}>
                                                    {contact.name.charAt(0).toUpperCase()}
                                                </div>
                                                <div className="flex-1">
                                                    <div className="flex items-center space-x-3">
                                                        <h3 className="text-lg font-semibold text-gray-900">
                                                            {contact.name}
                                                        </h3>
                                                        {contact.confirmed && (
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                                </svg>
                                                                Confirmed
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-4 mt-1">
                                                        <p className="text-sm text-gray-600">
                                                            <span className="font-medium">Birthday:</span> {formatBirthday(contact.birthday)}
                                                        </p>
                                                        <div className="flex items-center space-x-2">
                                                            <p className="text-sm text-gray-600">
                                                                <span className="font-medium">Category:</span> {contact.category}
                                                            </p>
                                                            <button
                                                                onClick={() => handleEditCategory(contact)}
                                                                className="text-xs text-blue-600 hover:text-blue-800 underline"
                                                            >
                                                                Edit
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex items-center space-x-3">
                                            <button
                                                onClick={() => handleCorrect(contact)}
                                                disabled={contact.confirmed || updateContactMutation.isPending}
                                                className={`px-4 py-2 rounded-lg font-medium transition-colors ${contact.confirmed
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-green-600 text-white hover:bg-green-700'
                                                    }`}
                                            >
                                                {contact.confirmed ? 'Confirmed' : 'Correct'}
                                            </button>

                                            <button
                                                onClick={() => handleNotCorrect(contact)}
                                                disabled={updateContactMutation.isPending}
                                                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                                            >
                                                Not Correct
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* Pagination Controls */}
                    {filteredContacts.length > 0 && (
                        <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4 bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            {/* Results Info */}
                            <div className="text-sm text-gray-600">
                                Showing {startIndex + 1} to {Math.min(endIndex, filteredContacts.length)} of {filteredContacts.length} contacts
                            </div>

                            {/* Page Size Selector */}
                            <div className="flex items-center space-x-2">
                                <label className="text-sm text-gray-600">Show:</label>
                                <select
                                    value={contactsPerPage}
                                    onChange={(e) => {
                                        setContactsPerPage(Number(e.target.value));
                                        setCurrentPage(1);
                                    }}
                                    className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                                >
                                    <option value={5}>5</option>
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                    <option value={50}>50</option>
                                </select>
                                <span className="text-sm text-gray-600">per page</span>
                            </div>

                            {/* Pagination Buttons */}
                            {totalPages > 1 && (
                                <div className="flex items-center space-x-2">
                                    {/* Previous Button */}
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === 1
                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                            : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-600 hover:to-blue-600 transform hover:scale-105 shadow-lg'
                                            }`}
                                    >
                                        Previous
                                    </button>

                                    {/* Page Numbers */}
                                    <div className="flex items-center space-x-1">
                                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                            let pageNum;
                                            if (totalPages <= 5) {
                                                pageNum = i + 1;
                                            } else if (currentPage <= 3) {
                                                pageNum = i + 1;
                                            } else if (currentPage >= totalPages - 2) {
                                                pageNum = totalPages - 4 + i;
                                            } else {
                                                pageNum = currentPage - 2 + i;
                                            }

                                            return (
                                                <button
                                                    key={pageNum}
                                                    onClick={() => setCurrentPage(pageNum)}
                                                    className={`w-10 h-10 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === pageNum
                                                        ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg transform scale-105'
                                                        : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200 hover:border-cyan-300'
                                                        }`}
                                                >
                                                    {pageNum}
                                                </button>
                                            );
                                        })}
                                    </div>

                                    {/* Next Button */}
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === totalPages
                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                            : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-600 hover:to-blue-600 transform hover:scale-105 shadow-lg'
                                            }`}
                                    >
                                        Next
                                    </button>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* Birthday Update Confirmation Dialog */}
                {showConfirmDialog && editingContact && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
                            <div className="p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Update Birthday for {editingContact.name}
                                </h3>
                                <p className="text-sm text-gray-600 mb-4">
                                    Please enter the correct birthday in YYYY-MM-DD format:
                                </p>
                                <input
                                    type="date"
                                    value={newBirthday}
                                    onChange={(e) => setNewBirthday(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent mb-4"
                                />
                                <div className="flex justify-end space-x-3">
                                    <button
                                        onClick={() => {
                                            setShowConfirmDialog(false);
                                            setEditingContact(null);
                                            setNewBirthday('');
                                        }}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleBirthdayUpdate}
                                        disabled={!newBirthday || updateContactMutation.isPending}
                                        className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {updateContactMutation.isPending ? 'Updating...' : 'Update Birthday'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Add Contact Dialog */}
                {showAddContactDialog && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
                            <div className="p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Add Missing Contact
                                </h3>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Name
                                        </label>
                                        <input
                                            type="text"
                                            value={newContactData.name}
                                            onChange={(e) => setNewContactData(prev => ({ ...prev, name: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                            placeholder="Enter contact name"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Birthday
                                        </label>
                                        <input
                                            type="date"
                                            value={newContactData.birthday}
                                            onChange={(e) => setNewContactData(prev => ({ ...prev, birthday: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Category
                                        </label>
                                        <select
                                            value={newContactData.category}
                                            onChange={(e) => setNewContactData(prev => ({ ...prev, category: e.target.value as Contact['category'] }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        >
                                            <option value="Friends">Friends</option>
                                            <option value="Family">Family</option>
                                            <option value="Colleagues">Colleagues</option>
                                            <option value="Clients">Clients</option>
                                            <option value="Members">Members</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex justify-end space-x-3 mt-6">
                                    <button
                                        onClick={() => {
                                            setShowAddContactDialog(false);
                                            setNewContactData({ name: '', birthday: '', category: 'Friends' });
                                        }}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleAddContact}
                                        disabled={!newContactData.name || !newContactData.birthday || createContactMutation.isPending}
                                        className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {createContactMutation.isPending ? 'Adding...' : 'Add Contact'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Category Edit Modal */}
                {editingCategory && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-2xl shadow-xl max-w-md w-full">
                            <div className="p-6 border-b border-gray-200">
                                <h3 className="text-xl font-bold text-gray-900">Edit Category</h3>
                                <p className="text-gray-600 mt-1">Update category for {editingCategory.name}</p>
                            </div>

                            <div className="p-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Category
                                </label>
                                <select
                                    value={newCategory}
                                    onChange={(e) => setNewCategory(e.target.value as Contact['category'])}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                >
                                    <option value="Family">Family</option>
                                    <option value="Friends">Friends</option>
                                    <option value="Colleagues">Colleagues</option>
                                    <option value="Clients">Clients</option>
                                    <option value="Members">Members</option>
                                </select>
                            </div>

                            <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-2xl">
                                <div className="flex justify-end space-x-3">
                                    <button
                                        onClick={() => setEditingCategory(null)}
                                        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleCategoryUpdate}
                                        disabled={updateContactMutation.isPending}
                                        className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                                    >
                                        {updateContactMutation.isPending ? 'Updating...' : 'Update Category'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Duplicates Modal */}
                {showDuplicatesModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-xl font-bold text-gray-900">
                                        Duplicate Contacts Found ({duplicates.length} groups)
                                    </h3>
                                    <button
                                        onClick={() => setShowDuplicatesModal(false)}
                                        className="text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                <p className="text-gray-600 mt-2">
                                    Review and manage duplicate contacts. Keep one contact and delete the others.
                                </p>
                            </div>

                            <div className="p-6">
                                {duplicates.length === 0 ? (
                                    <div className="text-center py-12">
                                        <div className="text-6xl mb-4">🎉</div>
                                        <h3 className="text-xl font-bold text-gray-900 mb-2">No Duplicates Found!</h3>
                                        <p className="text-gray-600">Your contact list is clean and duplicate-free.</p>
                                    </div>
                                ) : (
                                    <div className="space-y-6">
                                        {duplicates.map((group, groupIndex) => (
                                            <div key={groupIndex} className="border border-gray-200 rounded-xl p-4 bg-gray-50">
                                                <h4 className="font-semibold text-gray-900 mb-3">
                                                    Duplicate Group {groupIndex + 1} ({group.length} contacts)
                                                </h4>
                                                <div className="grid gap-3">
                                                    {group.map((contact) => (
                                                        <div key={contact.id} className="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between">
                                                            <div className="flex-1">
                                                                <div className="font-medium text-gray-900">{contact.name}</div>
                                                                <div className="text-sm text-gray-600">
                                                                    Birthday: {new Date(contact.birthday).toLocaleDateString()} • Category: {contact.category}
                                                                </div>
                                                                <div className="text-xs text-gray-500">
                                                                    ID: {contact.id}
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center space-x-2">
                                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${contact.confirmed
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : 'bg-yellow-100 text-yellow-800'
                                                                    }`}>
                                                                    {contact.confirmed ? 'Confirmed' : 'Pending'}
                                                                </span>
                                                                <button
                                                                    onClick={() => handlePurgeDuplicate(contact.id)}
                                                                    className="px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                                                                >
                                                                    Delete
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-2xl">
                                <div className="flex justify-end space-x-3">
                                    <button
                                        onClick={() => setShowDuplicatesModal(false)}
                                        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                        Close
                                    </button>
                                    <button
                                        onClick={findDuplicates}
                                        className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                                    >
                                        Refresh Check
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ValidationPage;
