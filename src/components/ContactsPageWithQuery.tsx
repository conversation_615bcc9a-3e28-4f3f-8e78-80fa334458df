import { useState } from "react";
import { useContactsQuery, useDeleteContact } from "../hooks/useContacts";
import { useContactGiftHistoryQuery, useContactGiftReminderQuery, useGiftSuggestionsQuery } from "../hooks/useGifts";

interface Contact {
    id: string;
    name: string;
    birthday: string;
    category: string;
}

interface ContactsPageWithQueryProps {
    onNavigate: (page: string) => void;
}

const ContactsPageWithQuery = ({ onNavigate }: ContactsPageWithQueryProps) => {
    // Use React Query hooks instead of context
    const { data: contacts = [], isLoading, error, refetch } = useContactsQuery();
    const deleteContactMutation = useDeleteContact();

    const [searchTerm, setSearchTerm] = useState("");
    const [activeFilter, setActiveFilter] = useState("All");
    const [currentPage] = useState(1);
    const [contactsPerPage] = useState(6);
    const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
    const [showGiftModal, setShowGiftModal] = useState(false);

    // Gift data for selected contact
    const { data: contactGiftHistory = [] } = useContactGiftHistoryQuery(selectedContact?.id || '');
    const { data: contactGiftReminder } = useContactGiftReminderQuery(selectedContact?.id || '');
    const { data: giftSuggestions = [] } = useGiftSuggestionsQuery();

    const filters = ["All", "Family", "Friends", "Work", "Clients", "Members"];

    const calculateDaysUntilBirthday = (birthday: string) => {
        const today = new Date();
        const currentYear = today.getFullYear();
        const birthdayThisYear = new Date(currentYear, new Date(birthday).getMonth(), new Date(birthday).getDate());

        if (birthdayThisYear < today) {
            birthdayThisYear.setFullYear(currentYear + 1);
        }

        const diffTime = birthdayThisYear.getTime() - today.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    // Enhanced contacts with calculated days until birthday
    const enhancedContacts = contacts.map(contact => ({
        ...contact,
        email: `${contact.name.toLowerCase().replace(' ', '.')}@example.com`,
        phone: "",
        avatar: contact.name.split(' ').map(n => n[0]).join('').toUpperCase(),
        color: "bg-purple-500",
        notes: `Contact added via form`,
        daysUntilBirthday: calculateDaysUntilBirthday(contact.birthday),
        tags: contact.category === "Family" ? ["Family"] :
            contact.category === "Friends" ? ["Friends"] :
                contact.category === "Colleagues" ? ["Work"] : []
    }));

    // Filter contacts
    const filteredContacts = enhancedContacts.filter(contact => {
        const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesFilter = activeFilter === "All" ||
            (activeFilter === "Family" && contact.category === "Family") ||
            (activeFilter === "Friends" && contact.category === "Friends") ||
            (activeFilter === "Work" && contact.category === "Colleagues") ||
            (activeFilter === "Clients" && contact.category === "Clients") ||
            (activeFilter === "Members" && contact.category === "Members");
        return matchesSearch && matchesFilter;
    });

    // Sort by days until birthday (closest first)
    const sortedContacts = [...filteredContacts].sort((a, b) => a.daysUntilBirthday - b.daysUntilBirthday);

    // Pagination
    const startIndex = (currentPage - 1) * contactsPerPage;
    const endIndex = startIndex + contactsPerPage;
    const paginatedContacts = sortedContacts.slice(startIndex, endIndex);

    const handleDeleteContact = async (contactId: string) => {
        try {
            await deleteContactMutation.mutateAsync(contactId);
        } catch (error) {
            console.error('Failed to delete contact:', error);
        }
    };

    const handleGiftClick = (contact: Contact) => {
        setSelectedContact(contact);
        setShowGiftModal(true);
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading contacts...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load contacts</p>
                    <button
                        onClick={() => refetch()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex justify-between items-center mb-8">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-600 mb-2">Contacts (React Query)</h1>
                        <p className="text-gray-600">Manage your birthday contacts with React Query</p>
                        <p className="text-sm text-gray-500 mt-1">📅 Sorted by upcoming birthdays</p>
                    </div>
                    <button
                        onClick={() => onNavigate('add-contacts')}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center"
                    >
                        <span className="mr-2">+</span>
                        Add Contacts
                    </button>
                </div>

                {/* Search and Filter */}
                <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1">
                            <input
                                type="text"
                                placeholder="Search contacts..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                        </div>
                        <div className="flex gap-2">
                            {filters.map((filter) => (
                                <button
                                    key={filter}
                                    onClick={() => setActiveFilter(filter)}
                                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${activeFilter === filter
                                        ? 'bg-purple-600 text-white'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    {filter}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <div className="flex items-center">
                            <div className="p-3 bg-purple-100 rounded-lg">
                                <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Contacts</p>
                                <p className="text-2xl font-bold text-gray-900">{contacts.length}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <div className="flex items-center">
                            <div className="p-3 bg-blue-100 rounded-lg">
                                <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Gift History Items</p>
                                <p className="text-2xl font-bold text-gray-900">{contactGiftHistory.length}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <div className="flex items-center">
                            <div className="p-3 bg-green-100 rounded-lg">
                                <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM14 9a1 1 0 100 2h2a1 1 0 100-2h-2zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                                </svg>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Gift Suggestions</p>
                                <p className="text-2xl font-bold text-gray-900">{giftSuggestions.length}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Contacts Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {paginatedContacts.map((contact) => (
                        <div key={contact.id} className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
                            {/* Contact Header */}
                            <div className="flex items-start justify-between mb-4">
                                <div className="flex items-center">
                                    <div className={`w-12 h-12 ${contact.color} rounded-full flex items-center justify-center text-white font-semibold text-lg mr-3`}>
                                        {contact.avatar}
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 text-lg">{contact.name}</h3>
                                        <p className="text-sm text-gray-500">{contact.category}</p>
                                    </div>
                                </div>
                                <button
                                    onClick={() => handleDeleteContact(contact.id)}
                                    disabled={deleteContactMutation.isPending}
                                    className="text-gray-400 hover:text-red-600 disabled:opacity-50"
                                >
                                    {deleteContactMutation.isPending ? (
                                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
                                    ) : (
                                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    )}
                                </button>
                            </div>

                            {/* Birthday Info */}
                            <div className="mb-4">
                                <div className="flex items-center text-sm text-gray-600 mb-2">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    {new Date(contact.birthday).toLocaleDateString()}
                                </div>
                                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${contact.daysUntilBirthday <= 7
                                    ? 'bg-red-100 text-red-800'
                                    : contact.daysUntilBirthday <= 30
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-green-100 text-green-800'
                                    }`}>
                                    {contact.daysUntilBirthday === 0 ? 'Today!' :
                                        contact.daysUntilBirthday === 1 ? 'Tomorrow' :
                                            `${contact.daysUntilBirthday} days`}
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex space-x-2">
                                <button className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors flex items-center justify-center">
                                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                    Message
                                </button>
                                <button
                                    onClick={() => handleGiftClick(contact)}
                                    className="bg-purple-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-purple-700 transition-colors flex items-center justify-center"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM14 9a1 1 0 100 2h2a1 1 0 100-2h-2zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                                    </svg>
                                    Gifts
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Simple Gift Modal */}
                {showGiftModal && selectedContact && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Gift Data for {selectedContact.name}
                                </h3>
                                <button
                                    onClick={() => setShowGiftModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">Gift History ({contactGiftHistory.length} items)</h4>
                                    {contactGiftHistory.length > 0 ? (
                                        <div className="space-y-2">
                                            {contactGiftHistory.slice(0, 3).map((gift) => (
                                                <div key={gift.id} className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                                    {gift.giftName} - ${gift.amount} ({new Date(gift.date).toLocaleDateString()})
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-500">No gift history yet</p>
                                    )}
                                </div>

                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">Gift Reminder</h4>
                                    {contactGiftReminder ? (
                                        <div className="text-sm text-gray-600 bg-green-50 p-2 rounded">
                                            Reminder set for: {new Date(contactGiftReminder.reminderDate).toLocaleDateString()}
                                            {contactGiftReminder.giftIdeas.length > 0 && (
                                                <div>Ideas: {contactGiftReminder.giftIdeas.join(', ')}</div>
                                            )}
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-500">No reminder set</p>
                                    )}
                                </div>

                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">Gift Suggestions ({giftSuggestions.length} available)</h4>
                                    <div className="space-y-1">
                                        {giftSuggestions.slice(0, 3).map((suggestion) => (
                                            <div key={suggestion.id} className="text-sm text-gray-600">
                                                {suggestion.name} - ${suggestion.price}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end mt-6">
                                <button
                                    onClick={() => setShowGiftModal(false)}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {filteredContacts.length === 0 && (
                    <div className="text-center py-12">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No contacts found</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by adding a new contact.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ContactsPageWithQuery;
