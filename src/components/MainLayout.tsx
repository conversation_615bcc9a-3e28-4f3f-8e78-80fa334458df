import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscription } from '../context/SubscriptionContext';
import Sidebar from './Sidebar';
import Dashboard from './Dashboard';
import AnalyticsDashboard from './AnalyticsDashboard';
import GiftCenter from './GiftCenter';
import BirthdayCalendar from './BirthdayCalendar';
import ContactsPage from './ContactsPage';
import RemindersPage from './RemindersPage';
import AddContactsPage from './AddContactsPage';
import SubscriptionPage from './SubscriptionPage';
import DemoBillingPage from './DemoBillingPage';
import MessagingDashboard from './MessagingDashboard';
import TrialConversionFlow from './TrialConversionFlow';
import AdminDashboard from './AdminDashboard';
import ValidationPage from './ValidationPage';
import AnniversaryCalendar from './AnniversaryCalendar';

const MainLayout = () => {
    const { user } = useAuth();
    const { plan, isTrialActive, trialDaysLeft } = useSubscription();
    const [currentPage, setCurrentPage] = useState('dashboard');

    const renderContent = () => {
        switch (currentPage) {
            case 'dashboard':
                return <Dashboard onNavigate={setCurrentPage} />;
            case 'analytics':
                return <AnalyticsDashboard />;
            case 'gifts':
                return <GiftCenter />;
            case 'contacts':
                return <ContactsPage onNavigate={setCurrentPage} />;
            case 'add-contacts':
                return <AddContactsPage />;
            case 'calendar':
                return <BirthdayCalendar />;
            case 'anniversaries':
                return <AnniversaryCalendar />;
            case 'reminders':
                return <RemindersPage />;
            case 'messaging':
                return <MessagingDashboard onNavigate={setCurrentPage} />;
            case 'subscription':
                return <SubscriptionPage />;
            case 'billing':
                return <DemoBillingPage />;
            case 'admin':
                return <AdminDashboard />;
            case 'validation':
                return <ValidationPage onNavigate={setCurrentPage} />;
            case 'settings':
                return (
                    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
                        <div className="max-w-7xl mx-auto space-y-6">
                            <div className="mb-8">
                                <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
                                <p className="text-gray-600">Manage your account and application preferences</p>
                            </div>

                            {/* Account Settings */}
                            <div className="bg-white p-6 rounded-lg shadow-sm border">
                                <h2 className="text-lg font-semibold text-gray-900 mb-4">Account Settings</h2>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                        <input
                                            type="email"
                                            value={user?.email || ''}
                                            disabled
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                                        <input
                                            type="text"
                                            placeholder="Enter your display name"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">Time Zone</label>
                                        <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                                            <option>UTC-8 (Pacific Time)</option>
                                            <option>UTC-5 (Eastern Time)</option>
                                            <option>UTC+0 (GMT)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            {/* Notification Preferences */}
                            <div className="bg-white p-6 rounded-lg shadow-sm border">
                                <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h2>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <div className="font-medium text-gray-900">Email Notifications</div>
                                            <div className="text-sm text-gray-600">Receive birthday reminders via email</div>
                                        </div>
                                        <label className="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" className="sr-only peer" defaultChecked />
                                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                        </label>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <div className="font-medium text-gray-900">Push Notifications</div>
                                            <div className="text-sm text-gray-600">Receive push notifications on your device</div>
                                        </div>
                                        <label className="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" className="sr-only peer" />
                                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Data & Privacy */}
                            <div className="bg-white p-6 rounded-lg shadow-sm border">
                                <h2 className="text-lg font-semibold text-gray-900 mb-4">Data & Privacy</h2>
                                <div className="space-y-3">
                                    <button className="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                        <div className="font-medium text-gray-900">Export Data</div>
                                        <div className="text-sm text-gray-600">Download all your birthday data</div>
                                    </button>
                                    <button className="w-full text-left px-4 py-3 border border-red-200 rounded-lg hover:bg-red-50 transition-colors text-red-600">
                                        <div className="font-medium">Delete Account</div>
                                        <div className="text-sm">Permanently delete your account and all data</div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            default:
                return <Dashboard />;
        }
    };

    return (
        <div className="flex h-screen bg-gray-50">
            <Sidebar currentPage={currentPage} onPageChange={setCurrentPage} />
            <div className="flex-1 overflow-auto">
                {/* Top Search Bar */}
                <div className="bg-white border-b border-gray-200 px-6 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex-1 max-w-lg">
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    placeholder="Search contacts..."
                                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                                />
                            </div>
                        </div>
                        <div className="flex items-center space-x-4">
                            {/* Subscription Link with Status */}
                            <button
                                onClick={() => setCurrentPage('subscription')}
                                className="flex items-center space-x-2 text-sm font-medium text-gray-600 hover:text-purple-600 transition-colors"
                            >
                                <span>Subscription</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${plan === "Free"
                                    ? "bg-gray-100 text-gray-700"
                                    : isTrialActive
                                        ? "bg-blue-100 text-blue-700"
                                        : "bg-green-100 text-green-700"
                                    }`}>
                                    {plan === "Free"
                                        ? "Free"
                                        : isTrialActive
                                            ? `Trial (${trialDaysLeft}d)`
                                            : plan
                                    }
                                </span>
                            </button>

                            {/* Notifications */}
                            <button className="relative p-2 text-gray-400 hover:text-gray-500">
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                                </svg>
                                <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                            </button>

                            {/* Dark mode toggle */}
                            <button className="p-2 text-gray-400 hover:text-gray-500">
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                                </svg>
                            </button>

                            {/* User avatar */}
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                                    <span className="text-white text-sm font-medium">
                                        {user?.email?.charAt(0).toUpperCase() || 'U'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <main className="flex-1">
                    {renderContent()}
                </main>

                {/* Trial Conversion Flow */}
                <TrialConversionFlow autoShow={true} />

                {/* Footer */}
                <footer className="bg-white border-t border-gray-200 px-6 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6 text-sm text-gray-600">
                            <button
                                onClick={() => setCurrentPage('dashboard')}
                                className={`hover:text-purple-600 transition-colors ${currentPage === 'dashboard' ? 'text-purple-600 font-medium' : ''}`}
                            >
                                Dashboard
                            </button>
                            <button
                                onClick={() => setCurrentPage('contacts')}
                                className={`hover:text-purple-600 transition-colors ${currentPage === 'contacts' ? 'text-purple-600 font-medium' : ''}`}
                            >
                                Contacts
                            </button>
                            <button
                                onClick={() => setCurrentPage('subscription')}
                                className={`hover:text-purple-600 transition-colors ${currentPage === 'subscription' ? 'text-purple-600 font-medium' : ''}`}
                            >
                                Subscription
                            </button>
                            <button
                                onClick={() => setCurrentPage('billing')}
                                className={`hover:text-purple-600 transition-colors ${currentPage === 'billing' ? 'text-purple-600 font-medium' : ''}`}
                            >
                                Billing
                            </button>
                            <button
                                onClick={() => setCurrentPage('settings')}
                                className={`hover:text-purple-600 transition-colors ${currentPage === 'settings' ? 'text-purple-600 font-medium' : ''}`}
                            >
                                Profile
                            </button>
                        </div>
                        <div className="text-sm text-gray-500">
                            © {new Date().getFullYear()} WeWish. All rights reserved.
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default MainLayout;
