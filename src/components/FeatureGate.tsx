import React from 'react';
import { useFeatureAccess, FEATURES } from '../hooks/useFeatureAccess';
import { usePlanManagement } from '../hooks/useStripe';

interface FeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgrade?: boolean;
  className?: string;
}

interface UpgradePromptProps {
  requiredPlan: string;
  featureName: string;
  featureDescription: string;
  onUpgrade?: () => void;
  compact?: boolean;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  requiredPlan,
  featureName,
  featureDescription,
  onUpgrade,
  compact = false,
}) => {
  const { handleUpgrade, isUpgrading } = usePlanManagement();

  const handleUpgradeClick = async () => {
    if (onUpgrade) {
      onUpgrade();
    } else if (requiredPlan !== 'Free') {
      try {
        await handleUpgrade(requiredPlan as 'Standard' | 'Elite' | 'Premium');
      } catch (error) {
        console.error('Upgrade failed:', error);
      }
    }
  };

  if (compact) {
    return (
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">{featureName}</h4>
            <p className="text-sm text-gray-600">Requires {requiredPlan} plan</p>
          </div>
          <button
            onClick={handleUpgradeClick}
            disabled={isUpgrading}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
            {isUpgrading ? 'Upgrading...' : 'Upgrade'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg border-2 border-dashed border-purple-200">
      <div className="mb-6">
        <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{featureName}</h3>
        <p className="text-gray-600 mb-4">{featureDescription}</p>
        <div className="bg-white rounded-lg p-3 inline-block">
          <span className="text-sm font-medium text-purple-600">
            Requires {requiredPlan} Plan
          </span>
        </div>
      </div>

      <div className="space-y-3">
        <button
          onClick={handleUpgradeClick}
          disabled={isUpgrading}
          className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50"
        >
          {isUpgrading ? 'Processing Upgrade...' : `Upgrade to ${requiredPlan}`}
        </button>
        
        <p className="text-xs text-gray-500">
          Start your free trial or upgrade to unlock this feature
        </p>
      </div>
    </div>
  );
};

const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback,
  showUpgrade = true,
  className = '',
}) => {
  const { hasFeatureAccess, getUpgradeSuggestion } = useFeatureAccess();
  
  const hasAccess = hasFeatureAccess(feature);
  const featureConfig = FEATURES[feature];
  
  if (hasAccess) {
    return <div className={className}>{children}</div>;
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  // If showUpgrade is false, render nothing
  if (!showUpgrade) {
    return null;
  }

  // Show upgrade prompt
  const requiredPlan = getUpgradeSuggestion(feature);
  
  if (!featureConfig || !requiredPlan) {
    return null;
  }

  return (
    <div className={className}>
      <UpgradePrompt
        requiredPlan={requiredPlan}
        featureName={featureConfig.name}
        featureDescription={featureConfig.description}
      />
    </div>
  );
};

// Usage limit component
interface UsageLimitProps {
  feature: string;
  currentUsage: number;
  showUpgradeWhenFull?: boolean;
  className?: string;
}

export const UsageLimit: React.FC<UsageLimitProps> = ({
  feature,
  currentUsage,
  showUpgradeWhenFull = true,
  className = '',
}) => {
  const { getUsageLimit, isWithinUsageLimit, getUpgradeSuggestion } = useFeatureAccess();
  
  const limit = getUsageLimit(feature);
  const withinLimit = isWithinUsageLimit(feature, currentUsage);
  const featureConfig = FEATURES[feature];
  
  if (limit === -1) {
    return (
      <div className={`text-sm text-green-600 ${className}`}>
        ✓ Unlimited {featureConfig?.name || feature}
      </div>
    );
  }

  const percentage = (currentUsage / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = !withinLimit;

  return (
    <div className={className}>
      <div className="flex items-center justify-between text-sm mb-2">
        <span className="text-gray-600">
          {featureConfig?.name || feature} Usage
        </span>
        <span className={`font-medium ${
          isAtLimit ? 'text-red-600' : isNearLimit ? 'text-yellow-600' : 'text-gray-900'
        }`}>
          {currentUsage} / {limit}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAtLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-green-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>

      {isAtLimit && showUpgradeWhenFull && (
        <div className="mt-3">
          <UpgradePrompt
            requiredPlan={getUpgradeSuggestion(feature) || 'Standard'}
            featureName={`More ${featureConfig?.name || feature}`}
            featureDescription={`You've reached your ${featureConfig?.name || feature} limit`}
            compact
          />
        </div>
      )}

      {isNearLimit && !isAtLimit && (
        <p className="text-xs text-yellow-600 mt-1">
          You're approaching your {featureConfig?.name || feature} limit
        </p>
      )}
    </div>
  );
};

// Feature badge component
interface FeatureBadgeProps {
  feature: string;
  className?: string;
}

export const FeatureBadge: React.FC<FeatureBadgeProps> = ({ feature, className = '' }) => {
  const { hasFeatureAccess, getUpgradeSuggestion } = useFeatureAccess();
  const featureConfig = FEATURES[feature];
  
  if (!featureConfig) return null;
  
  const hasAccess = hasFeatureAccess(feature);
  const requiredPlan = getUpgradeSuggestion(feature);

  return (
    <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
      hasAccess 
        ? 'bg-green-100 text-green-800' 
        : 'bg-gray-100 text-gray-600'
    } ${className}`}>
      {hasAccess ? (
        <>
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Available
        </>
      ) : (
        <>
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          {requiredPlan} Required
        </>
      )}
    </span>
  );
};

export default FeatureGate;
