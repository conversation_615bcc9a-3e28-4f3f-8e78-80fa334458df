import React, { useState } from "react";
import { useContactsQuery, useDeleteContact } from "../hooks/useContacts";
import { ContactSkeleton } from "./SkeletonLoader";
import { InlineLoader } from "./LoadingOverlay";
import LoadingSpinner from "./LoadingSpinner";

const categories = ["All", "Family", "Friends", "Colleagues", "Clients", "Members"] as const;

const ContactList = () => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const deleteContactMutation = useDeleteContact();
    const [search, setSearch] = useState("");
    const [filterCategory, setFilterCategory] = useState<typeof categories[number]>("All");
    const [deletingContactId, setDeletingContactId] = useState<string | null>(null);

    const filteredContacts = contacts.filter((contact) => {
        const matchesCategory =
            filterCategory === "All" ? true : contact.category === filterCategory;
        const matchesSearch = contact.name.toLowerCase().includes(search.toLowerCase());
        return matchesCategory && matchesSearch;
    });

    const handleDeleteContact = async (contactId: string) => {
        setDeletingContactId(contactId);
        try {
            await deleteContactMutation.mutateAsync(contactId);
        } catch (error) {
            console.error('Failed to delete contact:', error);
        } finally {
            setDeletingContactId(null);
        }
    };

    if (isLoading) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Contacts</h2>
                <div className="flex flex-wrap gap-4 mb-4">
                    <input
                        type="text"
                        placeholder="Search contacts..."
                        className="flex-grow border p-2 rounded"
                        disabled
                    />
                    <select className="border p-2 rounded" disabled>
                        <option>All</option>
                    </select>
                </div>
                <div className="space-y-4">
                    {Array.from({ length: 3 }).map((_, index) => (
                        <ContactSkeleton key={index} />
                    ))}
                </div>
                <InlineLoader message="Loading contacts..." />
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Contacts</h2>
                <div className="text-center py-8 text-red-500">
                    <div className="text-2xl mb-2">⚠️</div>
                    <p>Failed to load contacts</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="mt-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Contacts</h2>
            <div className="flex flex-wrap gap-4 mb-4">
                <input
                    type="text"
                    placeholder="Search contacts..."
                    className="flex-grow border p-2 rounded"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                />
                <select
                    className="border p-2 rounded"
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value as typeof categories[number])}
                >
                    {categories.map((cat) => (
                        <option key={cat} value={cat}>
                            {cat}
                        </option>
                    ))}
                </select>
            </div>
            {filteredContacts.length === 0 ? (
                <p className="text-center text-gray-500 py-8">No contacts found.</p>
            ) : (
                <ul className="space-y-2">
                    {filteredContacts.map((c) => (
                        <li
                            key={c.id}
                            className="flex justify-between items-center border-b py-3 hover:bg-gray-50 rounded px-2 transition"
                        >
                            <div>
                                <span className="font-semibold">{c.name}</span>{" "}
                                <span className="text-sm text-gray-600">
                                    ({new Date(c.birthday).toLocaleDateString()}, {c.category})
                                </span>
                            </div>
                            <button
                                className="text-red-600 hover:text-red-800 disabled:opacity-50 flex items-center space-x-1"
                                onClick={() => handleDeleteContact(c.id)}
                                disabled={deletingContactId === c.id}
                            >
                                {deletingContactId === c.id ? (
                                    <>
                                        <LoadingSpinner size="sm" color="red" />
                                        <span>Deleting...</span>
                                    </>
                                ) : (
                                    <span>Delete</span>
                                )}
                            </button>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default ContactList;
