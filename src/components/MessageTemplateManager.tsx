import React, { useState } from 'react';
import {
  useTemplatesQuery,
  useCreateTemplate,
  useUpdateTemplate,
  useDeleteTemplate,
  useResetTemplates
} from '../hooks/useMessaging';
import type { MessageTemplate } from '../types/messaging';

const MessageTemplateManager: React.FC = () => {
  const { data: templates = [], isLoading, error } = useTemplatesQuery();
  const createTemplate = useCreateTemplate();
  const updateTemplate = useUpdateTemplate();
  const deleteTemplate = useDeleteTemplate();
  const resetTemplates = useResetTemplates();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);
  const [showInactive, setShowInactive] = useState(true);
  const [filterType, setFilterType] = useState<'all' | 'birthday' | 'anniversary' | 'landmark' | 'weekly_admin'>('all');
  const [formData, setFormData] = useState({
    name: '',
    type: 'birthday' as 'birthday' | 'anniversary' | 'landmark' | 'reminder' | 'weekly_admin',
    subject: '',
    message: '',
    variables: [] as string[],
    isActive: true,
  });

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'birthday',
      subject: '',
      message: '',
      variables: [],
      isActive: true,
    });
    setEditingTemplate(null);
    setShowCreateForm(false);
  };

  const handleEdit = (template: MessageTemplate) => {
    setFormData({
      name: template.name,
      type: template.type,
      subject: template.subject,
      message: template.message,
      variables: template.variables,
      isActive: template.isActive,
    });
    setEditingTemplate(template);
    setShowCreateForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingTemplate) {
        await updateTemplate.mutateAsync({
          id: editingTemplate.id,
          updates: formData,
        });
      } else {
        await createTemplate.mutateAsync(formData);
      }
      resetForm();
    } catch (error) {
      console.error('Failed to save template:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      try {
        await deleteTemplate.mutateAsync(id);
      } catch (error) {
        console.error('Failed to delete template:', error);
      }
    }
  };

  const handleToggleActive = async (template: MessageTemplate) => {
    try {
      await updateTemplate.mutateAsync({
        id: template.id,
        updates: { isActive: !template.isActive },
      });
    } catch (error) {
      console.error('Failed to toggle template status:', error);
    }
  };

  const handleResetTemplates = async () => {
    if (window.confirm('Are you sure you want to reset all templates to defaults? This will restore all default templates and cannot be undone.')) {
      try {
        await resetTemplates.mutateAsync();
        console.log('✅ Templates reset to defaults');
      } catch (error) {
        console.error('Failed to reset templates:', error);
      }
    }
  };

  const extractVariables = (text: string): string[] => {
    const matches = text.match(/\{([^}]+)\}/g);
    return matches ? matches.map(match => match.slice(1, -1)) : [];
  };

  // Filter templates based on type and active status
  const filteredTemplates = templates.filter(template => {
    const typeMatch = filterType === 'all' || template.type === filterType;
    const activeMatch = showInactive || template.isActive;
    return typeMatch && activeMatch;
  });

  // Get template counts by type
  const templateCounts = {
    all: templates.length,
    birthday: templates.filter(t => t.type === 'birthday').length,
    anniversary: templates.filter(t => t.type === 'anniversary').length,
    landmark: templates.filter(t => t.type === 'landmark').length,
    weekly_admin: templates.filter(t => t.type === 'weekly_admin').length,
    active: templates.filter(t => t.isActive).length,
    inactive: templates.filter(t => !t.isActive).length,
  };

  const handleContentChange = (field: 'subject' | 'message', value: string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      // Auto-extract variables from subject and message
      const subjectVars = extractVariables(updated.subject);
      const messageVars = extractVariables(updated.message);
      const allVars = [...new Set([...subjectVars, ...messageVars])];
      updated.variables = allVars;
      return updated;
    });
  };

  const getTemplateTypeColor = (type: string) => {
    switch (type) {
      case 'birthday': return 'bg-blue-100 text-blue-800';
      case 'anniversary': return 'bg-pink-100 text-pink-800';
      case 'landmark': return 'bg-yellow-100 text-yellow-800';
      case 'reminder': return 'bg-green-100 text-green-800';
      case 'weekly_admin': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="text-center text-red-600">
          <p>Failed to load templates</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Message Templates ({templateCounts.all} total)
          </h3>
          <p className="text-sm text-gray-600">
            Manage templates for birthday, anniversary, and other automated messages
          </p>
          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
            <span>🎂 Birthday: {templateCounts.birthday}</span>
            <span>💕 Anniversary: {templateCounts.anniversary}</span>
            <span>🎊 Landmark: {templateCounts.landmark}</span>
            <span>📧 Admin: {templateCounts.weekly_admin}</span>
            <span>✅ Active: {templateCounts.active}</span>
            <span>❌ Inactive: {templateCounts.inactive}</span>
          </div>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
        >
          + New Template
        </button>
      </div>

      {/* Filter Controls */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mr-2">Filter by Type:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as 'all' | 'birthday' | 'anniversary' | 'landmark' | 'weekly_admin')}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Types ({templateCounts.all})</option>
                <option value="birthday">Birthday ({templateCounts.birthday})</option>
                <option value="anniversary">Anniversary ({templateCounts.anniversary})</option>
                <option value="landmark">Landmark ({templateCounts.landmark})</option>
                <option value="weekly_admin">Admin ({templateCounts.weekly_admin})</option>
              </select>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="showInactive"
                checked={showInactive}
                onChange={(e) => setShowInactive(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="showInactive" className="text-sm text-gray-700">
                Show Inactive Templates
              </label>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">
              Showing {filteredTemplates.length} of {templateCounts.all} templates
            </div>
            {templateCounts.all < 15 && (
              <button
                onClick={handleResetTemplates}
                disabled={resetTemplates.isPending}
                className="px-3 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {resetTemplates.isPending ? 'Resetting...' : 'Reset Templates'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-4">
            {editingTemplate ? 'Edit Template' : 'Create New Template'}
          </h4>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    type: e.target.value as 'birthday' | 'anniversary' | 'landmark' | 'reminder' | 'weekly_admin'
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="birthday">Birthday</option>
                  <option value="anniversary">Anniversary</option>
                  <option value="landmark">Landmark Birthday</option>
                  <option value="reminder">Reminder</option>
                  <option value="weekly_admin">Weekly Admin</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subject Line
              </label>
              <input
                type="text"
                value={formData.subject}
                onChange={(e) => handleContentChange('subject', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., Happy Birthday, {name}! 🎉"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message Content
              </label>
              <textarea
                value={formData.message}
                onChange={(e) => handleContentChange('message', e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Dear {name},&#10;&#10;Birthday: Wishing you a very happy {age}th birthday!&#10;Anniversary: Happy {years} year anniversary to you and {partnerName}!..."
                required
              />
            </div>

            {formData.variables.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Available Variables
                </label>
                <div className="flex flex-wrap gap-2">
                  {formData.variables.map(variable => (
                    <span
                      key={variable}
                      className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
                    >
                      {`{${variable}}`}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                Active template
              </label>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={createTemplate.isPending || updateTemplate.isPending}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                {createTemplate.isPending || updateTemplate.isPending
                  ? 'Saving...'
                  : editingTemplate ? 'Update Template' : 'Create Template'
                }
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Templates List */}
      <div className="space-y-4">
        {filteredTemplates.map(template => (
          <div
            key={template.id}
            className={`p-4 border rounded-lg ${template.isActive ? 'border-gray-200' : 'border-gray-100 bg-gray-50'
              }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="font-medium text-gray-900">{template.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${getTemplateTypeColor(template.type)}`}>
                    {template.type}
                  </span>
                  {!template.isActive && (
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                      Inactive
                    </span>
                  )}
                </div>

                <p className="text-sm text-gray-600 mb-2">
                  <strong>Subject:</strong> {template.subject}
                </p>

                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {typeof template.message === 'string' ? template.message.substring(0, 150) : ''}
                  {typeof template.message === 'string' && template.message.length > 150 && '...'}
                </p>

                {template.variables.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {template.variables.map(variable => (
                      <span
                        key={variable}
                        className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                      >
                        {`{${variable}}`}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleToggleActive(template)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${template.isActive
                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                >
                  {template.isActive ? 'Active' : 'Inactive'}
                </button>
                <button
                  onClick={() => handleEdit(template)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(template.id)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}

        {filteredTemplates.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {templates.length === 0 ? (
              <p>No templates found. Create your first template to get started!</p>
            ) : (
              <p>No templates match the current filter. Try adjusting your filter settings.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageTemplateManager;
