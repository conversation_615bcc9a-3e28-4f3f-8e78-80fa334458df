import React, { useState } from "react";
import { useContactsQuery } from "../hooks/useContacts";
import {
    startOfMonth,
    endOfMonth,
    eachDayOfInterval,
    format,
    isSameDay,
    startOfWeek,
    endOfWeek,
    addMonths,
    subMonths,
    getDaysInMonth,
    getDay
} from "date-fns";

const BirthdayCalendar = () => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const [currentDate, setCurrentDate] = useState(new Date());
    const [selectedDate, setSelectedDate] = useState(new Date());

    // Get the full calendar grid (including days from previous/next month)
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart);
    const calendarEnd = endOfWeek(monthEnd);

    const days = eachDayOfInterval({
        start: calendarStart,
        end: calendarEnd,
    });

    // Collect birthdays and anniversaries matching the month/day (ignore year)
    const birthdaysByDate = days.reduce<Record<string, string[]>>((acc, day) => {
        const key = format(day, "MM-dd");
        acc[key] = contacts
            .filter(
                (c) =>
                    format(new Date(c.birthday), "MM-dd") === key
            )
            .map((c) => `🎂 ${c.name}`);
        return acc;
    }, {});

    const anniversariesByDate = days.reduce<Record<string, string[]>>((acc, day) => {
        const key = format(day, "MM-dd");
        acc[key] = contacts
            .filter(
                (c) =>
                    c.anniversaryDate && format(new Date(c.anniversaryDate), "MM-dd") === key
            )
            .map((c) => `💕 ${c.name}${c.partnerName ? ` & ${c.partnerName}` : ''} (${c.anniversaryType})`);
        return acc;
    }, {});

    // Combine birthdays and anniversaries
    const eventsByDate = days.reduce<Record<string, string[]>>((acc, day) => {
        const key = format(day, "MM-dd");
        acc[key] = [...(birthdaysByDate[key] || []), ...(anniversariesByDate[key] || [])];
        return acc;
    }, {});

    const isCurrentMonth = (day: Date) => {
        return format(day, 'MM-yyyy') === format(currentDate, 'MM-yyyy');
    };

    const selectedDateKey = format(selectedDate, "MM-dd");
    const selectedDateEvents = eventsByDate[selectedDateKey] || [];
    const selectedDateBirthdays = birthdaysByDate[selectedDateKey] || [];
    const selectedDateAnniversaries = anniversariesByDate[selectedDateKey] || [];

    // Calculate monthly summary for birthdays
    const currentMonthBirthdays = Object.entries(birthdaysByDate)
        .filter(([dateKey, names]) => {
            const [month] = dateKey.split('-');
            return month === format(currentDate, 'MM') && names.length > 0;
        });

    const totalBirthdays = currentMonthBirthdays.reduce((sum, [, names]) => sum + names.length, 0);
    const daysWithBirthdays = currentMonthBirthdays.length;

    // Calculate monthly summary for anniversaries
    const currentMonthAnniversaries = Object.entries(anniversariesByDate)
        .filter(([dateKey, names]) => {
            const [month] = dateKey.split('-');
            return month === format(currentDate, 'MM') && names.length > 0;
        });

    const totalAnniversaries = currentMonthAnniversaries.reduce((sum, [, names]) => sum + names.length, 0);
    const daysWithAnniversaries = currentMonthAnniversaries.length;

    // Find busiest day (combining birthdays and anniversaries)
    const allEvents = Object.entries(eventsByDate)
        .filter(([dateKey, events]) => {
            const [month] = dateKey.split('-');
            return month === format(currentDate, 'MM') && events.length > 0;
        });

    const busiestDay = allEvents.reduce((busiest, [dateKey, events]) => {
        if (events.length > busiest.count) {
            const [, day] = dateKey.split('-');
            return { day: parseInt(day), count: events.length };
        }
        return busiest;
    }, { day: 0, count: 0 });

    const totalEvents = totalBirthdays + totalAnniversaries;
    const daysWithEvents = new Set([...currentMonthBirthdays.map(([key]) => key), ...currentMonthAnniversaries.map(([key]) => key)]).size;

    const navigateMonth = (direction: 'prev' | 'next') => {
        setCurrentDate(prev => direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1));
    };

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="flex h-full bg-gray-50 items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading calendar...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex h-full bg-gray-50 items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">Failed to load calendar</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <>
            {/* Custom Styles */}
            <style>{`
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes slideInLeft {
                    from {
                        opacity: 0;
                        transform: translateX(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }

                @keyframes shimmer {
                    0% {
                        background-position: -200px 0;
                    }
                    100% {
                        background-position: calc(200px + 100%) 0;
                    }
                }

                .calendar-day {
                    animation: fadeInUp 0.3s ease-out forwards;
                }

                .sidebar-item {
                    animation: slideInLeft 0.4s ease-out forwards;
                }

                .shimmer {
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                    background-size: 200px 100%;
                    animation: shimmer 2s infinite;
                }
            `}</style>

            <div className="flex h-full bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 min-h-screen">
                {/* Main Calendar Area */}
                <div className="flex-1 p-6">
                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 h-full flex flex-col overflow-hidden">
                        {/* Calendar Header */}
                        <div className="relative bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 p-6 text-white">
                            <div className="absolute inset-0 bg-black/10"></div>
                            <div className="relative flex items-center justify-between">
                                <div>
                                    <h1 className="text-3xl font-bold mb-2 flex items-center">
                                        <span className="mr-3 text-4xl">🎂💕</span>
                                        Birthday & Anniversary Calendar
                                    </h1>
                                    <p className="text-purple-100">View all upcoming birthdays and anniversaries in calendar format</p>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <button
                                        onClick={() => navigateMonth('prev')}
                                        className="p-3 hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95"
                                    >
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <div className="bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3 min-w-[180px]">
                                        <h2 className="text-xl font-bold text-center text-white">
                                            {format(currentDate, "MMMM yyyy")}
                                        </h2>
                                    </div>
                                    <button
                                        onClick={() => navigateMonth('next')}
                                        className="p-3 hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95"
                                    >
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            {/* Decorative elements */}
                            <div className="absolute top-4 right-4 opacity-20">
                                <div className="flex space-x-2">
                                    <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                                    <div className="w-3 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                                    <div className="w-3 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                                </div>
                            </div>
                        </div>

                        {/* Calendar Grid */}
                        <div className="flex-1 p-6 bg-gradient-to-b from-white/50 to-transparent">
                            <div className="grid grid-cols-7 gap-2 h-full">
                                {/* Day Headers */}
                                {["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"].map((day, index) => (
                                    <div key={day} className="text-center py-4 mb-2">
                                        <div className={`text-sm font-bold px-3 py-2 rounded-xl ${index === 0 || index === 6
                                            ? 'bg-gradient-to-r from-pink-100 to-purple-100 text-purple-700'
                                            : 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700'
                                            }`}>
                                            {day.slice(0, 3)}
                                        </div>
                                    </div>
                                ))}

                                {/* Calendar Days */}
                                {days.map((day, dayIndex) => {
                                    const key = format(day, "MM-dd");
                                    const isToday = isSameDay(day, new Date());
                                    const isSelected = isSameDay(day, selectedDate);
                                    const birthdayNames = birthdaysByDate[key];
                                    const anniversaryNames = anniversariesByDate[key];
                                    const allEvents = eventsByDate[key];
                                    const isInCurrentMonth = isCurrentMonth(day);
                                    const hasBirthdays = birthdayNames && birthdayNames.length > 0;
                                    const hasAnniversaries = anniversaryNames && anniversaryNames.length > 0;
                                    const hasEvents = allEvents && allEvents.length > 0;
                                    const birthdayCount = birthdayNames ? birthdayNames.length : 0;
                                    const anniversaryCount = anniversaryNames ? anniversaryNames.length : 0;
                                    const totalEvents = allEvents ? allEvents.length : 0;

                                    return (
                                        <div
                                            key={day.toISOString()}
                                            onClick={() => setSelectedDate(day)}
                                            className={`
                                            calendar-day relative p-3 cursor-pointer transition-all duration-300 rounded-2xl min-h-[80px] flex flex-col
                                            transform hover:scale-105 hover:shadow-lg group
                                            ${isSelected
                                                    ? "bg-gradient-to-br from-purple-500 to-pink-500 text-white shadow-xl scale-105 ring-4 ring-purple-200"
                                                    : isToday
                                                        ? "bg-gradient-to-br from-yellow-400 to-orange-400 text-white shadow-lg ring-2 ring-yellow-200"
                                                        : hasEvents && isInCurrentMonth
                                                            ? "bg-gradient-to-br from-pink-100 to-red-100 hover:from-pink-200 hover:to-red-200 border-2 border-pink-300"
                                                            : isInCurrentMonth
                                                                ? "bg-white/70 hover:bg-white/90 border border-gray-200 hover:border-purple-300"
                                                                : "bg-gray-50/50 text-gray-400 border border-gray-100"
                                                }
                                        `}
                                            style={{
                                                animationDelay: `${dayIndex * 20}ms`
                                            }}
                                        >
                                            {/* Day Number */}
                                            <div className={`text-lg font-bold mb-1 ${isSelected || isToday ? "text-white" :
                                                isInCurrentMonth ? "text-gray-800" : "text-gray-400"
                                                }`}>
                                                {format(day, "d")}
                                            </div>

                                            {/* Event Indicators */}
                                            {hasEvents && isInCurrentMonth && (
                                                <div className="flex-1 flex flex-col justify-center space-y-1">
                                                    {totalEvents === 1 ? (
                                                        <div className={`text-xs font-medium truncate ${isSelected ? "text-purple-100" : "text-pink-700"
                                                            }`}>
                                                            {allEvents[0].replace('🎂 ', '').replace('💕 ', '')}
                                                        </div>
                                                    ) : totalEvents === 2 ? (
                                                        <div className="space-y-1">
                                                            {allEvents.slice(0, 2).map((event, i) => (
                                                                <div key={i} className={`text-xs font-medium truncate ${isSelected ? "text-purple-100" : "text-pink-700"
                                                                    }`}>
                                                                    {event.includes('🎂') ? '🎂' : '💕'} {event.replace('🎂 ', '').replace('💕 ', '').split(' ')[0]}
                                                                </div>
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <div className={`text-xs font-bold text-center ${isSelected ? "text-purple-100" : "text-pink-700"
                                                            }`}>
                                                            🎉 {totalEvents} events
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            {/* Today Indicator */}
                                            {isToday && (
                                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-300 rounded-full border-2 border-white animate-pulse"></div>
                                            )}

                                            {/* Birthday Dots */}
                                            {hasBirthdays && isInCurrentMonth && birthdayCount > 1 && (
                                                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                                                    {Array.from({ length: Math.min(birthdayCount, 3) }).map((_, i) => (
                                                        <div
                                                            key={i}
                                                            className={`w-2 h-2 rounded-full ${isSelected ? "bg-purple-200" : "bg-pink-400"
                                                                } animate-pulse`}
                                                            style={{ animationDelay: `${i * 200}ms` }}
                                                        ></div>
                                                    ))}
                                                    {birthdayCount > 3 && (
                                                        <div className={`text-xs font-bold ${isSelected ? "text-purple-200" : "text-pink-600"
                                                            }`}>
                                                            +{birthdayCount - 3}
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            {/* Hover Effect */}
                                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none"></div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Sidebar */}
                <div className="w-80 p-6 space-y-6">
                    {/* Selected Date Panel */}
                    <div className="sidebar-item bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 overflow-hidden relative">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-pink-50/50"></div>
                        <div className="relative">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="text-xl font-bold text-gray-900">
                                        {format(selectedDate, "MMMM d")}
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        {format(selectedDate, "EEEE, yyyy")}
                                    </p>
                                </div>
                            </div>

                            {selectedDateEvents.length > 0 ? (
                                <div className="space-y-3">
                                    {selectedDateEvents.map((event, index) => (
                                        <div
                                            key={index}
                                            className={`flex items-center space-x-4 p-4 rounded-2xl border hover:shadow-md transition-all duration-200 transform hover:scale-105 ${event.includes('🎂')
                                                ? 'bg-gradient-to-r from-purple-50 to-pink-50 border-purple-100'
                                                : 'bg-gradient-to-r from-rose-50 to-red-50 border-rose-100'
                                                }`}
                                        >
                                            <div className={`w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg ${event.includes('🎂')
                                                ? 'bg-gradient-to-br from-purple-500 to-pink-500'
                                                : 'bg-gradient-to-br from-rose-500 to-red-500'
                                                }`}>
                                                <span className="text-white text-lg font-bold">
                                                    {event.replace('🎂 ', '').replace('💕 ', '').charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                            <div className="flex-1">
                                                <div className="font-bold text-gray-900 text-lg">
                                                    {event.replace('🎂 ', '').replace('💕 ', '').split(' (')[0]}
                                                </div>
                                                <div className={`text-sm font-medium flex items-center ${event.includes('🎂') ? 'text-purple-600' : 'text-rose-600'
                                                    }`}>
                                                    {event.includes('🎂')
                                                        ? `🎂 Birthday ${isSameDay(selectedDate, new Date()) ? 'today' : 'on this day'}`
                                                        : `💕 ${event.includes('(') ? event.split('(')[1].replace(')', '') : 'Anniversary'} ${isSameDay(selectedDate, new Date()) ? 'today' : 'on this day'}`
                                                    }
                                                </div>
                                            </div>
                                            <div className="text-2xl animate-bounce">
                                                {event.includes('🎂') ? '🎉' : '💖'}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-inner">
                                        <svg className="w-10 h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <h4 className="text-xl font-bold text-gray-900 mb-2">No events</h4>
                                    <p className="text-gray-600 leading-relaxed">
                                        Select a different date to view birthdays and anniversaries or add some contacts to get started! 🎂💕
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* Monthly Summary */}
                        <div className="sidebar-item bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 overflow-hidden relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50"></div>
                            <div className="relative">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-900">
                                            {format(currentDate, "MMMM")} Summary
                                        </h3>
                                        <p className="text-sm text-gray-600">Monthly overview</p>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    {/* Total Events Summary */}
                                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-purple-50 via-pink-50 to-rose-50 rounded-2xl border border-purple-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                                                <span className="text-white text-sm font-bold">🎉</span>
                                            </div>
                                            <span className="font-medium text-gray-700">Total Events</span>
                                        </div>
                                        <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{totalEvents}</span>
                                    </div>

                                    {/* Birthdays */}
                                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-blue-500 rounded-xl flex items-center justify-center">
                                                <span className="text-white text-sm font-bold">🎂</span>
                                            </div>
                                            <span className="font-medium text-gray-700">Birthdays</span>
                                        </div>
                                        <span className="text-2xl font-bold text-blue-600">{totalBirthdays}</span>
                                    </div>

                                    {/* Anniversaries */}
                                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-rose-50 to-red-50 rounded-2xl border border-rose-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-gradient-to-r from-rose-500 to-red-500 rounded-xl flex items-center justify-center">
                                                <span className="text-white text-sm font-bold">💕</span>
                                            </div>
                                            <span className="font-medium text-gray-700">Anniversaries</span>
                                        </div>
                                        <span className="text-2xl font-bold text-rose-600">{totalAnniversaries}</span>
                                    </div>

                                    {/* Days with Events */}
                                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl border border-emerald-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-emerald-500 rounded-xl flex items-center justify-center">
                                                <span className="text-white text-sm font-bold">📅</span>
                                            </div>
                                            <span className="font-medium text-gray-700">Days with Events</span>
                                        </div>
                                        <span className="text-2xl font-bold text-emerald-600">{daysWithEvents}</span>
                                    </div>

                                    {/* Busiest Day */}
                                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-2xl border border-amber-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-xl flex items-center justify-center">
                                                <span className="text-white text-sm font-bold">⭐</span>
                                            </div>
                                            <span className="font-medium text-gray-700">Busiest Day</span>
                                        </div>
                                        <span className="text-lg font-bold text-amber-600">
                                            {busiestDay.count > 0 ? `${format(currentDate, "MMM")} ${busiestDay.day}` : 'None'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default BirthdayCalendar;
