import React from "react";
import { useContactsQuery } from "../hooks/useContacts";
import { format } from "date-fns";

const QuickStats = () => {
    const { data: contacts = [], isLoading, error } = useContactsQuery();
    const thisMonthKey = format(new Date(), "MM");

    const birthdaysThisMonth = contacts.filter(
        (c) => format(new Date(c.birthday), "MM") === thisMonthKey
    ).length;

    // Handle loading and error states
    if (isLoading) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Quick Stats</h2>
                <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
                    <p className="text-gray-500">Loading stats...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-semibold mb-4 text-gray-800">Quick Stats</h2>
                <div className="text-center py-8 text-red-500">
                    <div className="text-2xl mb-2">⚠️</div>
                    <p>Failed to load stats</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4 text-gray-800">Quick Stats</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{contacts.length}</div>
                    <div className="text-sm text-gray-600 mt-1">Total Contacts</div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">{birthdaysThisMonth}</div>
                    <div className="text-sm text-gray-600 mt-1">Birthdays This Month</div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">
                        {contacts.filter(c => {
                            const today = new Date();
                            const birthday = new Date(c.birthday);
                            const nextBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
                            if (nextBirthday < today) {
                                nextBirthday.setFullYear(today.getFullYear() + 1);
                            }
                            const daysUntil = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                            return daysUntil <= 7;
                        }).length}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">Upcoming (7 days)</div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600">
                        {new Set(contacts.map(c => c.category)).size}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">Categories</div>
                </div>
            </div>
        </div>
    );
};

export default QuickStats;
