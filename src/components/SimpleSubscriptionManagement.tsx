import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

// Import the same PLAN_DETAILS structure from SubscriptionManagement
interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface PlanDetails {
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  cta: string;
}

const PLAN_DETAILS: Record<string, PlanDetails> = {
  Free: {
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    description: 'Perfect for getting started',
    features: [
      { name: 'Up to 10 contacts', included: true },
      { name: 'Basic reminders', included: true },
      { name: 'Email support', included: true },
      { name: 'Advanced analytics', included: false },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Current Plan',
  },
  Standard: {
    name: 'Standard',
    price: { monthly: 7.99, yearly: 79.99 },
    description: 'Great for individuals',
    features: [
      { name: 'Up to 100 contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'Email support', included: true },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Upgrade to Standard',
  },
  Elite: {
    name: 'Elite',
    price: { monthly: 15.99, yearly: 159.99 },
    description: 'Perfect for power users',
    features: [
      { name: 'Up to 500 contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Priority email support', included: true },
      { name: 'Automated messaging', included: true },
      { name: 'Priority support', included: false },
    ],
    popular: true,
    cta: 'Upgrade to Elite',
  },
  Premium: {
    name: 'Premium',
    price: { monthly: 23.99, yearly: 239.99 },
    description: 'For teams and businesses',
    features: [
      { name: 'Unlimited contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Priority support', included: true },
      { name: 'Automated messaging', included: true },
      { name: 'Custom integrations', included: true },
    ],
    cta: 'Upgrade to Premium',
  },
};

const SimpleSubscriptionManagement: React.FC = () => {
  const { user } = useAuth();

  // Try to get real Stripe data, but fall back to demo data if it fails
  let subscription, hasActiveSubscription, isTrialActive, trialDaysLeft;
  let handleUpgrade, handleManageBilling, isManagingBilling;

  try {
    const stripeData = useSubscriptionStatus();
    const stripeActions = usePlanManagement();
    subscription = stripeData.subscription;
    hasActiveSubscription = stripeData.hasActiveSubscription;
    isTrialActive = stripeData.isTrialActive;
    trialDaysLeft = stripeData.trialDaysLeft;
    handleUpgrade = stripeActions.handleUpgrade;
    handleManageBilling = stripeActions.handleManageBilling;
    isManagingBilling = stripeActions.isManagingBilling;
  } catch (error) {
    console.warn('Stripe not available, using demo data:', error);
    // Demo data fallback
    subscription = null;
    hasActiveSubscription = true;
    isTrialActive = false;
    trialDaysLeft = 0;
    handleUpgrade = async () => alert('🎭 DEMO MODE: Upgrade functionality requires Stripe backend');
    handleManageBilling = async () => alert('🎭 DEMO MODE: Billing management requires Stripe backend');
    isManagingBilling = false;
  }

  const [showPlanModal, setShowPlanModal] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Premium Trial' : 'Free');

  // Get usage data (simulated for now)
  const usageData = {
    used: 106,
    total: 300,
    renewsMonthly: 50
  };

  const nextBillingDate = subscription?.current_period_end
    ? new Date(subscription.current_period_end * 1000)
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  const chargeAmount = subscription?.plan?.amount ? (subscription.plan.amount / 100) : 0;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(price);
  };

  // Get plan features from PLAN_DETAILS
  const getCurrentPlanFeatures = () => {
    if (isTrialActive) {
      return ['Unlimited contacts', 'Advanced analytics', 'Priority support'];
    }
    const planDetails = PLAN_DETAILS[currentPlan];
    return planDetails ? planDetails.features.filter(f => f.included).map(f => f.name) : [];
  };

  const handlePlanUpgrade = async (plan: 'Standard' | 'Elite' | 'Premium') => {
    setProcessingPlan(plan);
    try {
      await handleUpgrade(plan, 'monthly');
      setShowPlanModal(false);
    } catch (error) {
      console.error('Upgrade failed:', error);
    } finally {
      setProcessingPlan(null);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Demo Notice */}
      {!subscription && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Demo Mode</h3>
              <p className="text-sm text-yellow-700">Stripe backend not available. Showing demo subscription data.</p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">Subscription</h1>
        <p className="text-gray-600">Manage your subscription and billing details.</p>
      </div>

      {/* Usage and Billing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* User Messages Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">User Messages</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View usage
            </button>
          </div>

          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.001 8.001 0 01-7.93-6.94c-.42-.51-.93-.94-1.53-1.26A8.001 8.001 0 0121 12z" />
            </svg>
            <span className="text-2xl font-bold text-gray-900">{usageData.used}.00 available</span>
          </div>

          <p className="text-sm text-gray-600 mb-4">{usageData.renewsMonthly} renew monthly</p>

          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Used {usageData.used} of {usageData.total} this month</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${(usageData.used / usageData.total) * 100}%` }}
              ></div>
            </div>
          </div>

          <button className="w-full bg-green-50 text-green-700 border border-green-200 rounded-lg py-2 px-4 text-sm font-medium hover:bg-green-100 transition-colors">
            + Purchase additional user messages
          </button>
        </div>

        {/* Billing Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Billing</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              Payment history
            </button>
          </div>

          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm0 0v4a2 2 0 002 2h6a2 2 0 002-2v-4" />
            </svg>
            <span className="text-2xl font-bold text-gray-900">
              {nextBillingDate.toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
              })}
            </span>
          </div>

          <p className="text-sm text-gray-600 mb-4">Next Billing Date</p>

          <p className="text-sm text-gray-600 mb-4">
            Your card will be charged ${chargeAmount.toFixed(2)}
          </p>

          <div className="space-y-2">
            <button
              onClick={handleManageBilling}
              disabled={isManagingBilling}
              className="w-full bg-blue-50 text-blue-700 border border-blue-200 rounded-lg py-2 px-4 text-sm font-medium hover:bg-blue-100 transition-colors disabled:opacity-50"
            >
              {isManagingBilling ? 'Loading...' : 'Update Payment Method & Billing Info'}
            </button>
            <a
              href="/billing"
              className="block w-full text-center bg-gray-50 text-gray-700 border border-gray-200 rounded-lg py-2 px-4 text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              View Billing & Invoices
            </a>
          </div>
        </div>
      </div>

      {/* Current Plan Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Current plan</h3>

        <div className="border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">
              {currentPlan} Plan
            </h4>
            <button
              onClick={handleManageBilling}
              className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              Cancel subscription
            </button>
          </div>

          {/* Plan Features */}
          <div className="space-y-2 mb-6">
            {getCurrentPlanFeatures().slice(0, 3).map((feature, index) => (
              <div key={index} className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                {feature}
              </div>
            ))}
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              Show more
            </button>
          </div>

          <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
            <span>${chargeAmount.toFixed(2)}/user/mo • 1 seat purchased</span>
          </div>

          <div className="text-lg font-medium text-gray-900">
            Monthly total: ${chargeAmount.toFixed(2)}
          </div>
        </div>
      </div>

      {/* Change Subscription Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">Change your subscription</h3>
            <p className="text-sm text-gray-600">Switch plans or contact sales about Enterprise options</p>
          </div>
          <button
            onClick={() => setShowPlanModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Change plan
          </button>
        </div>
      </div>

      {/* Plan Change Modal */}
      {showPlanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Change Plan</h3>
              <button
                onClick={() => setShowPlanModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(PLAN_DETAILS)
                .filter(([planKey]) => planKey !== currentPlan && planKey !== 'Free')
                .map(([planKey, plan]) => (
                  <div
                    key={planKey}
                    className={`border rounded-lg p-4 ${plan.popular ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                      }`}
                  >
                    <div className="text-center mb-4">
                      <h4 className="font-medium text-gray-900">{plan.name}</h4>
                      <div className="text-2xl font-bold text-gray-900 mt-2">
                        {formatPrice(plan.price.monthly)}
                      </div>
                      <div className="text-sm text-gray-600">/month</div>
                    </div>

                    <ul className="space-y-1 mb-4 text-sm">
                      {plan.features.filter(f => f.included).slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="text-gray-600">{feature.name}</span>
                        </li>
                      ))}
                    </ul>

                    <button
                      onClick={() => handlePlanUpgrade(planKey as 'Standard' | 'Elite' | 'Premium')}
                      disabled={processingPlan === planKey}
                      className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors ${plan.popular
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                        } disabled:opacity-50`}
                    >
                      {processingPlan === planKey ? 'Processing...' : 'Upgrade'}
                    </button>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleSubscriptionManagement;
