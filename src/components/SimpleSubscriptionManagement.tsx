import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

const SimpleSubscriptionManagement: React.FC = () => {
  const { user } = useAuth();
  const { subscription, hasActiveSubscription, isTrialActive, trialDaysLeft } = useSubscriptionStatus();
  const { handleUpgrade, handleManageBilling, isManagingBilling } = usePlanManagement();

  const [showPlanModal, setShowPlanModal] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Premium Trial' : 'Community');
  
  // Get usage data (simulated for now)
  const usageData = {
    used: 106,
    total: 300,
    renewsMonthly: 50
  };

  const nextBillingDate = subscription?.current_period_end 
    ? new Date(subscription.current_period_end * 1000)
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  const chargeAmount = subscription?.plan?.amount ? (subscription.plan.amount / 100) : 0;

  const planFeatures = {
    'Community': ['50 user messages per month', 'Context Engine', 'MCP & Native Tools'],
    'Premium Trial': ['Unlimited contacts', 'Advanced analytics', 'Priority support'],
    'Standard': ['5,000 user messages per month', 'Context Engine', 'MCP & Native Tools'],
    'Elite': ['11,000 user messages per month', 'Advanced features', 'Priority support'],
    'Premium': ['Unlimited user messages', 'All features', 'Premium support']
  };

  const handlePlanUpgrade = async (plan: 'Standard' | 'Elite' | 'Premium') => {
    setProcessingPlan(plan);
    try {
      await handleUpgrade(plan, 'monthly');
      setShowPlanModal(false);
    } catch (error) {
      console.error('Upgrade failed:', error);
    } finally {
      setProcessingPlan(null);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">Subscription</h1>
        <p className="text-gray-600">Manage your subscription and billing details.</p>
      </div>

      {/* Usage and Billing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* User Messages Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">User Messages</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View usage
            </button>
          </div>
          
          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.001 8.001 0 01-7.93-6.94c-.42-.51-.93-.94-1.53-1.26A8.001 8.001 0 0121 12z" />
            </svg>
            <span className="text-2xl font-bold text-gray-900">{usageData.used}.00 available</span>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">{usageData.renewsMonthly} renew monthly</p>
          
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Used {usageData.used} of {usageData.total} this month</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${(usageData.used / usageData.total) * 100}%` }}
              ></div>
            </div>
          </div>
          
          <button className="w-full bg-green-50 text-green-700 border border-green-200 rounded-lg py-2 px-4 text-sm font-medium hover:bg-green-100 transition-colors">
            + Purchase additional user messages
          </button>
        </div>

        {/* Billing Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Billing</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              Payment history
            </button>
          </div>
          
          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm0 0v4a2 2 0 002 2h6a2 2 0 002-2v-4" />
            </svg>
            <span className="text-2xl font-bold text-gray-900">
              {nextBillingDate.toLocaleDateString('en-US', { 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
              })}
            </span>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">Next Billing Date</p>
          
          <p className="text-sm text-gray-600 mb-4">
            Your card will be charged ${chargeAmount.toFixed(2)}
          </p>
          
          <button 
            onClick={handleManageBilling}
            disabled={isManagingBilling}
            className="w-full bg-blue-50 text-blue-700 border border-blue-200 rounded-lg py-2 px-4 text-sm font-medium hover:bg-blue-100 transition-colors disabled:opacity-50"
          >
            {isManagingBilling ? 'Loading...' : 'Update Payment Method & Billing Info'}
          </button>
        </div>
      </div>

      {/* Current Plan Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Current plan</h3>
        
        <div className="border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">
              {currentPlan} Plan
            </h4>
            <button 
              onClick={handleManageBilling}
              className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              Cancel subscription
            </button>
          </div>
          
          {/* Plan Features */}
          <div className="space-y-2 mb-6">
            {(planFeatures[currentPlan as keyof typeof planFeatures] || []).slice(0, 3).map((feature, index) => (
              <div key={index} className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                {feature}
              </div>
            ))}
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              Show more
            </button>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
            <span>${chargeAmount.toFixed(2)}/user/mo • 1 seat purchased</span>
          </div>
          
          <div className="text-lg font-medium text-gray-900">
            Monthly total: ${chargeAmount.toFixed(2)}
          </div>
        </div>
      </div>

      {/* Change Subscription Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">Change your subscription</h3>
            <p className="text-sm text-gray-600">Switch plans or contact sales about Enterprise options</p>
          </div>
          <button 
            onClick={() => setShowPlanModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Change plan
          </button>
        </div>
      </div>

      {/* Plan Change Modal */}
      {showPlanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Change Plan</h3>
              <button
                onClick={() => setShowPlanModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { key: 'Standard', name: 'Standard', price: 20, features: ['5,000 messages/month', 'Basic support', 'Core features'] },
                { key: 'Elite', name: 'Elite', price: 40, features: ['11,000 messages/month', 'Priority support', 'Advanced features'], popular: true },
                { key: 'Premium', name: 'Premium', price: 80, features: ['Unlimited messages', 'Premium support', 'All features'] }
              ].filter(plan => plan.key !== currentPlan).map((plan) => (
                <div
                  key={plan.key}
                  className={`border rounded-lg p-4 ${
                    plan.popular ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="text-center mb-4">
                    <h4 className="font-medium text-gray-900">{plan.name}</h4>
                    <div className="text-2xl font-bold text-gray-900 mt-2">
                      ${plan.price}
                    </div>
                    <div className="text-sm text-gray-600">/month</div>
                  </div>
                  
                  <ul className="space-y-1 mb-4 text-sm">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button
                    onClick={() => handlePlanUpgrade(plan.key as 'Standard' | 'Elite' | 'Premium')}
                    disabled={processingPlan === plan.key}
                    className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                      plan.popular
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    } disabled:opacity-50`}
                  >
                    {processingPlan === plan.key ? 'Processing...' : 'Upgrade'}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleSubscriptionManagement;
