import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

interface TrialConversionFlowProps {
  onClose?: () => void;
  autoShow?: boolean;
}

const TrialConversionFlow: React.FC<TrialConversionFlowProps> = ({ onClose, autoShow = true }) => {
  const { user } = useAuth();
  const { isTrialActive, trialDaysLeft, hasActiveSubscription } = useSubscriptionStatus();
  const { handleUpgrade, handleStartTrial, isUpgrading, isStartingTrial } = usePlanManagement();
  
  const [showModal, setShowModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'Standard' | 'Elite' | 'Premium'>('Elite');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  // Auto-show logic based on trial status
  useEffect(() => {
    if (!autoShow || hasActiveSubscription) return;

    // Show trial start prompt for free users
    if (!isTrialActive && !hasActiveSubscription) {
      const hasSeenTrialPrompt = localStorage.getItem('hasSeenTrialPrompt');
      if (!hasSeenTrialPrompt) {
        setShowModal(true);
      }
    }

    // Show conversion prompt when trial is ending (last 3 days)
    if (isTrialActive && trialDaysLeft <= 3) {
      const hasSeenConversionPrompt = localStorage.getItem('hasSeenConversionPrompt');
      if (!hasSeenConversionPrompt) {
        setShowModal(true);
      }
    }
  }, [isTrialActive, trialDaysLeft, hasActiveSubscription, autoShow]);

  const handleStartTrialClick = async () => {
    try {
      await handleStartTrial();
      localStorage.setItem('hasSeenTrialPrompt', 'true');
      setShowModal(false);
      if (onClose) onClose();
    } catch (error) {
      console.error('Failed to start trial:', error);
    }
  };

  const handleUpgradeClick = async () => {
    try {
      await handleUpgrade(selectedPlan, billingCycle);
      localStorage.setItem('hasSeenConversionPrompt', 'true');
      setShowModal(false);
      if (onClose) onClose();
    } catch (error) {
      console.error('Failed to upgrade:', error);
    }
  };

  const handleCloseModal = () => {
    if (isTrialActive && trialDaysLeft <= 3) {
      localStorage.setItem('hasSeenConversionPrompt', 'true');
    } else {
      localStorage.setItem('hasSeenTrialPrompt', 'true');
    }
    setShowModal(false);
    if (onClose) onClose();
  };

  const getPlanPrice = (plan: 'Standard' | 'Elite' | 'Premium') => {
    const prices = {
      Standard: { monthly: 7.99, yearly: 79.99 },
      Elite: { monthly: 15.99, yearly: 159.99 },
      Premium: { monthly: 23.99, yearly: 239.99 },
    };
    return prices[plan][billingCycle];
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  if (!showModal || hasActiveSubscription) return null;

  // Trial start flow for free users
  if (!isTrialActive) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-md w-full p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Unlock Premium Features
            </h3>
            <p className="text-gray-600">
              Try all premium features free for 14 days. No credit card required.
            </p>
          </div>

          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-gray-700">
              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Unlimited contacts
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Automated messaging
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Advanced analytics
            </div>
            <div className="flex items-center text-sm text-gray-700">
              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Priority support
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleStartTrialClick}
              disabled={isStartingTrial}
              className="flex-1 bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {isStartingTrial ? 'Starting Trial...' : 'Start Free Trial'}
            </button>
            <button
              onClick={handleCloseModal}
              className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition-colors"
            >
              Maybe Later
            </button>
          </div>

          <p className="text-xs text-gray-500 text-center mt-4">
            Cancel anytime during the trial period
          </p>
        </div>
      </div>
    );
  }

  // Trial conversion flow for trial users
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-lg w-full p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Your Trial is Ending Soon
          </h3>
          <p className="text-gray-600">
            You have {trialDaysLeft} day{trialDaysLeft !== 1 ? 's' : ''} left in your trial. 
            Continue enjoying premium features by upgrading now.
          </p>
        </div>

        {/* Plan Selection */}
        <div className="mb-6">
          <div className="flex items-center justify-center space-x-4 mb-4">
            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingCycle === 'yearly' ? 'bg-purple-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm ${billingCycle === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                Save 17%
              </span>
            )}
          </div>

          <div className="space-y-3">
            {(['Standard', 'Elite', 'Premium'] as const).map((plan) => (
              <button
                key={plan}
                onClick={() => setSelectedPlan(plan)}
                className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                  selectedPlan === plan
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">{plan}</h4>
                    <p className="text-sm text-gray-600">
                      {plan === 'Standard' && 'Great for personal use'}
                      {plan === 'Elite' && 'Most popular choice'}
                      {plan === 'Premium' && 'Everything you need'}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900">
                      {formatPrice(getPlanPrice(plan))}
                    </div>
                    <div className="text-sm text-gray-600">
                      /{billingCycle === 'yearly' ? 'year' : 'month'}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={handleUpgradeClick}
            disabled={isUpgrading}
            className="flex-1 bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
            {isUpgrading ? 'Processing...' : `Upgrade to ${selectedPlan}`}
          </button>
          <button
            onClick={handleCloseModal}
            className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition-colors"
          >
            Continue Trial
          </button>
        </div>

        <p className="text-xs text-gray-500 text-center mt-4">
          You can cancel anytime. No long-term commitments.
        </p>
      </div>
    </div>
  );
};

export default TrialConversionFlow;
