import React from "react";
import { useCreateContact } from "../hooks/useContacts";
import { type Contact } from "../types";
import { v4 as uuidv4 } from "uuid";
import { useFormValidation } from "../hooks/useFormValidation";
import <PERSON><PERSON>ield from "./FormField";

const categories = ["Family", "Friends", "Colleagues", "Clients", "Members"] as const;

interface ContactFormData {
    name: string;
    birthday: string;
    category: typeof categories[number];
}

interface ContactFormProps {
    onSuccess?: () => void;
}

const ContactForm = ({ onSuccess }: ContactFormProps) => {
    const createContactMutation = useCreateContact();

    const {
        values,
        errors,
        touched,
        isValid,
        isSubmitting,
        setValue,
        setFieldTouched,
        handleSubmit,
        resetForm,
    } = useFormValidation<ContactFormData>(
        { name: '', birthday: '', category: 'Family' },
        {
            name: {
                required: true,
                minLength: 2,
                maxLength: 50,
                custom: (value: string) => {
                    if (!/^[a-zA-Z\s]+$/.test(value)) {
                        return 'Name can only contain letters and spaces';
                    }
                    return null;
                },
            },
            birthday: {
                required: true,
                custom: (value: string) => {
                    const date = new Date(value);
                    const today = new Date();
                    const minDate = new Date(today.getFullYear() - 120, today.getMonth(), today.getDate());

                    if (date > today) {
                        return 'Birthday cannot be in the future';
                    }
                    if (date < minDate) {
                        return 'Please enter a valid birth year';
                    }
                    return null;
                },
            },
            category: {
                required: true,
            },
        }
    );

    const onSubmit = async (formData: ContactFormData) => {
        const newContact: Omit<Contact, 'id'> = {
            name: formData.name.trim(),
            birthday: formData.birthday,
            category: formData.category,
        };

        try {
            await createContactMutation.mutateAsync(newContact);
            resetForm();
            onSuccess?.();
        } catch (error) {
            console.error('Failed to create contact:', error);
            // Error is handled by React Query
        }
    };

    return (
        <div className="bg-white">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    label="Name"
                    name="name"
                    type="text"
                    value={values.name}
                    onChange={(value) => setValue('name', value)}
                    onBlur={() => setFieldTouched('name')}
                    error={errors.name}
                    touched={touched.name}
                    required
                    placeholder="Enter contact name"
                />

                <FormField
                    label="Birthday"
                    name="birthday"
                    type="date"
                    value={values.birthday}
                    onChange={(value) => setValue('birthday', value)}
                    onBlur={() => setFieldTouched('birthday')}
                    error={errors.birthday}
                    touched={touched.birthday}
                    required
                />

                <FormField
                    label="Category"
                    name="category"
                    type="select"
                    value={values.category}
                    onChange={(value) => setValue('category', value as typeof categories[number])}
                    onBlur={() => setFieldTouched('category')}
                    error={errors.category}
                    touched={touched.category}
                    required
                    options={categories.map(cat => ({ value: cat, label: cat }))}
                    placeholder="Select a category"
                />

                <button
                    type="submit"
                    disabled={createContactMutation.isPending || isSubmitting || !isValid}
                    className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {createContactMutation.isPending || isSubmitting ? "Adding Contact..." : "Add Contact"}
                </button>
            </form>
        </div>
    );
};

export default ContactForm;
