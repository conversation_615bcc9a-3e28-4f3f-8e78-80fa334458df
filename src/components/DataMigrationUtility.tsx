import React, { useState, useEffect } from 'react';
import { dataMigrationService } from '../services/dataMigrationService';
import { migrationService } from '../services/migrationService';

const DataMigrationUtility: React.FC = () => {
  const [migrationStatus, setMigrationStatus] = useState<{
    hasLocalStorageData: boolean;
    hasSQLiteData: boolean;
    localStorageKeys: string[];
    sqliteStats: Record<string, number>;
  } | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [migrationLog, setMigrationLog] = useState<string[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    checkMigrationStatus();
  }, []);

  const checkMigrationStatus = async () => {
    try {
      const status = await dataMigrationService.getMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Error checking migration status:', error);
      addToLog('Error checking migration status: ' + error);
    }
  };

  const addToLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setMigrationLog(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const runMigration = async () => {
    setIsLoading(true);
    setMigrationLog([]);
    
    try {
      addToLog('Starting data migration from localStorage to SQLite...');
      
      // Run database migrations first
      addToLog('Running database schema migrations...');
      await migrationService.runMigrations();
      addToLog('Database schema migrations completed');
      
      // Run data migration
      addToLog('Migrating data from localStorage...');
      await dataMigrationService.migrateFromLocalStorage();
      addToLog('Data migration completed successfully!');
      
      // Update status
      await checkMigrationStatus();
      
      addToLog('Migration process finished. You can now use the SQLite database.');
    } catch (error) {
      console.error('Migration failed:', error);
      addToLog('Migration failed: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLocalStorageData = async () => {
    if (!confirm('Are you sure you want to clear localStorage data? This cannot be undone. Make sure your data has been successfully migrated to SQLite first.')) {
      return;
    }
    
    setIsLoading(true);
    try {
      addToLog('Clearing localStorage data...');
      await dataMigrationService.clearLocalStorageData();
      addToLog('localStorage data cleared successfully');
      await checkMigrationStatus();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      addToLog('Error clearing localStorage: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetDatabase = async () => {
    if (!confirm('Are you sure you want to reset the SQLite database? This will delete all data and cannot be undone.')) {
      return;
    }
    
    setIsLoading(true);
    try {
      addToLog('Resetting SQLite database...');
      await migrationService.resetDatabase();
      addToLog('Database reset completed');
      await checkMigrationStatus();
    } catch (error) {
      console.error('Error resetting database:', error);
      addToLog('Error resetting database: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const restoreFromBackup = async () => {
    if (!confirm('Are you sure you want to restore from backup? This will overwrite current localStorage data.')) {
      return;
    }
    
    setIsLoading(true);
    try {
      addToLog('Restoring from backup...');
      await dataMigrationService.restoreFromBackup();
      addToLog('Backup restored successfully');
      await checkMigrationStatus();
    } catch (error) {
      console.error('Error restoring backup:', error);
      addToLog('Error restoring backup: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!migrationStatus) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <h2 className="text-xl font-bold mb-4">Data Migration Utility</h2>
        <p>Loading migration status...</p>
      </div>
    );
  }

  const needsMigration = migrationStatus.hasLocalStorageData && !migrationStatus.hasSQLiteData;
  const migrationComplete = migrationStatus.hasSQLiteData;

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">🔄 Data Migration Utility</h2>
      
      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">localStorage Data</h3>
          <p className="text-sm text-blue-600">
            {migrationStatus.hasLocalStorageData 
              ? `Found ${migrationStatus.localStorageKeys.length} data keys`
              : 'No localStorage data found'
            }
          </p>
        </div>
        
        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">SQLite Database</h3>
          <p className="text-sm text-green-600">
            {migrationStatus.hasSQLiteData 
              ? `Database contains ${Object.values(migrationStatus.sqliteStats).reduce((a, b) => a + b, 0)} records`
              : 'No SQLite data found'
            }
          </p>
        </div>
      </div>

      {/* Migration Actions */}
      <div className="space-y-4 mb-6">
        {needsMigration && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Migration Required</h3>
            <p className="text-sm text-yellow-700 mb-3">
              You have data in localStorage that needs to be migrated to SQLite for better performance and reliability.
            </p>
            <button
              onClick={runMigration}
              disabled={isLoading}
              className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 disabled:opacity-50"
            >
              {isLoading ? 'Migrating...' : 'Start Migration'}
            </button>
          </div>
        )}

        {migrationComplete && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">✅ Migration Complete</h3>
            <p className="text-sm text-green-700">
              Your data has been successfully migrated to SQLite. The app is now using the SQLite database.
            </p>
          </div>
        )}
      </div>

      {/* Advanced Options */}
      <div className="border-t pt-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-sm text-gray-600 hover:text-gray-800 mb-4"
        >
          {showAdvanced ? '▼' : '▶'} Advanced Options
        </button>

        {showAdvanced && (
          <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <button
                onClick={clearLocalStorageData}
                disabled={isLoading || !migrationStatus.hasLocalStorageData}
                className="bg-orange-600 text-white px-3 py-2 rounded text-sm hover:bg-orange-700 disabled:opacity-50"
              >
                Clear localStorage
              </button>
              
              <button
                onClick={resetDatabase}
                disabled={isLoading}
                className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 disabled:opacity-50"
              >
                Reset Database
              </button>
              
              <button
                onClick={restoreFromBackup}
                disabled={isLoading}
                className="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                Restore Backup
              </button>
            </div>
            
            <p className="text-xs text-gray-500">
              ⚠️ Advanced options can cause data loss. Use with caution.
            </p>
          </div>
        )}
      </div>

      {/* Migration Log */}
      {migrationLog.length > 0 && (
        <div className="mt-6">
          <h3 className="font-semibold text-gray-800 mb-2">Migration Log</h3>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono max-h-64 overflow-y-auto">
            {migrationLog.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
      )}

      {/* Data Details */}
      {showAdvanced && (
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">localStorage Keys</h4>
            <div className="bg-gray-100 p-3 rounded text-sm max-h-32 overflow-y-auto">
              {migrationStatus.localStorageKeys.length > 0 ? (
                migrationStatus.localStorageKeys.map(key => (
                  <div key={key} className="text-gray-600">{key}</div>
                ))
              ) : (
                <div className="text-gray-500">No localStorage keys found</div>
              )}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">SQLite Tables</h4>
            <div className="bg-gray-100 p-3 rounded text-sm max-h-32 overflow-y-auto">
              {Object.entries(migrationStatus.sqliteStats).map(([table, count]) => (
                <div key={table} className="text-gray-600 flex justify-between">
                  <span>{table}</span>
                  <span>{count} records</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataMigrationUtility;
