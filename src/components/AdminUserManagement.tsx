import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { type User, type UserRole, type Organization } from '../types';
import { userManagementService } from '../services/userManagementService';

interface AdminUserManagementProps {
  onClose?: () => void;
}

const AdminUserManagement: React.FC<AdminUserManagementProps> = ({ onClose }) => {
  const { user: currentUser, isSystemAdmin, isChurchAdmin } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showCreateOrg, setShowCreateOrg] = useState(false);
  const [activeTab, setActiveTab] = useState<'users' | 'organizations'>('users');

  // Form states
  const [newUser, setNewUser] = useState({
    email: '',
    name: '',
    role: 'user' as UserRole,
    organizationId: '',
  });

  const [newOrg, setNewOrg] = useState({
    name: '',
    type: 'church' as Organization['type'],
    description: '',
    adminEmail: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersData, orgsData] = await Promise.all([
        userManagementService.getAllUsers(),
        userManagementService.getAllOrganizations(),
      ]);

      // Filter users based on current user's role
      if (isSystemAdmin) {
        setUsers(usersData);
        setOrganizations(orgsData);
      } else if (isChurchAdmin && currentUser?.organizationId) {
        // Church admins can only see users in their organization
        const orgUsers = usersData.filter(user =>
          user.organizationId === currentUser.organizationId || user.email === currentUser.email
        );
        setUsers(orgUsers);
        setOrganizations(orgsData.filter(org => org.id === currentUser.organizationId));
      }
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await userManagementService.createUser({
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        organizationId: newUser.organizationId || undefined,
      });

      setNewUser({ email: '', name: '', role: 'user', organizationId: '' });
      setShowCreateUser(false);
      await loadData();
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await userManagementService.createOrganization({
        name: newOrg.name,
        type: newOrg.type,
        description: newOrg.description,
        adminEmail: newOrg.adminEmail,
        settings: {
          allowMemberSelfRegistration: false,
          requireAdminApproval: true,
          defaultContactCategory: 'Members',
          enableAnniversaries: true,
          enableLandmarkBirthdays: true,
          timezone: 'America/New_York',
          emailNotifications: true,
        },
      });

      setNewOrg({ name: '', type: 'church', description: '', adminEmail: '' });
      setShowCreateOrg(false);
      await loadData();
    } catch (error) {
      console.error('Error creating organization:', error);
    }
  };

  const handleUpdateUserRole = async (email: string, newRole: UserRole) => {
    try {
      await userManagementService.updateUserRole(email, newRole);
      await loadData();
    } catch (error) {
      console.error('Error updating user role:', error);
    }
  };

  const handleToggleUserStatus = async (email: string, isActive: boolean) => {
    try {
      if (isActive) {
        await userManagementService.deactivateUser(email);
      } else {
        await userManagementService.activateUser(email);
      }
      await loadData();
    } catch (error) {
      console.error('Error toggling user status:', error);
    }
  };

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'system_admin': return 'bg-red-100 text-red-800';
      case 'church_admin': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (isActive?: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2">Loading admin data...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {isSystemAdmin ? 'System Administration' : 'Organization Management'}
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('users')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'users'
              ? 'border-purple-500 text-purple-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
          >
            👥 Users ({users.length})
          </button>
          {isSystemAdmin && (
            <button
              onClick={() => setActiveTab('organizations')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'organizations'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
            >
              🏢 Organizations ({organizations.length})
            </button>
          )}
        </nav>
      </div>

      {/* Users Tab */}
      {activeTab === 'users' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">User Management</h3>
            {isSystemAdmin && (
              <button
                onClick={() => setShowCreateUser(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
              >
                + Add User
              </button>
            )}
          </div>

          {/* Users Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Organization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  {isSystemAdmin && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => {
                  const userOrg = organizations.find(org => org.id === user.organizationId);
                  return (
                    <tr key={user.email} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.name || 'No name'}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user.role || 'user')}`}>
                          {user.role || 'user'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {userOrg?.name || 'No organization'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(user.isActive)}`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                      </td>
                      {isSystemAdmin && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <select
                              value={user.role || 'user'}
                              onChange={(e) => handleUpdateUserRole(user.email, e.target.value as UserRole)}
                              className="text-xs border rounded px-2 py-1"
                            >
                              <option value="user">User</option>
                              <option value="church_admin">Church Admin</option>
                              <option value="system_admin">System Admin</option>
                            </select>
                            <button
                              onClick={() => handleToggleUserStatus(user.email, user.isActive || false)}
                              className={`text-xs px-2 py-1 rounded ${user.isActive
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                                }`}
                            >
                              {user.isActive ? 'Deactivate' : 'Activate'}
                            </button>
                          </div>
                        </td>
                      )}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Organizations Tab */}
      {activeTab === 'organizations' && isSystemAdmin && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Organization Management</h3>
            <button
              onClick={() => setShowCreateOrg(true)}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
            >
              + Add Organization
            </button>
          </div>

          {/* Organizations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {organizations.map((org) => (
              <div key={org.id} className="border rounded-lg p-4 hover:shadow-md">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-gray-900">{org.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${org.type === 'church' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                    {org.type}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{org.description}</p>
                <div className="text-xs text-gray-500">
                  <div>Admin: {org.adminEmail}</div>
                  <div>Created: {new Date(org.createdAt).toLocaleDateString()}</div>
                  <div>Users: {users.filter(u => u.organizationId === org.id).length}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Create User Modal */}
      {showCreateUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New User</h3>
            <form onSubmit={handleCreateUser}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    required
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    value={newUser.name}
                    onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Role</label>
                  <select
                    value={newUser.role}
                    onChange={(e) => setNewUser({ ...newUser, role: e.target.value as UserRole })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="user">User</option>
                    <option value="church_admin">Church Admin</option>
                    <option value="system_admin">System Admin</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Organization</label>
                  <select
                    value={newUser.organizationId}
                    onChange={(e) => setNewUser({ ...newUser, organizationId: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">No organization</option>
                    {organizations.map((org) => (
                      <option key={org.id} value={org.id}>{org.name}</option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowCreateUser(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Create User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminUserManagement;
