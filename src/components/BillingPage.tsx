import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

const BillingPage: React.FC = () => {
  const { user } = useAuth();
  const { subscription, customer, hasActiveSubscription, isTrialActive, trialDaysLeft } = useSubscriptionStatus();
  const { handleManageBilling, isManagingBilling } = usePlanManagement();

  const [showCancelModal, setShowCancelModal] = useState(false);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Premium Trial' : 'Free');
  const nextBillingDate = subscription?.current_period_end 
    ? new Date(subscription.current_period_end * 1000)
    : null;
  const chargeAmount = subscription?.plan?.amount ? (subscription.plan.amount / 100) : 0;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric',
      month: 'long', 
      day: 'numeric' 
    });
  };

  const handleCancelSubscription = async () => {
    try {
      await handleManageBilling(); // This opens Stripe billing portal where they can cancel
      setShowCancelModal(false);
    } catch (error) {
      console.error('Failed to open billing portal:', error);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">Please log in to view your billing information.</p>
          <a href="/login" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button 
              onClick={() => window.history.back()}
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 className="text-2xl font-semibold text-gray-900">Billing & Invoices</h1>
          </div>
          <p className="text-gray-600">Manage your billing information and view payment history.</p>
        </div>

        {/* Current Subscription Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Current Subscription</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Plan</h3>
              <p className="text-lg font-semibold text-gray-900">{currentPlan}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Status</h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                hasActiveSubscription 
                  ? 'bg-green-100 text-green-800' 
                  : isTrialActive 
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
              }`}>
                {hasActiveSubscription ? 'Active' : isTrialActive ? `Trial (${trialDaysLeft} days left)` : 'Free'}
              </span>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                {isTrialActive ? 'Trial Ends' : 'Next Billing'}
              </h3>
              <p className="text-lg font-semibold text-gray-900">
                {nextBillingDate ? formatDate(nextBillingDate) : 'N/A'}
              </p>
            </div>
          </div>

          {hasActiveSubscription && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Monthly charge</p>
                  <p className="text-lg font-semibold text-gray-900">${chargeAmount.toFixed(2)}</p>
                </div>
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="text-red-600 hover:text-red-700 text-sm font-medium"
                >
                  Cancel Subscription
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Payment Method & Billing Portal */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Payment & Billing Management</h2>
          <p className="text-gray-600 mb-4">
            Manage your payment methods, update billing information, download invoices, and view payment history.
          </p>
          
          <button
            onClick={handleManageBilling}
            disabled={isManagingBilling}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isManagingBilling ? 'Opening...' : 'Open Billing Portal'}
          </button>
          
          <p className="text-xs text-gray-500 mt-2">
            You'll be redirected to Stripe's secure billing portal to manage your payment information.
          </p>
        </div>

        {/* Billing Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Billing Information</h2>
          
          <div className="space-y-3">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Email</h3>
              <p className="text-gray-900">{user.email}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Customer ID</h3>
              <p className="text-gray-900 font-mono text-sm">{customer?.id || 'Not available'}</p>
            </div>
            
            {subscription && (
              <div>
                <h3 className="text-sm font-medium text-gray-500">Subscription ID</h3>
                <p className="text-gray-900 font-mono text-sm">{subscription.id}</p>
              </div>
            )}
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">Need Help?</h2>
          <p className="text-gray-600 mb-4">
            If you have questions about your billing or need assistance, we're here to help.
          </p>
          <div className="flex space-x-4">
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Contact Support
            </a>
            <a
              href="/subscription"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              View Plans
            </a>
          </div>
        </div>

        {/* Cancel Subscription Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Cancel Subscription</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to cancel your subscription? You'll be redirected to the billing portal 
                where you can manage your subscription and see cancellation options.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Keep Subscription
                </button>
                <button
                  onClick={handleCancelSubscription}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Manage in Portal
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BillingPage;
