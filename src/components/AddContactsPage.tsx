import React, { useState } from "react";
import { useCreateContacts } from "../hooks/useContacts";
import { type Contact } from "../types";
import <PERSON>Field from "./FormField";
import {  paginatedContacts} from "ValidationPage";

const categories = ["Family", "Friends", "Colleagues", "Clients", "Members"] as const;

interface ContactFormData {
    name: string;
    birthday: string;
    category: typeof categories[number];
    email?: string;
    phone?: string;
    notes?: string;
    // Anniversary fields
    anniversaryDate?: string;
    anniversaryType?: "Wedding" | "Dating" | "Business" | "Engagement" | "Other";
    partnerName?: string;
    anniversaryNotes?: string;
}

const AddContactsPage = () => {
    const createContactsMutation = useCreateContacts();
    const [contacts, setContacts] = useState<ContactFormData[]>([
        {
            name: '',
            birthday: '',
            category: 'Family',
            email: '',
            phone: '',
            notes: '',
            anniversaryDate: '',
            anniversaryType: 'Wedding',
            partnerName: '',
            anniversaryNotes: ''
        }
    ]);

    const [successMessage, setSuccessMessage] = useState('');

    const addContactRow = () => {
        setContacts([...contacts, {
            name: '',
            birthday: '',
            category: 'Family',
            email: '',
            phone: '',
            notes: '',
            anniversaryDate: '',
            anniversaryType: 'Wedding',
            partnerName: '',
            anniversaryNotes: ''
        }]);
    };

    const removeContactRow = (index: number) => {
        if (contacts.length > 1) {
            setContacts(contacts.filter((_, i) => i !== index));
        }
    };

    const updateContact = (index: number, field: keyof ContactFormData, value: string) => {
        const updatedContacts = [...contacts];
        updatedContacts[index] = { ...updatedContacts[index], [field]: value };
        setContacts(updatedContacts);
    };

    const validateContact = (contact: ContactFormData) => {
        const errors: string[] = [];

        if (!contact.name.trim()) {
            errors.push("Name is required");
        } else if (contact.name.trim().length < 2) {
            errors.push("Name must be at least 2 characters");
        } else if (!/^[a-zA-Z\s]+$/.test(contact.name.trim())) {
            errors.push("Name can only contain letters and spaces");
        }

        if (!contact.birthday) {
            errors.push("Birthday is required");
        } else {
            const date = new Date(contact.birthday);
            const today = new Date();
            if (date > today) {
                errors.push("Birthday cannot be in the future");
            }
        }

        if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
            errors.push("Invalid email format");
        }

        return errors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSuccessMessage('');

        // Validate all contacts
        const validContacts: ContactFormData[] = [];
        const errors: { [key: number]: string[]; } = {};

        contacts.forEach((contact, index) => {
            const contactErrors = validateContact(contact);
            if (contactErrors.length > 0) {
                errors[index] = contactErrors;
            } else if (contact.name.trim()) { // Only add contacts with names
                validContacts.push(contact);
            }
        });

        if (Object.keys(errors).length > 0) {
            // Error handled by React Query
            return;
        }

        if (validContacts.length === 0) {
            // Error handled by React Query
            return;
        }

        // Add all valid contacts using React Query
        try {
            const contactsToCreate = validContacts.map(contactData => ({
                name: contactData.name.trim(),
                birthday: contactData.birthday,
                category: contactData.category,
                ...(contactData.anniversaryDate && {
                    anniversaryDate: contactData.anniversaryDate,
                    anniversaryType: contactData.anniversaryType || 'Wedding',
                    partnerName: contactData.partnerName || '',
                    anniversaryNotes: contactData.anniversaryNotes || ''
                })
            }));

            await createContactsMutation.mutateAsync(contactsToCreate);

            setSuccessMessage(`Successfully added ${validContacts.length} contact${validContacts.length > 1 ? 's' : ''}!`);

            // Reset form
            setContacts([{
                name: '',
                birthday: '',
                category: 'Family',
                email: '',
                phone: '',
                notes: '',
                anniversaryDate: '',
                anniversaryType: 'Wedding',
                partnerName: '',
                anniversaryNotes: ''
            }]);

            // Clear success message after 3 seconds
            setTimeout(() => setSuccessMessage(''), 3000);
        } catch (error) {
            console.error('Error adding contacts:', error);
            setSuccessMessage('Failed to add contacts. Please try again.');
        }


    };

    const downloadSampleCSV = () => {
        const sampleData = [
            ['Name', 'Birthday', 'Category', 'Email', 'Phone', 'Notes'],
            ['John Doe', '1990-01-15', 'Family', '<EMAIL>', '+1234567890', 'Brother'],
            ['Jane Smith', '1985-03-22', 'Friends', '<EMAIL>', '', 'College friend'],
            ['Mike Johnson', '1992-07-08', 'Colleagues', '<EMAIL>', '+0987654321', 'Team lead'],
            ['Sarah Wilson', '1988-11-30', 'Clients', '<EMAIL>', '', 'Important client']
        ];

        const csvContent = sampleData.map(row =>
            row.map(field => `"${field}"`).join(',')
        ).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'contacts_sample.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const importFromCSV = () => {
        // Create a file input element
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv';
        input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const csv = event.target?.result as string;
                        const lines = csv.split('\n').filter(line => line.trim()); // Remove empty lines

                        if (lines.length < 2) {
                            alert('CSV file must have at least a header row and one data row.');
                            return;
                        }

                        // Parse CSV properly handling quoted fields
                        const parseCSVLine = (line: string): string[] => {
                            const result: string[] = [];
                            let current = '';
                            let inQuotes = false;

                            for (let i = 0; i < line.length; i++) {
                                const char = line[i];

                                if (char === '"') {
                                    inQuotes = !inQuotes;
                                } else if (char === ',' && !inQuotes) {
                                    result.push(current.trim());
                                    current = '';
                                } else {
                                    current += char;
                                }
                            }
                            result.push(current.trim());
                            return result;
                        };

                        const headers = parseCSVLine(lines[0]).map(h => h.toLowerCase().replace(/"/g, ''));
                        const importedContacts: ContactFormData[] = [];

                        for (let i = 1; i < lines.length; i++) {
                            const values = parseCSVLine(lines[i]).map(v => v.replace(/"/g, ''));

                            if (values.length >= 1 && values[0]) { // At least name
                                // Find column indices
                                const nameIndex = headers.findIndex(h => h.includes('name')) || 0;
                                const birthdayIndex = headers.findIndex(h => h.includes('birthday') || h.includes('birth')) || 1;
                                const categoryIndex = headers.findIndex(h => h.includes('category') || h.includes('type'));
                                const emailIndex = headers.findIndex(h => h.includes('email') || h.includes('mail'));
                                const phoneIndex = headers.findIndex(h => h.includes('phone') || h.includes('tel'));
                                const notesIndex = headers.findIndex(h => h.includes('note') || h.includes('comment'));
                                const anniversaryDateIndex = headers.findIndex(h => h.includes('anniversary') && (h.includes('date') || h.includes('day')));
                                const anniversaryTypeIndex = headers.findIndex(h => h.includes('anniversary') && h.includes('type'));
                                const partnerNameIndex = headers.findIndex(h => h.includes('partner') || h.includes('spouse'));
                                const anniversaryNotesIndex = headers.findIndex(h => h.includes('anniversary') && (h.includes('note') || h.includes('comment')));

                                // Validate category
                                let category = categoryIndex >= 0 ? values[categoryIndex] : 'Family';
                                if (!categories.includes(category as typeof categories[number])) {
                                    category = 'Family'; // Default fallback
                                }

                                // Validate birthday format
                                let birthday = birthdayIndex >= 0 ? values[birthdayIndex] : '';
                                if (birthday && !birthday.match(/^\d{4}-\d{2}-\d{2}$/)) {
                                    // Try to convert common date formats to YYYY-MM-DD
                                    const date = new Date(birthday);
                                    if (!isNaN(date.getTime())) {
                                        birthday = date.toISOString().split('T')[0];
                                    } else {
                                        birthday = ''; // Invalid date
                                    }
                                }

                                // Validate anniversary date format
                                let anniversaryDate = anniversaryDateIndex >= 0 ? values[anniversaryDateIndex] : '';
                                if (anniversaryDate && !anniversaryDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
                                    // Try to convert common date formats to YYYY-MM-DD
                                    const date = new Date(anniversaryDate);
                                    if (!isNaN(date.getTime())) {
                                        anniversaryDate = date.toISOString().split('T')[0];
                                    } else {
                                        anniversaryDate = ''; // Invalid date
                                    }
                                }

                                // Validate anniversary type
                                let anniversaryType = anniversaryTypeIndex >= 0 ? values[anniversaryTypeIndex] : 'Wedding';
                                const validTypes = ['Wedding', 'Dating', 'Business', 'Engagement', 'Other'];
                                if (!validTypes.includes(anniversaryType)) {
                                    anniversaryType = 'Wedding'; // Default fallback
                                }

                                const contact: ContactFormData = {
                                    name: values[nameIndex] || '',
                                    birthday: birthday,
                                    category: category as typeof categories[number],
                                    email: emailIndex >= 0 ? values[emailIndex] : '',
                                    phone: phoneIndex >= 0 ? values[phoneIndex] : '',
                                    notes: notesIndex >= 0 ? values[notesIndex] : '',
                                    anniversaryDate: anniversaryDate,
                                    anniversaryType: anniversaryType as any,
                                    partnerName: partnerNameIndex >= 0 ? values[partnerNameIndex] : '',
                                    anniversaryNotes: anniversaryNotesIndex >= 0 ? values[anniversaryNotesIndex] : ''
                                };

                                if (contact.name.trim()) {
                                    importedContacts.push(contact);
                                }
                            }
                        }

                        if (importedContacts.length > 0) {
                            setContacts(importedContacts);
                            setSuccessMessage(`Successfully imported ${importedContacts.length} contact${importedContacts.length > 1 ? 's' : ''} from CSV!`);
                            setTimeout(() => setSuccessMessage(''), 3000);
                        } else {
                            alert('No valid contacts found in the CSV file. Please check the format and try again.');
                        }
                    } catch (error) {
                        console.error('Error parsing CSV:', error);
                        alert('Error parsing CSV file. Please check the format and try again.');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-teal-50 via-cyan-50 to-blue-50 p-6">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">👥</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-teal-600 via-cyan-600 to-blue-600 bg-clip-text text-transparent mb-3">
                        Add Contacts
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Add multiple contacts at once to your birthday list
                    </p>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-4 mb-6">
                    <button
                        type="button"
                        onClick={addContactRow}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center"
                    >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Add Row
                    </button>

                    <button
                        type="button"
                        onClick={downloadSampleCSV}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                    >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        Download Sample CSV
                    </button>

                    <button
                        type="button"
                        onClick={importFromCSV}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                    >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        Import CSV
                    </button>
                </div>

                {/* Success Message */}
                {successMessage && (
                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center">
                            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="text-green-700 font-medium">{successMessage}</span>
                        </div>
                    </div>
                )}

                {/* Form */}
                <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border">
                    <div className="p-6 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">Contact Information</h2>
                        <p className="text-sm text-gray-600 mt-1">Fill in the details for each contact you want to add</p>
                    </div>

                    <div className="p-6">
                        {/* Table Header */}
                        <div className="hidden lg:grid lg:grid-cols-11 gap-4 mb-4 text-sm font-medium text-gray-700">
                            <div>Name *</div>
                            <div>Birthday *</div>
                            <div>Category</div>
                            <div>Email</div>
                            <div>Phone</div>
                            <div>Notes</div>
                            <div>Anniversary Date</div>
                            <div>Anniversary Type</div>
                            <div>Partner Name</div>
                            <div>Anniversary Notes</div>
                            <div>Actions</div>
                        </div>

                        {/* Contact Rows */}
                        <div className="space-y-4">
                            {paginatedContacts.map((contact, displayIndex) => {
                                const actualIndex = startIndex + displayIndex;
                                return (
                                    <div key={actualIndex} className="grid grid-cols-1 lg:grid-cols-11 gap-4 p-4 border border-gray-200 rounded-lg">
                                        {/* Name */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Name *</label>
                                            <input
                                                type="text"
                                                value={contact.name}
                                                onChange={(e) => updateContact(displayIndex, 'name', e.target.value)}
                                                placeholder="Enter name"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                                required
                                            />
                                        </div>

                                        {/* Birthday */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Birthday *</label>
                                            <input
                                                type="date"
                                                value={contact.birthday}
                                                onChange={(e) => updateContact(displayIndex, 'birthday', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                                required
                                            />
                                        </div>

                                        {/* Category */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Category</label>
                                            <select
                                                value={contact.category}
                                                onChange={(e) => updateContact(displayIndex, 'category', e.target.value as typeof categories[number])}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            >
                                                {categories.map(cat => (
                                                    <option key={cat} value={cat}>{cat}</option>
                                                ))}
                                            </select>
                                        </div>

                                        {/* Email */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Email</label>
                                            <input
                                                type="email"
                                                value={contact.email}
                                                onChange={(e) => updateContact(displayIndex, 'email', e.target.value)}
                                                placeholder="<EMAIL>"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Phone */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Phone</label>
                                            <input
                                                type="tel"
                                                value={contact.phone}
                                                onChange={(e) => updateContact(displayIndex, 'phone', e.target.value)}
                                                placeholder="+1234567890"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Notes */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Notes</label>
                                            <input
                                                type="text"
                                                value={contact.notes}
                                                onChange={(e) => updateContact(displayIndex, 'notes', e.target.value)}
                                                placeholder="Optional notes"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Anniversary Date */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Anniversary Date</label>
                                            <input
                                                type="date"
                                                value={contact.anniversaryDate || ''}
                                                onChange={(e) => updateContact(displayIndex, 'anniversaryDate', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Anniversary Type */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Anniversary Type</label>
                                            <select
                                                value={contact.anniversaryType || 'Wedding'}
                                                onChange={(e) => updateContact(displayIndex, 'anniversaryType', e.target.value as any)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            >
                                                <option value="Wedding">Wedding</option>
                                                <option value="Dating">Dating</option>
                                                <option value="Engagement">Engagement</option>
                                                <option value="Business">Business</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>

                                        {/* Partner Name */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Partner Name</label>
                                            <input
                                                type="text"
                                                value={contact.partnerName || ''}
                                                onChange={(e) => updateContact(displayIndex, 'partnerName', e.target.value)}
                                                placeholder="Partner's name"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Anniversary Notes */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 lg:hidden">Anniversary Notes</label>
                                            <input
                                                type="text"
                                                value={contact.anniversaryNotes || ''}
                                                onChange={(e) => updateContact(displayIndex, 'anniversaryNotes', e.target.value)}
                                                placeholder="Anniversary notes"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center justify-center">
                                            {contacts.length > 1 && (
                                                <button
                                                    type="button"
                                                    onClick={() => removeContactRow(actualIndex)}
                                                    className="text-red-600 hover:text-red-800 p-1"
                                                    title="Remove this contact"
                                                >
                                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                    </svg>
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Submit Button */}
                        <div className="mt-6 flex justify-end">
                            <button
                                type="submit"
                                disabled={createContactsMutation.isPending || contacts.every(c => !c.name.trim())}
                                className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                            >
                                {createContactsMutation.isPending ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Adding Contacts...
                                    </>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                        </svg>
                                        Add All Contacts
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </form>

                {/* Help Section */}
                <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Tips for Adding Multiple Contacts</h3>
                    <ul className="text-blue-800 space-y-1 text-sm">
                        <li>• Use the "Add Row" button to add more contact fields</li>
                        <li>• <strong>Download Sample CSV</strong> to get a template with example data</li>
                        <li>• Import from CSV: Format should be Name, Birthday, Category, Email, Phone, Notes</li>
                        <li>• CSV supports flexible column names (e.g., "Birth Date" or "Birthday")</li>
                        <li>• Only Name and Birthday are required fields</li>
                        <li>• You can remove individual rows using the X button</li>
                        <li>• Empty rows (without names) will be ignored when submitting</li>
                        <li>• Date format: Use YYYY-MM-DD (e.g., 1990-01-15) for best results</li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default AddContactsPage;
