import React from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate, Link } from "react-router-dom";
import { useErrorNotification } from "../context/ErrorContext";
import { useFormValidation } from "../hooks/useFormValidation";
import <PERSON><PERSON><PERSON> from "./FormField";

interface LoginFormData {
    email: string;
    password: string;
}

const LoginForm: React.FC = () => {
    const { login, loginWithGoogle, isAuthLoading } = useAuth();
    const navigate = useNavigate();
    const { showError, showSuccess } = useErrorNotification();

    const {
        values,
        errors,
        touched,
        isValid,
        isSubmitting,
        setValue,
        setFieldTouched,
        handleSubmit,
    } = useFormValidation<LoginFormData>(
        { email: '', password: '' },
        {
            email: {
                required: true,
                email: true,
            },
            password: {
                required: true,
                minLength: 6,
            },
        }
    );

    const onSubmit = async (formData: LoginFormData) => {
        try {
            const success = await login(formData.email, formData.password);
            if (success) {
                showSuccess("Login successful!");
                navigate("/dashboard");
            } else {
                showError("Invalid email or password");
            }
        } catch (error) {
            showError("An error occurred during login");
            console.error("Login error:", error);
        }
    };

    const handleGoogleLogin = async () => {
        const success = await loginWithGoogle();
        if (success) {
            showSuccess("Google login successful!");
            navigate("/dashboard");
        } else {
            showError("Google login failed. Please try again.");
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="bg-white p-8 rounded-lg shadow-md">
                    <h2 className="text-2xl font-semibold mb-6 text-center">Log In</h2>
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            label="Email"
                            name="email"
                            type="email"
                            value={values.email}
                            onChange={(value) => setValue('email', value)}
                            onBlur={() => setFieldTouched('email')}
                            error={errors.email}
                            touched={touched.email}
                            required
                            autoFocus
                            placeholder="Enter your email"
                        />

                        <FormField
                            label="Password"
                            name="password"
                            type="password"
                            value={values.password}
                            onChange={(value) => setValue('password', value)}
                            onBlur={() => setFieldTouched('password')}
                            error={errors.password}
                            touched={touched.password}
                            required
                            minLength={6}
                            placeholder="Enter your password"
                        />

                        <button
                            type="submit"
                            disabled={isSubmitting || !isValid || isAuthLoading}
                            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting || isAuthLoading ? "Logging in..." : "Log In"}
                        </button>
                    </form>

                    <button
                        onClick={handleGoogleLogin}
                        disabled={isAuthLoading}
                        className="mt-4 w-full bg-red-600 text-white py-2 rounded hover:bg-red-700 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {/* Google G Icon */}
                        <svg
                            className="w-6 h-6"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 48 48"
                        >
                            <path
                                fill="#4285F4"
                                d="M24 9.5c3.54 0 6.74 1.22 9.22 3.23l6.89-6.9C34.78 2.54 29.68 0 24 0 14.8 0 6.74 5.69 2.67 13.9l7.99 6.22C12.88 14.06 18.9 9.5 24 9.5z"
                            />
                            <path
                                fill="#34A853"
                                d="M46.5 24c0-1.62-.14-3.18-.42-4.69H24v9.01h12.71c-.56 3.05-2.26 5.63-4.8 7.32l7.63 5.93c4.44-4.1 7-10.1 7-17.57z"
                            />
                            <path
                                fill="#FBBC05"
                                d="M10.66 28.1a14.39 14.39 0 0 1 0-8.2l-8-6.22A23.94 23.94 0 0 0 0 24c0 3.89 1 7.55 2.78 10.79l7.88-6.69z"
                            />
                            <path
                                fill="#EA4335"
                                d="M24 48c6.48 0 11.91-2.13 15.89-5.77l-7.63-5.93c-2.09 1.42-4.77 2.28-8.26 2.28-5.1 0-9.42-3.44-10.97-8.07l-7.99 6.22C6.74 42.31 14.8 48 24 48z"
                            />
                            <path fill="none" d="M0 0h48v48H0z" />
                        </svg>
                        Sign in with Google
                    </button>

                    {/* Navigation Links */}
                    <div className="mt-6 text-center space-y-2">
                        <div>
                            <Link
                                to="/forgot-password"
                                className="text-sm text-blue-600 hover:text-blue-800 underline"
                            >
                                Forgot your password?
                            </Link>
                        </div>
                        <div className="text-sm text-gray-600">
                            Don't have an account?{" "}
                            <Link
                                to="/signup"
                                className="text-blue-600 hover:text-blue-800 underline font-medium"
                            >
                                Sign up here
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoginForm;
