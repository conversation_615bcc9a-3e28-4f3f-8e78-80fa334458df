import React, { useState, useMemo } from 'react';
import { differenceInYears, format, addYears } from 'date-fns';
import { useContactsQuery } from '../hooks/useContacts';
import { useLandmarkBirthday } from '../hooks/useMessaging';
import { LANDMARK_BIRTHDAYS } from '../services/messagingService';
import type { Contact } from '../types';

interface LandmarkBirthdayInfo extends Contact {
  currentAge: number;
  nextAge: number;
  isCurrentLandmark: boolean;
  isUpcomingLandmark: boolean;
  currentLandmark?: any;
  upcomingLandmark?: any;
  daysUntilBirthday: number;
  nextBirthday: Date;
}

const LandmarkBirthdayDetector: React.FC = () => {
  const { data: contacts = [], isLoading } = useContactsQuery();
  const [filter, setFilter] = useState<'all' | 'current' | 'upcoming'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'age' | 'days'>('days');

  const landmarkContacts = useMemo(() => {
    const today = new Date();

    return contacts.map(contact => {
      const birthDate = new Date(contact.birthday);
      const currentAge = differenceInYears(today, birthDate);
      const nextAge = currentAge + 1;

      // Calculate next birthday
      const nextBirthday = new Date(today.getFullYear(), birthDate.getMonth(), birthDate.getDate());
      if (nextBirthday < today) {
        nextBirthday.setFullYear(today.getFullYear() + 1);
      }

      const daysUntilBirthday = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

      const { isLandmark: isCurrentLandmark, landmark: currentLandmark } = useLandmarkBirthday(currentAge);
      const { isLandmark: isUpcomingLandmark, landmark: upcomingLandmark } = useLandmarkBirthday(nextAge);

      return {
        ...contact,
        currentAge,
        nextAge,
        isCurrentLandmark,
        isUpcomingLandmark,
        currentLandmark,
        upcomingLandmark,
        daysUntilBirthday,
        nextBirthday,
      } as LandmarkBirthdayInfo;
    }).filter(contact => {
      switch (filter) {
        case 'current':
          return contact.isCurrentLandmark;
        case 'upcoming':
          return contact.isUpcomingLandmark;
        default:
          return contact.isCurrentLandmark || contact.isUpcomingLandmark;
      }
    }).sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'age':
          return b.currentAge - a.currentAge;
        case 'days':
        default:
          return a.daysUntilBirthday - b.daysUntilBirthday;
      }
    });
  }, [contacts, filter, sortBy]);

  const stats = useMemo(() => {
    const currentLandmarks = landmarkContacts.filter(c => c.isCurrentLandmark).length;
    const upcomingLandmarks = landmarkContacts.filter(c => c.isUpcomingLandmark).length;
    const thisMonthLandmarks = landmarkContacts.filter(c =>
      c.isUpcomingLandmark && c.daysUntilBirthday <= 30
    ).length;

    return {
      currentLandmarks,
      upcomingLandmarks,
      thisMonthLandmarks,
      totalLandmarks: currentLandmarks + upcomingLandmarks,
    };
  }, [landmarkContacts]);

  const getLandmarkIcon = (age: number) => {
    if (age >= 100) return '🎊';
    if (age >= 80) return '🏆';
    if (age >= 60) return '💎';
    if (age >= 50) return '🎯';
    if (age >= 40) return '🌟';
    if (age >= 30) return '🎈';
    if (age >= 21) return '🍾';
    if (age >= 18) return '🗝️';
    return '🎉';
  };

  const getLandmarkColor = (age: number, isSpecial: boolean) => {
    if (age >= 100) return 'bg-purple-100 text-purple-800 border-purple-200';
    if (age >= 80) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (age >= 60) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (isSpecial) return 'bg-pink-100 text-pink-800 border-pink-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  if (isLoading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Landmark Birthdays</h3>
          <p className="text-sm text-gray-600">Special milestone birthdays to celebrate</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded-full">
            {stats.totalLandmarks} Total
          </span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">🎂</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-600">Current Year</p>
              <p className="text-2xl font-bold text-blue-900">{stats.currentLandmarks}</p>
            </div>
          </div>
        </div>

        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">🎯</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-600">Upcoming</p>
              <p className="text-2xl font-bold text-green-900">{stats.upcomingLandmarks}</p>
            </div>
          </div>
        </div>

        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-2xl">📅</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-600">This Month</p>
              <p className="text-2xl font-bold text-yellow-900">{stats.thisMonthLandmarks}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Sorting */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${filter === 'all'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            All Landmarks
          </button>
          <button
            onClick={() => setFilter('current')}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${filter === 'current'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            Current Year
          </button>
          <button
            onClick={() => setFilter('upcoming')}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${filter === 'upcoming'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            Upcoming
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="days">Days Until Birthday</option>
            <option value="name">Name</option>
            <option value="age">Age</option>
          </select>
        </div>
      </div>

      {/* Landmark Contacts List */}
      <div className="space-y-4">
        {landmarkContacts.map(contact => (
          <div
            key={contact.id}
            className="p-4 border rounded-lg hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-semibold text-purple-600">
                    {contact.name.charAt(0).toUpperCase()}
                  </span>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900">{contact.name}</h4>
                  <p className="text-sm text-gray-600">
                    {format(new Date(contact.birthday), 'MMMM do')} •
                    {contact.daysUntilBirthday === 0 ? ' Today!' :
                      contact.daysUntilBirthday === 1 ? ' Tomorrow' :
                        ` ${contact.daysUntilBirthday} days`}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                {contact.isCurrentLandmark && contact.currentLandmark && (
                  <div className={`px-3 py-1 rounded-full border ${getLandmarkColor(contact.currentAge, contact.currentLandmark.isSpecial)}`}>
                    <div className="flex items-center space-x-2">
                      <span>{getLandmarkIcon(contact.currentAge)}</span>
                      <span className="text-sm font-medium">
                        {contact.currentAge} - {contact.currentLandmark.name}
                      </span>
                    </div>
                  </div>
                )}

                {contact.isUpcomingLandmark && contact.upcomingLandmark && (
                  <div className={`px-3 py-1 rounded-full border ${getLandmarkColor(contact.nextAge, contact.upcomingLandmark.isSpecial)}`}>
                    <div className="flex items-center space-x-2">
                      <span>{getLandmarkIcon(contact.nextAge)}</span>
                      <span className="text-sm font-medium">
                        {contact.nextAge} - {contact.upcomingLandmark.name}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {(contact.currentLandmark?.description || contact.upcomingLandmark?.description) && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {contact.isCurrentLandmark && contact.currentLandmark?.description}
                  {contact.isUpcomingLandmark && contact.upcomingLandmark?.description}
                </p>
              </div>
            )}
          </div>
        ))}

        {landmarkContacts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-4">🎂</div>
            <p className="text-lg font-medium mb-2">No landmark birthdays found</p>
            <p className="text-sm">
              {filter === 'all'
                ? 'No contacts have landmark birthdays this year or next.'
                : `No contacts match the "${filter}" filter.`
              }
            </p>
          </div>
        )}
      </div>

      {/* Landmark Ages Reference */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <h4 className="font-medium text-gray-900 mb-4">Landmark Birthday Ages</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          {LANDMARK_BIRTHDAYS.map(landmark => (
            <div
              key={landmark.age}
              className={`p-2 rounded-lg border text-center ${getLandmarkColor(landmark.age, landmark.isSpecial)}`}
            >
              <div className="text-lg">{getLandmarkIcon(landmark.age)}</div>
              <div className="text-sm font-medium">{landmark.age}</div>
              <div className="text-xs">{landmark.name}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LandmarkBirthdayDetector;
