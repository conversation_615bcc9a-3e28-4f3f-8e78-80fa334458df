import React from 'react';

interface FormFieldProps {
    label: string;
    name: string;
    type?: 'text' | 'email' | 'password' | 'date' | 'select';
    value: string;
    onChange: (value: string) => void;
    onBlur?: () => void;
    error?: string;
    touched?: boolean;
    required?: boolean;
    placeholder?: string;
    disabled?: boolean;
    autoFocus?: boolean;
    minLength?: number;
    maxLength?: number;
    options?: { value: string; label: string; }[]; // For select fields
    className?: string;
}

const FormField: React.FC<FormFieldProps> = ({
    label,
    name,
    type = 'text',
    value,
    onChange,
    onBlur,
    error,
    touched,
    required = false,
    placeholder,
    disabled = false,
    autoFocus = false,
    minLength,
    maxLength,
    options,
    className = '',
}) => {
    const hasError = touched && error;

    const baseInputClasses = `
        w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
        disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
        ${hasError
            ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
            : 'border-gray-300 bg-white text-gray-900 placeholder-gray-400'
        }
        ${className}
    `.trim().replace(/\s+/g, ' ');

    const renderInput = () => {
        if (type === 'select' && options) {
            return (
                <select
                    id={name}
                    name={name}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    onBlur={onBlur}
                    disabled={disabled}
                    className={baseInputClasses}
                    aria-invalid={hasError ? 'true' : 'false'}
                    aria-describedby={hasError ? `${name}-error` : undefined}
                >
                    <option value="" disabled>
                        {placeholder || `Select ${label.toLowerCase()}`}
                    </option>
                    {options.map((option) => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
            );
        }

        return (
            <input
                id={name}
                name={name}
                type={type}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onBlur={onBlur}
                disabled={disabled}
                autoFocus={autoFocus}
                placeholder={placeholder}
                minLength={minLength}
                maxLength={maxLength}
                className={baseInputClasses}
                aria-invalid={hasError ? 'true' : 'false'}
                aria-describedby={hasError ? `${name}-error` : undefined}
            />
        );
    };

    return (
        <div className="mb-4">
            <label
                htmlFor={name}
                className="block text-sm font-medium text-gray-700 mb-1"
            >
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>

            {renderInput()}

            {hasError && (
                <div
                    id={`${name}-error`}
                    className="mt-1 flex items-center text-sm text-red-600"
                    role="alert"
                >
                    <svg
                        className="w-4 h-4 mr-1 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                        />
                    </svg>
                    {error}
                </div>
            )}

            {/* Success indicator when field is valid and touched */}
            {touched && !error && value && (
                <div className="mt-1 flex items-center text-sm text-green-600">
                    <svg
                        className="w-4 h-4 mr-1 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                        />
                    </svg>
                    Valid
                </div>
            )}
        </div>
    );
};

export default FormField;
