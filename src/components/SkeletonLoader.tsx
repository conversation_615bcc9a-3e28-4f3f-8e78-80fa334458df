import React from 'react';

interface SkeletonLoaderProps {
    variant?: 'text' | 'rectangular' | 'circular' | 'card';
    width?: string | number;
    height?: string | number;
    lines?: number; // For text variant
    className?: string;
    animate?: boolean;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
    variant = 'text',
    width,
    height,
    lines = 1,
    className = '',
    animate = true,
}) => {
    const baseClasses = `bg-gray-200 ${animate ? 'animate-pulse' : ''} ${className}`;

    const getVariantClasses = () => {
        switch (variant) {
            case 'text':
                return 'rounded h-4';
            case 'rectangular':
                return 'rounded';
            case 'circular':
                return 'rounded-full';
            case 'card':
                return 'rounded-lg';
            default:
                return 'rounded';
        }
    };

    const getStyle = () => {
        const style: React.CSSProperties = {};
        if (width) style.width = typeof width === 'number' ? `${width}px` : width;
        if (height) style.height = typeof height === 'number' ? `${height}px` : height;
        return style;
    };

    if (variant === 'text' && lines > 1) {
        return (
            <div className="space-y-2">
                {Array.from({ length: lines }).map((_, index) => (
                    <div
                        key={index}
                        className={`${baseClasses} ${getVariantClasses()}`}
                        style={{
                            ...getStyle(),
                            width: index === lines - 1 ? '75%' : '100%', // Last line is shorter
                        }}
                    />
                ))}
            </div>
        );
    }

    return (
        <div
            className={`${baseClasses} ${getVariantClasses()}`}
            style={getStyle()}
        />
    );
};

// Predefined skeleton components for common use cases
export const ContactSkeleton: React.FC = () => (
    <div className="p-4 border rounded-lg space-y-3">
        <div className="flex items-center space-x-3">
            <SkeletonLoader variant="circular" width={40} height={40} />
            <div className="flex-1">
                <SkeletonLoader variant="text" width="60%" />
                <SkeletonLoader variant="text" width="40%" className="mt-1" />
            </div>
        </div>
    </div>
);

export const StatsSkeleton: React.FC = () => (
    <div className="max-w-md mx-auto p-4 border rounded mt-6 flex justify-between">
        <div className="text-center">
            <SkeletonLoader variant="text" width={60} height={32} className="mb-2" />
            <SkeletonLoader variant="text" width={80} />
        </div>
        <div className="text-center">
            <SkeletonLoader variant="text" width={60} height={32} className="mb-2" />
            <SkeletonLoader variant="text" width={120} />
        </div>
    </div>
);

export const CalendarSkeleton: React.FC = () => (
    <div className="max-w-md mx-auto p-4 border rounded mt-6">
        <SkeletonLoader variant="text" width={200} className="mb-4" />
        <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 35 }).map((_, index) => (
                <SkeletonLoader
                    key={index}
                    variant="rectangular"
                    width="100%"
                    height={80}
                />
            ))}
        </div>
    </div>
);

export const TodaysBirthdaysSkeleton: React.FC = () => (
    <div className="max-w-md mx-auto p-4 border rounded mt-6">
        <SkeletonLoader variant="text" width={150} className="mb-2" />
        <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
                <SkeletonLoader key={index} variant="text" width="80%" />
            ))}
        </div>
    </div>
);

export default SkeletonLoader;
