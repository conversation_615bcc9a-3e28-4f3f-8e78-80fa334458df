import { useState } from "react";
import { Link } from "react-router-dom";
import { useErrorNotification } from "../context/ErrorContext";
import <PERSON><PERSON><PERSON> from "./FormField";

const ForgotPassword = () => {
    const [email, setEmail] = useState("");
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState<{ email?: string }>({});
    const { showSuccess, showError } = useErrorNotification();

    const validateEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email) {
            return "Email is required";
        }
        if (!emailRegex.test(email)) {
            return "Please enter a valid email address";
        }
        return "";
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        const emailError = validateEmail(email);
        if (emailError) {
            setErrors({ email: emailError });
            return;
        }

        setErrors({});
        setIsLoading(true);

        try {
            // Simulate password reset process
            // In a real app, this would call your backend API
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Check if user exists in localStorage (for demo purposes)
            const users = JSON.parse(localStorage.getItem('birthdaySaaSUserDB') || '{}');
            
            if (users[email]) {
                // User exists, show success message
                setIsSubmitted(true);
                showSuccess("Password reset instructions sent to your email!");
                
                // In a real app, you would:
                // 1. Generate a secure reset token
                // 2. Store it with expiration time
                // 3. Send email with reset link
                // For demo, we'll just show success
                
            } else {
                // User doesn't exist, but don't reveal this for security
                // Always show success to prevent email enumeration attacks
                setIsSubmitted(true);
                showSuccess("If an account with that email exists, password reset instructions have been sent.");
            }
        } catch (error) {
            showError("Failed to send password reset email. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleEmailChange = (value: string) => {
        setEmail(value);
        if (errors.email) {
            const error = validateEmail(value);
            setErrors({ email: error });
        }
    };

    const handleEmailBlur = () => {
        const error = validateEmail(email);
        setErrors({ email: error });
    };

    if (isSubmitted) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="bg-white p-8 rounded-lg shadow-md text-center">
                        <div className="mb-6">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        </div>
                        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Check Your Email</h2>
                        <p className="text-gray-600 mb-6">
                            We've sent password reset instructions to <strong>{email}</strong>
                        </p>
                        <p className="text-sm text-gray-500 mb-6">
                            Didn't receive the email? Check your spam folder or try again.
                        </p>
                        <div className="space-y-3">
                            <button
                                onClick={() => {
                                    setIsSubmitted(false);
                                    setEmail("");
                                }}
                                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition"
                            >
                                Try Again
                            </button>
                            <Link
                                to="/login"
                                className="block w-full text-center text-blue-600 hover:text-blue-800 underline"
                            >
                                Back to Sign In
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="bg-white p-8 rounded-lg shadow-md">
                    <div className="text-center mb-6">
                        <h2 className="text-2xl font-semibold mb-2">Forgot Your Password?</h2>
                        <p className="text-gray-600 text-sm">
                            No worries! Enter your email address and we'll send you instructions to reset your password.
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <FormField
                            label="Email Address"
                            name="email"
                            type="email"
                            value={email}
                            onChange={handleEmailChange}
                            onBlur={handleEmailBlur}
                            error={errors.email}
                            touched={!!errors.email}
                            placeholder="Enter your email address"
                            autoFocus
                            required
                        />

                        <button
                            type="submit"
                            disabled={isLoading || !!errors.email || !email}
                            className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                            {isLoading ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Sending Instructions...
                                </>
                            ) : (
                                "Send Reset Instructions"
                            )}
                        </button>
                    </form>

                    {/* Navigation Links */}
                    <div className="mt-6 text-center space-y-2">
                        <div>
                            <Link
                                to="/login"
                                className="text-sm text-blue-600 hover:text-blue-800 underline"
                            >
                                Back to Sign In
                            </Link>
                        </div>
                        <div className="text-sm text-gray-600">
                            Don't have an account?{" "}
                            <Link
                                to="/signup"
                                className="text-blue-600 hover:text-blue-800 underline font-medium"
                            >
                                Sign up here
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
