import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { databaseService } from '../services/databaseService';
import { migrationService } from '../services/migrationService';
import { messagingService } from '../services/messagingService';
import { contactsService } from '../services/contactsService';

interface DatabaseInitializerProps {
  children: React.ReactNode;
}

const DatabaseInitializer: React.FC<DatabaseInitializerProps> = ({ children }) => {
  const { user } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Initializing SQLite database...');

        // Initialize SQLite database service
        await databaseService.initialize();
        console.log('✅ SQLite database initialized');

        // Run database migrations
        await migrationService.runMigrations();
        console.log('✅ Database migrations completed');

        // If user is logged in, set up user-specific services
        if (user?.email) {
          console.log(`🔄 Setting up services for user: ${user.email}`);

          // Set current user in services
          databaseService.setCurrentUser(user.email);
          messagingService.setCurrentUser(user.email);
          contactsService.setCurrentUser(user.email);

          console.log('✅ User-specific services initialized');
        }

        setIsInitialized(true);
        console.log('🎉 Database initialization complete');
      } catch (err) {
        console.error('❌ Database initialization failed:', err);
        setError(err instanceof Error ? err.message : 'Database initialization failed');
      } finally {
        setIsLoading(false);
      }
    };

    initializeDatabase();
  }, [user?.email]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mb-4 shadow-lg animate-pulse">
            <span className="text-3xl">🎂</span>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent mb-2">
            Setting up WeWish
          </h2>
          <p className="text-gray-600 mb-4">
            Initializing your birthday management system...
          </p>
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-pink-50 to-orange-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl mb-4 shadow-lg">
            <span className="text-3xl">⚠️</span>
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-2">
            Initialization Failed
          </h2>
          <p className="text-gray-600 mb-4">
            We encountered an error while setting up your database:
          </p>
          <div className="bg-red-100 border border-red-300 rounded-lg p-3 mb-4">
            <p className="text-red-700 text-sm font-mono">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-2 rounded-lg hover:from-red-600 hover:to-pink-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl mb-4 shadow-lg">
            <span className="text-3xl">⏳</span>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
            Almost Ready
          </h2>
          <p className="text-gray-600">
            Finalizing setup...
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default DatabaseInitializer;
