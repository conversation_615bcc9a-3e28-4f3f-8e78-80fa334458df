import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface PlanDetails {
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  cta: string;
}

const PLAN_DETAILS: Record<string, PlanDetails> = {
  Free: {
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    description: 'Perfect for getting started',
    features: [
      { name: 'Up to 10 contacts', included: true },
      { name: 'Basic reminders', included: true },
      { name: 'Email support', included: true },
      { name: 'Advanced analytics', included: false },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Current Plan',
  },
  Standard: {
    name: 'Standard',
    price: { monthly: 7.99, yearly: 79.99 },
    description: 'Great for personal use',
    features: [
      { name: 'Up to 100 contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'Email support', included: true },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Upgrade to Standard',
  },
  Elite: {
    name: 'Elite',
    price: { monthly: 15.99, yearly: 159.99 },
    description: 'Most popular for power users',
    features: [
      { name: 'Up to 500 contacts', included: true },
      { name: 'Premium reminders', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Priority support', included: true },
      { name: 'Gift suggestions', included: true },
      { name: 'Automated messaging', included: true },
    ],
    popular: true,
    cta: 'Upgrade to Elite',
  },
  Premium: {
    name: 'Premium',
    price: { monthly: 23.99, yearly: 239.99 },
    description: 'Everything you need and more',
    features: [
      { name: 'Unlimited contacts', included: true },
      { name: 'All features', included: true },
      { name: 'Advanced analytics', included: true },
      { name: '24/7 support', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Team collaboration', included: true },
    ],
    cta: 'Upgrade to Premium',
  },
};

const SubscriptionManagement: React.FC = () => {
  const { user } = useAuth();
  const { subscription, trialStatus, hasActiveSubscription, isTrialActive, trialDaysLeft } = useSubscriptionStatus();
  const { handleUpgrade, handleManageBilling, handleStartTrial, isUpgrading, isManagingBilling, isStartingTrial } = usePlanManagement();

  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [showTrialModal, setShowTrialModal] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Trial' : 'Free');
  const yearlyDiscount = 17; // 17% discount for yearly billing

  const handlePlanUpgrade = async (plan: 'Standard' | 'Elite' | 'Premium') => {
    try {
      setProcessingPlan(plan);
      await handleUpgrade(plan, billingCycle);
    } catch (error) {
      alert('Upgrade failed. Please try again.');
    } finally {
      setProcessingPlan(null);
    }
  };

  const handleTrialStart = async () => {
    try {
      await handleStartTrial();
      setShowTrialModal(false);
      alert('Trial started! You now have access to all Premium features for 14 days.');
    } catch (error) {
      alert('Failed to start trial. Please try again.');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(price);
  };

  const getYearlyPrice = (monthlyPrice: number) => {
    return monthlyPrice * 12 * (1 - yearlyDiscount / 100);
  };

  const getPlanStatus = (planName: string) => {
    if (planName === currentPlan) return 'current';
    if (planName === 'Free') return 'downgrade';
    return 'upgrade';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p className="text-lg text-gray-600 mb-6">
          Unlock the full potential of WeWish with our premium features
        </p>

        {/* Current Status */}
        {hasActiveSubscription && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 inline-block">
            <p className="text-green-800">
              <span className="font-semibold">Current Plan:</span> {currentPlan}
              {subscription?.current_period_end && (
                <span className="ml-2">
                  (Renews {new Date(subscription.current_period_end * 1000).toLocaleDateString()})
                </span>
              )}
            </p>
          </div>
        )}

        {isTrialActive && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 inline-block">
            <p className="text-blue-800">
              <span className="font-semibold">Trial Active:</span> {trialDaysLeft} days remaining
            </p>
          </div>
        )}

        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Monthly
          </span>
          <button
            onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${billingCycle === 'yearly' ? 'bg-purple-600' : 'bg-gray-200'
              }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
            />
          </button>
          <span className={`text-sm ${billingCycle === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Yearly
          </span>
          {billingCycle === 'yearly' && (
            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              Save {yearlyDiscount}%
            </span>
          )}
        </div>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {Object.entries(PLAN_DETAILS).map(([planKey, plan]) => {
          const status = getPlanStatus(planKey);
          const price = billingCycle === 'yearly' ? getYearlyPrice(plan.price.monthly) : plan.price.monthly;
          const isCurrentPlan = status === 'current';

          return (
            <div
              key={planKey}
              className={`relative rounded-lg border-2 p-6 ${plan.popular
                ? 'border-purple-500 shadow-lg'
                : isCurrentPlan
                  ? 'border-green-500'
                  : 'border-gray-200'
                }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-500 text-white px-3 py-1 text-sm rounded-full">
                    Most Popular
                  </span>
                </div>
              )}

              {isCurrentPlan && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-green-500 text-white px-3 py-1 text-sm rounded-full">
                    Current Plan
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                <div className="mb-4">
                  <span className="text-3xl font-bold text-gray-900">
                    {formatPrice(price)}
                  </span>
                  {planKey !== 'Free' && (
                    <span className="text-gray-600">
                      /{billingCycle === 'yearly' ? 'year' : 'month'}
                    </span>
                  )}
                </div>

                {billingCycle === 'yearly' && planKey !== 'Free' && (
                  <p className="text-sm text-green-600">
                    Save {formatPrice(plan.price.monthly * 12 - price)} per year
                  </p>
                )}
              </div>

              {/* Features */}
              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <span className={`mr-2 ${feature.included ? 'text-green-500' : 'text-gray-300'}`}>
                      {feature.included ? '✓' : '✗'}
                    </span>
                    <span className={feature.included ? 'text-gray-900' : 'text-gray-500'}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <button
                onClick={() => {
                  if (planKey === 'Free') return;
                  if (isCurrentPlan) {
                    handleManageBilling();
                  } else if (planKey === 'Standard' || planKey === 'Elite' || planKey === 'Premium') {
                    handlePlanUpgrade(planKey as 'Standard' | 'Elite' | 'Premium');
                  }
                }}
                disabled={processingPlan === planKey || (isCurrentPlan && isManagingBilling) || (planKey === 'Free')}
                className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${isCurrentPlan
                  ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  : plan.popular
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-900 text-white hover:bg-gray-800'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {processingPlan === planKey ? 'Processing...' :
                  (isCurrentPlan && isManagingBilling) ? 'Processing...' :
                    isCurrentPlan ? 'Manage Billing' : plan.cta}
              </button>
            </div>
          );
        })}
      </div>

      {/* Trial CTA */}
      {!hasActiveSubscription && !isTrialActive && currentPlan === 'Free' && (
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-center text-white mb-8">
          <h3 className="text-xl font-semibold mb-2">Try Premium Features Free for 14 Days</h3>
          <p className="mb-4">No credit card required. Cancel anytime.</p>
          <button
            onClick={() => setShowTrialModal(true)}
            className="bg-white text-purple-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            Start Free Trial
          </button>
        </div>
      )}

      {/* Trial Confirmation Modal */}
      {showTrialModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Start Your Free Trial</h3>
            <p className="text-gray-600 mb-6">
              You'll get access to all Premium features for 14 days. No credit card required.
              You can cancel anytime during the trial period.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleTrialStart}
                disabled={isStartingTrial}
                className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                {isStartingTrial ? 'Starting...' : 'Start Trial'}
              </button>
              <button
                onClick={() => setShowTrialModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* FAQ Section */}
      <div className="mt-12 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
        <div className="text-sm text-gray-600 space-y-2">
          <p><strong>Can I change plans anytime?</strong> Yes, you can upgrade or downgrade at any time.</p>
          <p><strong>What happens after my trial ends?</strong> You'll be moved to the Free plan unless you choose to upgrade.</p>
          <p><strong>Can I cancel anytime?</strong> Yes, you can cancel your subscription at any time with no penalties.</p>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionManagement;
