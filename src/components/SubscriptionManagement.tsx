import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface PlanDetails {
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  cta: string;
}

const PLAN_DETAILS: Record<string, PlanDetails> = {
  Free: {
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    description: 'Perfect for getting started',
    features: [
      { name: 'Up to 10 contacts', included: true },
      { name: 'Basic reminders', included: true },
      { name: 'Email support', included: true },
      { name: 'Advanced analytics', included: false },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Current Plan',
  },
  Standard: {
    name: 'Standard',
    price: { monthly: 7.99, yearly: 79.99 },
    description: 'Great for personal use',
    features: [
      { name: 'Up to 100 contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'Email support', included: true },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Upgrade to Standard',
  },
  Elite: {
    name: 'Elite',
    price: { monthly: 15.99, yearly: 159.99 },
    description: 'Most popular for power users',
    features: [
      { name: 'Up to 500 contacts', included: true },
      { name: 'Premium reminders', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Priority support', included: true },
      { name: 'Gift suggestions', included: true },
      { name: 'Automated messaging', included: true },
    ],
    popular: true,
    cta: 'Upgrade to Elite',
  },
  Premium: {
    name: 'Premium',
    price: { monthly: 23.99, yearly: 239.99 },
    description: 'Everything you need and more',
    features: [
      { name: 'Unlimited contacts', included: true },
      { name: 'All features', included: true },
      { name: 'Advanced analytics', included: true },
      { name: '24/7 support', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Team collaboration', included: true },
    ],
    cta: 'Upgrade to Premium',
  },
};

const SubscriptionManagement: React.FC = () => {
  const { user } = useAuth();
  const { subscription, trialStatus, hasActiveSubscription, isTrialActive, trialDaysLeft } = useSubscriptionStatus();
  const { handleUpgrade, handleManageBilling, handleStartTrial, isUpgrading, isManagingBilling, isStartingTrial } = usePlanManagement();

  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [showTrialModal, setShowTrialModal] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Trial' : 'Free');
  const yearlyDiscount = 17; // 17% discount for yearly billing

  const handlePlanUpgrade = async (plan: 'Standard' | 'Elite' | 'Premium') => {
    try {
      setProcessingPlan(plan);
      await handleUpgrade(plan, billingCycle);
    } catch (error) {
      alert('Upgrade failed. Please try again.');
    } finally {
      setProcessingPlan(null);
    }
  };

  const handleTrialStart = async () => {
    try {
      await handleStartTrial();
      setShowTrialModal(false);
      alert('Trial started! You now have access to all Premium features for 14 days.');
    } catch (error) {
      alert('Failed to start trial. Please try again.');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(price);
  };

  const getYearlyPrice = (monthlyPrice: number) => {
    return monthlyPrice * 12 * (1 - yearlyDiscount / 100);
  };

  const getPlanStatus = (planName: string) => {
    if (planName === currentPlan) return 'current';
    if (planName === 'Free') return 'downgrade';
    return 'upgrade';
  };

  const getPlanTier = (planKey: string): number => {
    const tiers = { Free: 0, Standard: 1, Elite: 2, Premium: 3 };
    return tiers[planKey as keyof typeof tiers] || 0;
  };

  const getUpgradeDowngradeLabel = (planKey: string, userPlan: string): string => {
    const currentTier = getPlanTier(userPlan);
    const targetTier = getPlanTier(planKey);

    if (targetTier > currentTier) {
      return `Upgrade to ${planKey}`;
    } else if (targetTier < currentTier) {
      return `Downgrade to ${planKey}`;
    }
    return `Switch to ${planKey}`;
  };

  // Get current plan details for hero section
  const getCurrentPlanDetails = () => {
    if (isTrialActive) {
      return {
        name: 'Premium Trial',
        description: 'You\'re experiencing all Premium features',
        features: PLAN_DETAILS.Premium.features,
        trialInfo: { daysLeft: trialDaysLeft, isActive: true }
      };
    }

    const planName = subscription?.plan?.nickname || 'Free';
    return PLAN_DETAILS[planName] || PLAN_DETAILS.Free;
  };

  const currentPlanDetails = getCurrentPlanDetails();

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Hero Section for Current Subscription */}
      {(hasActiveSubscription || isTrialActive) && (
        <div className="mb-12">
          <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full -translate-x-20 -translate-y-20"></div>
              <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-16 translate-y-16"></div>
              <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-white rounded-full"></div>
            </div>

            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-2xl">✨</span>
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold">
                        {isTrialActive ? 'Premium Trial Active' : `${currentPlanDetails.name} Plan`}
                      </h1>
                      <p className="text-purple-100 text-lg">
                        {isTrialActive
                          ? `${trialDaysLeft} days remaining in your trial`
                          : currentPlanDetails.description
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {subscription?.current_period_end && !isTrialActive && (
                  <div className="text-right">
                    <p className="text-purple-100 text-sm">Renews on</p>
                    <p className="text-white font-semibold">
                      {new Date(subscription.current_period_end * 1000).toLocaleDateString('en-GB', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                      })}
                    </p>
                  </div>
                )}
              </div>

              {/* Current Plan Benefits */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold">Contacts</h3>
                  </div>
                  <p className="text-purple-100">
                    {currentPlanDetails.features.find(f => f.name.includes('contact'))?.name || 'Manage your contacts'}
                  </p>
                </div>

                <div className="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold">Analytics</h3>
                  </div>
                  <p className="text-purple-100">
                    {currentPlanDetails.features.find(f => f.name.includes('analytics'))?.included
                      ? 'Advanced birthday insights'
                      : 'Basic analytics included'
                    }
                  </p>
                </div>

                <div className="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12l.01.01M12 12l.01.01M12 12l.01.01M12 12l.01.01" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold">Support</h3>
                  </div>
                  <p className="text-purple-100">
                    {currentPlanDetails.features.find(f => f.name.includes('support'))?.name || 'Email support included'}
                  </p>
                </div>
              </div>

              {/* Trial Warning */}
              {isTrialActive && trialDaysLeft <= 3 && (
                <div className="bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-yellow-300 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                      <p className="text-yellow-100 font-semibold">Trial ending soon!</p>
                      <p className="text-yellow-200 text-sm">
                        Your trial expires in {trialDaysLeft} day{trialDaysLeft !== 1 ? 's' : ''}. Upgrade now to keep all features.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Header for Plan Selection */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {hasActiveSubscription || isTrialActive ? 'Manage Your Subscription' : 'Choose Your Plan'}
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          {hasActiveSubscription || isTrialActive
            ? 'Upgrade or downgrade your plan anytime'
            : 'Unlock the full potential of WeWish with our premium features'
          }
        </p>

        {isTrialActive && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 inline-block">
            <p className="text-blue-800">
              <span className="font-semibold">Trial Active:</span> {trialDaysLeft} days remaining
            </p>
          </div>
        )}

        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Monthly
          </span>
          <button
            onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${billingCycle === 'yearly' ? 'bg-purple-600' : 'bg-gray-200'
              }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
            />
          </button>
          <span className={`text-sm ${billingCycle === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Yearly
          </span>
          {billingCycle === 'yearly' && (
            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              Save {yearlyDiscount}%
            </span>
          )}
        </div>
      </div>

      {/* Upgrade/Downgrade CTAs */}
      {(hasActiveSubscription || isTrialActive) && (
        <div className="mb-8">
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Change Your Plan</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Manage Billing */}
              <button
                onClick={handleManageBilling}
                disabled={isManagingBilling}
                className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg className="w-6 h-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900">Manage Billing</h3>
                  <p className="text-sm text-gray-600">Update payment method & billing</p>
                </div>
              </button>

              {/* Cancel Subscription */}
              <button
                onClick={handleManageBilling}
                className="flex items-center justify-center p-4 border-2 border-red-200 rounded-lg hover:border-red-300 transition-colors"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg className="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900">Cancel Plan</h3>
                  <p className="text-sm text-gray-600">Downgrade or cancel subscription</p>
                </div>
              </button>

              {/* Contact Support */}
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center p-4 border-2 border-blue-200 rounded-lg hover:border-blue-300 transition-colors"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-900">Get Help</h3>
                  <p className="text-sm text-gray-600">Contact our support team</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Plans Grid - Exclude Current Plan */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {Object.entries(PLAN_DETAILS)
          .filter(([planKey]) => {
            // Exclude current plan from the grid
            if (isTrialActive) return planKey !== 'Premium'; // Trial users see all except Premium
            const userPlan = subscription?.plan?.nickname || 'Free';
            return planKey !== userPlan;
          })
          .map(([planKey, plan]) => {
            const status = getPlanStatus(planKey);
            const price = billingCycle === 'yearly' ? getYearlyPrice(plan.price.monthly) : plan.price.monthly;
            const isCurrentPlan = status === 'current';
            const userPlan = subscription?.plan?.nickname || 'Free';
            const isUpgrade = getPlanTier(planKey) > getPlanTier(userPlan);
            const isDowngrade = getPlanTier(planKey) < getPlanTier(userPlan);

            return (
              <div
                key={planKey}
                className={`relative rounded-lg border-2 p-6 ${plan.popular
                  ? 'border-purple-500 shadow-lg'
                  : isCurrentPlan
                    ? 'border-green-500'
                    : 'border-gray-200'
                  }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-500 text-white px-3 py-1 text-sm rounded-full">
                      Most Popular
                    </span>
                  </div>
                )}

                {isCurrentPlan && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-500 text-white px-3 py-1 text-sm rounded-full">
                      Current Plan
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(price)}
                    </span>
                    {planKey !== 'Free' && (
                      <span className="text-gray-600">
                        /{billingCycle === 'yearly' ? 'year' : 'month'}
                      </span>
                    )}
                  </div>

                  {billingCycle === 'yearly' && planKey !== 'Free' && (
                    <p className="text-sm text-green-600">
                      Save {formatPrice(plan.price.monthly * 12 - price)} per year
                    </p>
                  )}
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <span className={`mr-2 ${feature.included ? 'text-green-500' : 'text-gray-300'}`}>
                        {feature.included ? '✓' : '✗'}
                      </span>
                      <span className={feature.included ? 'text-gray-900' : 'text-gray-500'}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <button
                  onClick={() => {
                    if (planKey === 'Free') return;
                    if (isCurrentPlan) {
                      handleManageBilling();
                    } else if (planKey === 'Standard' || planKey === 'Elite' || planKey === 'Premium') {
                      handlePlanUpgrade(planKey as 'Standard' | 'Elite' | 'Premium');
                    }
                  }}
                  disabled={processingPlan === planKey || (isCurrentPlan && isManagingBilling) || (planKey === 'Free')}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${isCurrentPlan
                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      : isUpgrade
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg'
                        : isDowngrade
                          ? 'bg-gray-600 text-white hover:bg-gray-700'
                          : plan.popular
                            ? 'bg-purple-600 text-white hover:bg-purple-700'
                            : 'bg-gray-900 text-white hover:bg-gray-800'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {processingPlan === planKey ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : (isCurrentPlan && isManagingBilling) ? (
                    'Processing...'
                  ) : isCurrentPlan ? (
                    'Manage Billing'
                  ) : (
                    <div className="flex items-center justify-center">
                      {isUpgrade && (
                        <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                        </svg>
                      )}
                      {isDowngrade && (
                        <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
                        </svg>
                      )}
                      {getUpgradeDowngradeLabel(planKey, userPlan)}
                    </div>
                  )}
                </button>
              </div>
            );
          })}
      </div>

      {/* Trial CTA */}
      {!hasActiveSubscription && !isTrialActive && currentPlan === 'Free' && (
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-center text-white mb-8">
          <h3 className="text-xl font-semibold mb-2">Try Premium Features Free for 14 Days</h3>
          <p className="mb-4">No credit card required. Cancel anytime.</p>
          <button
            onClick={() => setShowTrialModal(true)}
            className="bg-white text-purple-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            Start Free Trial
          </button>
        </div>
      )}

      {/* Trial Confirmation Modal */}
      {showTrialModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Start Your Free Trial</h3>
            <p className="text-gray-600 mb-6">
              You'll get access to all Premium features for 14 days. No credit card required.
              You can cancel anytime during the trial period.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleTrialStart}
                disabled={isStartingTrial}
                className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                {isStartingTrial ? 'Starting...' : 'Start Trial'}
              </button>
              <button
                onClick={() => setShowTrialModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* FAQ Section */}
      <div className="mt-12 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
        <div className="text-sm text-gray-600 space-y-2">
          <p><strong>Can I change plans anytime?</strong> Yes, you can upgrade or downgrade at any time.</p>
          <p><strong>What happens after my trial ends?</strong> You'll be moved to the Free plan unless you choose to upgrade.</p>
          <p><strong>Can I cancel anytime?</strong> Yes, you can cancel your subscription at any time with no penalties.</p>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionManagement;
