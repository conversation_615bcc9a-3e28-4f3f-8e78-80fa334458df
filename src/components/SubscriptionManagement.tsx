import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSubscriptionStatus, usePlanManagement } from '../hooks/useStripe';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface PlanDetails {
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  cta: string;
}

const PLAN_DETAILS: Record<string, PlanDetails> = {
  Free: {
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    description: 'Perfect for getting started',
    features: [
      { name: 'Up to 10 contacts', included: true },
      { name: 'Basic reminders', included: true },
      { name: 'Email support', included: true },
      { name: 'Advanced analytics', included: false },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Current Plan',
  },
  Standard: {
    name: 'Standard',
    price: { monthly: 7.99, yearly: 79.99 },
    description: 'Great for personal use',
    features: [
      { name: 'Up to 100 contacts', included: true },
      { name: 'Advanced reminders', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'Email support', included: true },
      { name: 'Automated messaging', included: false },
      { name: 'Priority support', included: false },
    ],
    cta: 'Upgrade to Standard',
  },
  Elite: {
    name: 'Elite',
    price: { monthly: 15.99, yearly: 159.99 },
    description: 'Most popular for power users',
    features: [
      { name: 'Up to 500 contacts', included: true },
      { name: 'Premium reminders', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Priority support', included: true },
      { name: 'Gift suggestions', included: true },
      { name: 'Automated messaging', included: true },
    ],
    popular: true,
    cta: 'Upgrade to Elite',
  },
  Premium: {
    name: 'Premium',
    price: { monthly: 23.99, yearly: 239.99 },
    description: 'Everything you need and more',
    features: [
      { name: 'Unlimited contacts', included: true },
      { name: 'All features', included: true },
      { name: 'Advanced analytics', included: true },
      { name: '24/7 support', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Team collaboration', included: true },
    ],
    cta: 'Upgrade to Premium',
  },
};

const SubscriptionManagement: React.FC = () => {
  const { user } = useAuth();
  const { subscription, trialStatus, hasActiveSubscription, isTrialActive, trialDaysLeft } = useSubscriptionStatus();
  const { handleUpgrade, handleManageBilling, handleStartTrial, isUpgrading, isManagingBilling, isStartingTrial } = usePlanManagement();

  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [showTrialModal, setShowTrialModal] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const currentPlan = subscription?.plan?.nickname || (isTrialActive ? 'Trial' : 'Free');
  const yearlyDiscount = 17; // 17% discount for yearly billing

  const handlePlanUpgrade = async (plan: 'Standard' | 'Elite' | 'Premium') => {
    try {
      setProcessingPlan(plan);
      await handleUpgrade(plan, billingCycle);
    } catch (error) {
      alert('Upgrade failed. Please try again.');
    } finally {
      setProcessingPlan(null);
    }
  };

  const handleTrialStart = async () => {
    try {
      await handleStartTrial();
      setShowTrialModal(false);
      alert('Trial started! You now have access to all Premium features for 14 days.');
    } catch (error) {
      alert('Failed to start trial. Please try again.');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(price);
  };

  const getYearlyPrice = (monthlyPrice: number) => {
    return monthlyPrice * 12 * (1 - yearlyDiscount / 100);
  };

  const getPlanTier = (planKey: string): number => {
    const tiers = { Free: 0, Standard: 1, Elite: 2, Premium: 3 };
    return tiers[planKey as keyof typeof tiers] || 0;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation for non-logged-in users */}
      {!user && (
        <nav className="bg-white shadow-sm">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="text-xl font-bold text-blue-600">WeWish</div>
            <div className="flex space-x-4">
              <a href="/" className="text-gray-600 hover:text-blue-600 transition">
                Home
              </a>
              <a href="/login" className="text-gray-600 hover:text-blue-600 transition">
                Sign In
              </a>
              <a href="/signup" className="bg-yellow-400 text-blue-900 px-4 py-2 rounded font-semibold hover:bg-yellow-300 transition">
                Start Free Trial
              </a>
            </div>
          </div>
        </nav>
      )}

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          {user ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button className="mr-4 text-gray-600 hover:text-gray-900">
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h1 className="text-2xl font-semibold text-gray-900">Subscription</h1>
              </div>
              <button
                onClick={handleManageBilling}
                disabled={isManagingBilling}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Manage & Invoices
              </button>
            </div>
          ) : (
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
              <p className="text-xl text-gray-600 mb-8">
                Start with our free plan and upgrade as you grow. All plans include our core features.
              </p>
            </div>
          )}
        </div>

        {/* Current Plan Status */}
        {(hasActiveSubscription || isTrialActive) && (
          <div className="grid grid-cols-3 gap-8 mb-8 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {isTrialActive ? 'Trial' : currentPlan}
              </div>
              <div className="text-sm text-gray-600">Current Plan</div>
            </div>

            <div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {/* Usage tracking would go here */}
                {Math.floor(Math.random() * 800) + 200} / 1000
              </div>
              <div className="text-sm text-gray-600">Remaining / Total Credits</div>
            </div>

            <div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {subscription?.current_period_end
                  ? new Date(subscription.current_period_end * 1000).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  })
                  : isTrialActive
                    ? `${trialDaysLeft} days left`
                    : 'N/A'
                }
              </div>
              <div className="text-sm text-gray-600">
                {isTrialActive ? 'Trial Ends' : 'Next Billing Date'}
              </div>
            </div>
          </div>
        )}

        {isTrialActive && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 inline-block">
            <p className="text-blue-800">
              <span className="font-semibold">Trial Active:</span> {trialDaysLeft} days remaining
            </p>
          </div>
        )}

        {/* Billing Toggle */}
        <div className="flex items-center justify-center mb-8">
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              Yearly
              {billingCycle === 'yearly' && (
                <span className="absolute -top-1 -right-1 bg-pink-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                  Save
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {Object.entries(PLAN_DETAILS)
            .filter(([planKey]) => {
              // For non-logged-in users, show all plans
              if (!user) return true;

              // For logged-in users, show all plans except current one
              if (hasActiveSubscription || isTrialActive) {
                const userPlan = subscription?.plan?.nickname || (isTrialActive ? 'Premium' : 'Free');
                return planKey !== userPlan;
              }
              // Show all plans for free users
              return true;
            })
            .map(([planKey, plan]) => {
              const price = billingCycle === 'yearly' ? getYearlyPrice(plan.price.monthly) : plan.price.monthly;
              const userPlan = subscription?.plan?.nickname || 'Free';
              const isUpgrade = getPlanTier(planKey) > getPlanTier(userPlan);
              const isDowngrade = getPlanTier(planKey) < getPlanTier(userPlan);

              return (
                <div
                  key={planKey}
                  className={`relative bg-white rounded-lg border p-6 ${plan.popular ? 'border-blue-500 shadow-lg' : 'border-gray-200'
                    }`}
                >
                  {/* Plan Header */}
                  <div className="text-left mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                      {plan.popular && (
                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                          Popular
                        </span>
                      )}
                    </div>

                    {/* Price */}
                    <div className="mb-4">
                      <span className="text-3xl font-bold text-gray-900">
                        {formatPrice(price)}
                      </span>
                      <span className="text-gray-600 text-base">
                        {planKey !== 'Free' ? ` / ${billingCycle === 'yearly' ? 'Year' : 'Month'}` : ''}
                      </span>
                    </div>

                    {/* Credits/Usage Info */}
                    <div className="text-sm text-gray-600 mb-4">
                      {planKey === 'Free' && 'Including 100 Credits'}
                      {planKey === 'Standard' && 'Including 5,000 Credits'}
                      {planKey === 'Elite' && 'Including 11,000 Credits'}
                      {planKey === 'Premium' && 'Including Unlimited Credits'}
                    </div>
                  </div>

                  {/* Features */}
                  <ul className="space-y-2 mb-6 text-sm">
                    {plan.features.slice(0, 5).map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">{feature.name}</span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <button
                    onClick={() => {
                      // For non-logged-in users, redirect to signup
                      if (!user) {
                        if (planKey === 'Free') {
                          window.location.href = '/signup';
                        } else {
                          window.location.href = '/signup';
                        }
                        return;
                      }

                      // For logged-in users, handle subscription management
                      if (planKey === 'Free') {
                        if (hasActiveSubscription) {
                          handleManageBilling(); // Downgrade through billing portal
                        }
                        return;
                      }
                      if (planKey === 'Standard' || planKey === 'Elite' || planKey === 'Premium') {
                        handlePlanUpgrade(planKey as 'Standard' | 'Elite' | 'Premium');
                      }
                    }}
                    disabled={processingPlan === planKey}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${plan.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : planKey === 'Free'
                        ? 'bg-gray-600 text-white hover:bg-gray-700'
                        : 'bg-gray-900 text-white hover:bg-gray-800'
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {processingPlan === planKey ? (
                      <div className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </div>
                    ) : !user ? (
                      planKey === 'Free' ? 'Get Started Free' : 'Start Free Trial'
                    ) : planKey === 'Free' && hasActiveSubscription ? (
                      'Downgrade'
                    ) : planKey === 'Free' ? (
                      'Current Plan'
                    ) : (
                      'Upgrade'
                    )}
                  </button>
                </div>
              );
            })}
        </div>

        {/* Trial CTA */}
        {user && !hasActiveSubscription && !isTrialActive && currentPlan === 'Free' && (
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-center text-white mb-8">
            <h3 className="text-xl font-semibold mb-2">Try Premium Features Free for 14 Days</h3>
            <p className="mb-4">No credit card required. Cancel anytime.</p>
            <button
              onClick={() => setShowTrialModal(true)}
              className="bg-white text-purple-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              Start Free Trial
            </button>
          </div>
        )}

        {/* FAQ Section for non-logged-in users */}
        {!user && (
          <div className="bg-white rounded-lg p-8 mb-8">
            <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">Frequently Asked Questions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h4>
                <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">What happens after my trial ends?</h4>
                <p className="text-gray-600">You'll be moved to the Free plan unless you choose to upgrade to a paid plan.</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I cancel anytime?</h4>
                <p className="text-gray-600">Yes, you can cancel your subscription at any time with no penalties or cancellation fees.</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Do you offer refunds?</h4>
                <p className="text-gray-600">We offer a 30-day money-back guarantee for all paid plans. Contact support for assistance.</p>
              </div>
            </div>
          </div>
        )}

        {/* Trial Confirmation Modal */}
        {showTrialModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Start Your Free Trial</h3>
              <p className="text-gray-600 mb-6">
                You'll get access to all Premium features for 14 days. No credit card required.
                You can cancel anytime during the trial period.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={handleTrialStart}
                  disabled={isStartingTrial}
                  className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  {isStartingTrial ? 'Starting...' : 'Start Trial'}
                </button>
                <button
                  onClick={() => setShowTrialModal(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionManagement;
