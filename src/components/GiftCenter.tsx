import { useState, useEffect } from 'react';
import { useGiftSuggestionsQuery, useGiftHistoryQuery, useAddGiftToHistory } from '../hooks/useGifts';
import type { GiftSuggestion } from '../services/giftService';

interface Gift extends GiftSuggestion {
    image?: string;
    saved?: boolean;
}

const categories = [
    'All', 'Electronics', 'Books', 'Jewelry', 'Food & Beverage',
    'Clothing', 'Home & Garden', 'Stationery'
];

// Gift data is now fetched via React Query from giftService

const GiftCenter = () => {
    // React Query hooks for data fetching
    const { data: giftSuggestions = [], isLoading: suggestionsLoading, error: suggestionsError } = useGiftSuggestionsQuery();
    const { data: giftHistory = [], isLoading: historyLoading, error: historyError } = useGiftHistoryQuery();
    const addGiftToHistoryMutation = useAddGiftToHistory();

    // Local state for UI
    const [activeTab, setActiveTab] = useState<'suggestions' | 'history'>('suggestions');
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [searchQuery, setSearchQuery] = useState('');

    // Convert gift suggestions to local Gift format with saved state
    const [savedGifts, setSavedGifts] = useState<Set<string>>(new Set());
    const gifts: Gift[] = giftSuggestions.map(suggestion => ({
        ...suggestion,
        image: '/api/placeholder/300/200', // Default placeholder image
        saved: savedGifts.has(suggestion.id)
    }));

    // Modal state for Add to History
    const [showAddHistoryModal, setShowAddHistoryModal] = useState(false);
    const [historyFormData, setHistoryFormData] = useState({
        giftName: '',
        recipient: '',
        amount: '',
        date: new Date().toISOString().split('T')[0], // Today's date
        category: 'Electronics',
        notes: '',
        occasion: ''
    });

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(9); // 3x3 grid looks good

    const filteredGifts = gifts.filter(gift => {
        const matchesCategory = selectedCategory === 'All' || gift.category === selectedCategory;
        const matchesSearch = searchQuery === '' ||
            gift.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            gift.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
            gift.category.toLowerCase().includes(searchQuery.toLowerCase());
        return matchesCategory && matchesSearch;
    });

    const filteredHistory = giftHistory.filter(item => {
        if (searchQuery === '') return true;
        return item.giftName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.recipient.toLowerCase().includes(searchQuery.toLowerCase());
    });

    // Pagination calculations
    const totalPages = Math.ceil(
        activeTab === 'suggestions'
            ? filteredGifts.length / itemsPerPage
            : filteredHistory.length / itemsPerPage
    );

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    const paginatedGifts = filteredGifts.slice(startIndex, endIndex);
    const paginatedHistory = filteredHistory.slice(startIndex, endIndex);

    // Reset to first page when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [selectedCategory, searchQuery, activeTab, itemsPerPage]);

    const toggleSave = (giftId: string) => {
        setSavedGifts(prev => {
            const newSet = new Set(prev);
            if (newSet.has(giftId)) {
                newSet.delete(giftId);
            } else {
                newSet.add(giftId);
            }
            return newSet;
        });
    };

    const handleBuy = async (gift: Gift) => {
        try {
            // In a real app, this would integrate with a payment system
            await addGiftToHistoryMutation.mutateAsync({
                giftName: gift.name,
                recipient: 'Selected Contact', // This would come from contact selection
                recipientId: 'temp-id', // This would come from contact selection
                date: new Date().toISOString().split('T')[0],
                amount: gift.price,
                category: gift.category,
                notes: `Purchased from gift suggestions`
            });
            alert(`Purchase initiated for ${gift.name}!`);
        } catch (error) {
            console.error('Error adding gift to history:', error);
            alert('Failed to add gift to history. Please try again.');
        }
    };

    const addToHistory = () => {
        setShowAddHistoryModal(true);
    };

    const handleHistoryFormSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (historyFormData.giftName && historyFormData.recipient && historyFormData.amount) {
            try {
                await addGiftToHistoryMutation.mutateAsync({
                    giftName: historyFormData.giftName,
                    recipient: historyFormData.recipient,
                    recipientId: 'manual-entry', // For manually added gifts
                    date: historyFormData.date,
                    amount: parseFloat(historyFormData.amount),
                    category: historyFormData.category,
                    notes: historyFormData.notes ? `${historyFormData.occasion ? `Occasion: ${historyFormData.occasion}. ` : ''}${historyFormData.notes}` : historyFormData.occasion || undefined
                });

                setShowAddHistoryModal(false);

                // Reset form
                setHistoryFormData({
                    giftName: '',
                    recipient: '',
                    amount: '',
                    date: new Date().toISOString().split('T')[0],
                    category: 'Electronics',
                    notes: '',
                    occasion: ''
                });
            } catch (error) {
                console.error('Error adding gift to history:', error);
                alert('Failed to add gift to history. Please try again.');
            }
        }
    };

    const handleFormInputChange = (field: string, value: string) => {
        setHistoryFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl mb-4 shadow-lg">
                        <span className="text-3xl">🎁</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent mb-3">
                        Gift Center
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
                        Discover perfect gifts and track your giving history
                    </p>
                    {activeTab === 'history' && (
                        <button
                            onClick={addToHistory}
                            className="bg-gradient-to-r from-amber-600 to-orange-600 text-white px-6 py-3 rounded-2xl hover:from-amber-700 hover:to-orange-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold flex items-center mx-auto"
                        >
                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                            </svg>
                            Add to History
                        </button>
                    )}
                </div>

                {/* Tabs */}
                <div className="flex justify-center mb-8">
                    <div className="flex space-x-2 bg-white/80 backdrop-blur-sm p-2 rounded-2xl shadow-lg border border-white/20">
                        <button
                            onClick={() => {
                                setActiveTab('suggestions');
                                setSearchQuery('');
                            }}
                            className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 flex items-center ${activeTab === 'suggestions'
                                ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                }`}
                        >
                            <span className="text-lg mr-2">🎁</span>
                            Gift Suggestions
                        </button>
                        <button
                            onClick={() => {
                                setActiveTab('history');
                                setSearchQuery('');
                            }}
                            className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 flex items-center ${activeTab === 'history'
                                ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                }`}
                        >
                            <span className="text-lg mr-2">📋</span>
                            Gift History
                        </button>
                    </div>
                </div>

                {/* Loading States */}
                {(suggestionsLoading && activeTab === 'suggestions') && (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading gift suggestions...</p>
                        </div>
                    </div>
                )}

                {(historyLoading && activeTab === 'history') && (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading gift history...</p>
                        </div>
                    </div>
                )}

                {/* Error States */}
                {(suggestionsError && activeTab === 'suggestions') && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
                        <div className="text-red-600 mb-2">
                            <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Gift Suggestions</h3>
                        <p className="text-red-600 mb-4">There was an error loading gift suggestions. Please try again.</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                )}

                {(historyError && activeTab === 'history') && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
                        <div className="text-red-600 mb-2">
                            <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Gift History</h3>
                        <p className="text-red-600 mb-4">There was an error loading your gift history. Please try again.</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                )}

                {/* Content - only show when not loading and no errors */}
                {!suggestionsLoading && !suggestionsError && activeTab === 'suggestions' && (
                    <>
                        {/* Search and Filter Section */}
                        <div className="space-y-4 mb-6">
                            {/* Search Input */}
                            <div className="relative max-w-md">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    placeholder="Search gifts..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                                />
                                {searchQuery && (
                                    <button
                                        onClick={() => setSearchQuery('')}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    >
                                        <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                )}
                            </div>

                            {/* Category Filter */}
                            <div className="flex flex-wrap gap-2">
                                <span className="text-sm font-medium text-gray-700 flex items-center mr-3">
                                    Categories:
                                </span>
                                {categories.map(category => (
                                    <button
                                        key={category}
                                        onClick={() => setSelectedCategory(category)}
                                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === category
                                            ? 'bg-purple-600 text-white'
                                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                                            }`}
                                    >
                                        {category}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Gift Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {paginatedGifts.map(gift => (
                                <div key={gift.id} className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
                                    <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                        <div className="text-6xl">
                                            {gift.category === 'Electronics' && '🎧'}
                                            {gift.category === 'Food & Beverage' && '☕'}
                                            {gift.category === 'Stationery' && '📔'}
                                            {gift.category === 'Clothing' && '👗'}
                                            {gift.category === 'Books' && '📚'}
                                            {gift.category === 'Jewelry' && '💎'}
                                            {gift.category === 'Home & Garden' && '🏡'}
                                        </div>
                                    </div>
                                    <div className="p-6">
                                        <div className="flex justify-between items-start mb-2">
                                            <h3 className="font-semibold text-gray-900 text-lg">{gift.name}</h3>
                                            <span className={`px-2 py-1 rounded text-xs font-medium ${gift.category === 'Electronics' ? 'bg-purple-100 text-purple-800' :
                                                gift.category === 'Food & Beverage' ? 'bg-green-100 text-green-800' :
                                                    'bg-blue-100 text-blue-800'
                                                }`}>
                                                {gift.category}
                                            </span>
                                        </div>
                                        <p className="text-gray-600 text-sm mb-4">{gift.description}</p>
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="flex items-center">
                                                <div className="flex text-yellow-400">
                                                    {[...Array(5)].map((_, i) => (
                                                        <svg key={i} className={`w-4 h-4 ${i < Math.floor(gift.rating) ? 'fill-current' : 'text-gray-300'}`} viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                        </svg>
                                                    ))}
                                                </div>
                                                <span className="ml-2 text-sm text-gray-600">{gift.rating}</span>
                                            </div>
                                            <span className="text-2xl font-bold text-green-600">${gift.price}</span>
                                        </div>
                                        <div className="flex space-x-2">
                                            <button
                                                onClick={() => toggleSave(gift.id)}
                                                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${gift.saved
                                                    ? 'bg-gray-200 text-gray-800'
                                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                                    }`}
                                            >
                                                {gift.saved ? 'Saved' : 'Save'}
                                            </button>
                                            <button
                                                onClick={() => handleBuy(gift)}
                                                className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors flex items-center justify-center"
                                            >
                                                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                                                </svg>
                                                Buy
                                                <svg className="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Pagination Controls for Gift Suggestions */}
                        {filteredGifts.length > 0 && (
                            <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4 bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                {/* Results Info */}
                                <div className="text-sm text-gray-600">
                                    Showing {startIndex + 1} to {Math.min(endIndex, filteredGifts.length)} of {filteredGifts.length} gifts
                                </div>

                                {/* Page Size Selector */}
                                <div className="flex items-center space-x-2">
                                    <label className="text-sm text-gray-600">Show:</label>
                                    <select
                                        value={itemsPerPage}
                                        onChange={(e) => setItemsPerPage(Number(e.target.value))}
                                        className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                                    >
                                        <option value={6}>6</option>
                                        <option value={9}>9</option>
                                        <option value={12}>12</option>
                                        <option value={18}>18</option>
                                    </select>
                                    <span className="text-sm text-gray-600">per page</span>
                                </div>

                                {/* Pagination Buttons */}
                                {totalPages > 1 && (
                                    <div className="flex items-center space-x-2">
                                        {/* Previous Button */}
                                        <button
                                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                            disabled={currentPage === 1}
                                            className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === 1
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 shadow-lg'
                                                }`}
                                        >
                                            Previous
                                        </button>

                                        {/* Page Numbers */}
                                        <div className="flex items-center space-x-1">
                                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                                let pageNum;
                                                if (totalPages <= 5) {
                                                    pageNum = i + 1;
                                                } else if (currentPage <= 3) {
                                                    pageNum = i + 1;
                                                } else if (currentPage >= totalPages - 2) {
                                                    pageNum = totalPages - 4 + i;
                                                } else {
                                                    pageNum = currentPage - 2 + i;
                                                }

                                                return (
                                                    <button
                                                        key={pageNum}
                                                        onClick={() => setCurrentPage(pageNum)}
                                                        className={`w-10 h-10 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === pageNum
                                                            ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg transform scale-105'
                                                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200 hover:border-amber-300'
                                                            }`}
                                                    >
                                                        {pageNum}
                                                    </button>
                                                );
                                            })}
                                        </div>

                                        {/* Next Button */}
                                        <button
                                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                            disabled={currentPage === totalPages}
                                            className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === totalPages
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 shadow-lg'
                                                }`}
                                        >
                                            Next
                                        </button>
                                    </div>
                                )}
                            </div>
                        )}
                    </>
                )}

                {!historyLoading && !historyError && activeTab === 'history' && (
                    <div className="bg-white rounded-lg shadow-sm border">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-lg font-semibold text-gray-900">Gift Purchase History</h2>
                                {/* Search Input for History */}
                                <div className="relative max-w-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <input
                                        type="text"
                                        placeholder="Search history..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="block w-full pl-9 pr-3 py-1.5 text-sm border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                                    />
                                    {searchQuery && (
                                        <button
                                            onClick={() => setSearchQuery('')}
                                            className="absolute inset-y-0 right-0 pr-2 flex items-center"
                                        >
                                            <svg className="w-3 h-3 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                            </svg>
                                        </button>
                                    )}
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {paginatedHistory.map(item => (
                                    <div key={item.id} className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                        {/* Header */}
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="flex-1">
                                                <h4 className="font-bold text-gray-900 text-lg mb-1">{item.giftName}</h4>
                                                <p className="text-gray-600 flex items-center">
                                                    <span className="mr-1">👤</span>
                                                    {item.recipient}
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-2xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                                                    ${item.amount}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Details */}
                                        <div className="space-y-2 mb-4">
                                            <div className="flex items-center text-sm text-gray-600">
                                                <span className="mr-2">📅</span>
                                                {new Date(item.date).toLocaleDateString('en-US', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                })}
                                            </div>

                                            {item.category && (
                                                <div className="flex items-center text-sm text-gray-600">
                                                    <span className="mr-2">🏷️</span>
                                                    <span className="bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs font-medium">
                                                        {item.category}
                                                    </span>
                                                </div>
                                            )}

                                            {/* Occasion is now included in notes */}
                                        </div>

                                        {/* Notes */}
                                        {item.notes && (
                                            <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-3 rounded-xl border border-amber-200">
                                                <p className="text-sm text-gray-700 italic">
                                                    <span className="mr-1">💭</span>
                                                    {item.notes}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                            {filteredHistory.length === 0 && giftHistory.length === 0 && (
                                <div className="text-center py-8 text-gray-500">
                                    <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M5 5a3 3 0 015-2.236A3 3 0 0114.83 6H16a2 2 0 110 4h-5V9a1 1 0 10-2 0v1H4a2 2 0 110-4h1.17C5.06 5.687 5 5.35 5 5zm4 1V5a1 1 0 10-1 1h1zm3 0a1 1 0 10-1-1v1h1z" clipRule="evenodd" />
                                    </svg>
                                    <p>No gift history yet</p>
                                    <p className="text-sm mt-1">Start giving gifts to build your history!</p>
                                </div>
                            )}
                            {filteredHistory.length === 0 && giftHistory.length > 0 && (
                                <div className="text-center py-8 text-gray-500">
                                    <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                    </svg>
                                    <p>No results found</p>
                                    <p className="text-sm mt-1">Try adjusting your search terms</p>
                                </div>
                            )}

                            {/* Pagination Controls for Gift History */}
                            {filteredHistory.length > 0 && (
                                <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4 bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                    {/* Results Info */}
                                    <div className="text-sm text-gray-600">
                                        Showing {startIndex + 1} to {Math.min(endIndex, filteredHistory.length)} of {filteredHistory.length} history items
                                    </div>

                                    {/* Page Size Selector */}
                                    <div className="flex items-center space-x-2">
                                        <label className="text-sm text-gray-600">Show:</label>
                                        <select
                                            value={itemsPerPage}
                                            onChange={(e) => setItemsPerPage(Number(e.target.value))}
                                            className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                                        >
                                            <option value={6}>6</option>
                                            <option value={9}>9</option>
                                            <option value={12}>12</option>
                                            <option value={18}>18</option>
                                        </select>
                                        <span className="text-sm text-gray-600">per page</span>
                                    </div>

                                    {/* Pagination Buttons */}
                                    {totalPages > 1 && (
                                        <div className="flex items-center space-x-2">
                                            {/* Previous Button */}
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === 1
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 shadow-lg'
                                                    }`}
                                            >
                                                Previous
                                            </button>

                                            {/* Page Numbers */}
                                            <div className="flex items-center space-x-1">
                                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                                    let pageNum;
                                                    if (totalPages <= 5) {
                                                        pageNum = i + 1;
                                                    } else if (currentPage <= 3) {
                                                        pageNum = i + 1;
                                                    } else if (currentPage >= totalPages - 2) {
                                                        pageNum = totalPages - 4 + i;
                                                    } else {
                                                        pageNum = currentPage - 2 + i;
                                                    }

                                                    return (
                                                        <button
                                                            key={pageNum}
                                                            onClick={() => setCurrentPage(pageNum)}
                                                            className={`w-10 h-10 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === pageNum
                                                                ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg transform scale-105'
                                                                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200 hover:border-amber-300'
                                                                }`}
                                                        >
                                                            {pageNum}
                                                        </button>
                                                    );
                                                })}
                                            </div>

                                            {/* Next Button */}
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${currentPage === totalPages
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 shadow-lg'
                                                    }`}
                                            >
                                                Next
                                            </button>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Add to History Modal */}
                {showAddHistoryModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                            <div className="p-6">
                                {/* Modal Header */}
                                <div className="flex items-center justify-between mb-6">
                                    <div className="flex items-center">
                                        <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center mr-4">
                                            <span className="text-2xl">🎁</span>
                                        </div>
                                        <div>
                                            <h3 className="text-xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                                                Add Gift to History
                                            </h3>
                                            <p className="text-sm text-gray-600">Record a gift you've given</p>
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => setShowAddHistoryModal(false)}
                                        className="text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                {/* Form */}
                                <form onSubmit={handleHistoryFormSubmit} className="space-y-4">
                                    {/* Gift Name */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                                            Gift Name *
                                        </label>
                                        <input
                                            type="text"
                                            value={historyFormData.giftName}
                                            onChange={(e) => handleFormInputChange('giftName', e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                            placeholder="e.g., Wireless Headphones"
                                            required
                                        />
                                    </div>

                                    {/* Recipient */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                                            Recipient *
                                        </label>
                                        <input
                                            type="text"
                                            value={historyFormData.recipient}
                                            onChange={(e) => handleFormInputChange('recipient', e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                            placeholder="e.g., John Doe"
                                            required
                                        />
                                    </div>

                                    {/* Amount and Date Row */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                                                Amount *
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={historyFormData.amount}
                                                onChange={(e) => handleFormInputChange('amount', e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                                placeholder="0.00"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                                                Date *
                                            </label>
                                            <input
                                                type="date"
                                                value={historyFormData.date}
                                                onChange={(e) => handleFormInputChange('date', e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                                required
                                            />
                                        </div>
                                    </div>

                                    {/* Category */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                                            Category
                                        </label>
                                        <select
                                            value={historyFormData.category}
                                            onChange={(e) => handleFormInputChange('category', e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                        >
                                            {categories.map(category => (
                                                <option key={category} value={category}>{category}</option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Occasion */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                                            Occasion
                                        </label>
                                        <input
                                            type="text"
                                            value={historyFormData.occasion}
                                            onChange={(e) => handleFormInputChange('occasion', e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300"
                                            placeholder="e.g., Birthday, Anniversary, Christmas"
                                        />
                                    </div>

                                    {/* Notes */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                                            Notes
                                        </label>
                                        <textarea
                                            value={historyFormData.notes}
                                            onChange={(e) => handleFormInputChange('notes', e.target.value)}
                                            rows={3}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-300 resize-none"
                                            placeholder="Any additional notes about this gift..."
                                        />
                                    </div>

                                    {/* Form Actions */}
                                    <div className="flex space-x-3 pt-4">
                                        <button
                                            type="button"
                                            onClick={() => setShowAddHistoryModal(false)}
                                            className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-300 font-semibold"
                                        >
                                            Cancel
                                        </button>
                                        <button
                                            type="submit"
                                            className="flex-1 px-6 py-3 bg-gradient-to-r from-amber-600 to-orange-600 text-white rounded-xl hover:from-amber-700 hover:to-orange-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
                                        >
                                            Add Gift
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default GiftCenter;
