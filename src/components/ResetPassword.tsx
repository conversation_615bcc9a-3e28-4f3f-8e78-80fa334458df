import { useState, useEffect } from "react";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { useErrorNotification } from "../context/ErrorContext";
import <PERSON><PERSON>ield from "./FormField";

const ResetPassword = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isValidToken, setIsValidToken] = useState(true);
    const [errors, setErrors] = useState<{ password?: string; confirmPassword?: string; }>({});
    const { showSuccess, showError } = useErrorNotification();

    const token = searchParams.get('token');
    const email = searchParams.get('email');

    useEffect(() => {
        // Validate token on component mount
        if (!token || !email) {
            setIsValidToken(false);
        }
        // In a real app, you would validate the token with your backend
        // For demo purposes, we'll assume it's valid if both token and email exist
    }, [token, email]);

    const validatePassword = (password: string) => {
        if (!password) {
            return "Password is required";
        }
        if (password.length < 6) {
            return "Password must be at least 6 characters";
        }
        return "";
    };

    const validateConfirmPassword = (confirmPassword: string, password: string) => {
        if (!confirmPassword) {
            return "Please confirm your password";
        }
        if (confirmPassword !== password) {
            return "Passwords do not match";
        }
        return "";
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const passwordError = validatePassword(password);
        const confirmPasswordError = validateConfirmPassword(confirmPassword, password);

        if (passwordError || confirmPasswordError) {
            setErrors({
                password: passwordError,
                confirmPassword: confirmPasswordError
            });
            return;
        }

        setErrors({});
        setIsLoading(true);

        try {
            // Simulate password reset process
            await new Promise(resolve => setTimeout(resolve, 2000));

            // In a real app, you would:
            // 1. Validate the reset token with your backend
            // 2. Update the user's password in the database
            // 3. Invalidate the reset token

            // Update password in SQLite database
            if (email) {
                try {
                    // Import database service
                    const { databaseService } = await import('../services/databaseService');
                    await databaseService.initialize();

                    // Hash the new password
                    const hashedPassword = await hashPassword(password);

                    // Update password in user_auth table
                    await databaseService.query(
                        'UPDATE user_auth SET password_hash = ?, updated_at = ? WHERE email = ?',
                        [hashedPassword, new Date().toISOString(), email]
                    );

                    console.log('✅ Password updated in SQLite database');
                } catch (error) {
                    console.error('❌ Failed to update password in database:', error);
                    throw new Error('Failed to update password');
                }
            }

            setIsSubmitted(true);
            showSuccess("Password reset successfully!");

            // Redirect to login after 3 seconds
            setTimeout(() => {
                navigate('/login');
            }, 3000);

        } catch (error) {
            showError("Failed to reset password. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handlePasswordChange = (value: string) => {
        setPassword(value);
        if (errors.password) {
            const error = validatePassword(value);
            setErrors(prev => ({ ...prev, password: error }));
        }
        if (confirmPassword && errors.confirmPassword) {
            const confirmError = validateConfirmPassword(confirmPassword, value);
            setErrors(prev => ({ ...prev, confirmPassword: confirmError }));
        }
    };

    const handleConfirmPasswordChange = (value: string) => {
        setConfirmPassword(value);
        if (errors.confirmPassword) {
            const error = validateConfirmPassword(value, password);
            setErrors(prev => ({ ...prev, confirmPassword: error }));
        }
    };

    const handlePasswordBlur = () => {
        const error = validatePassword(password);
        setErrors(prev => ({ ...prev, password: error }));
    };

    const handleConfirmPasswordBlur = () => {
        const error = validateConfirmPassword(confirmPassword, password);
        setErrors(prev => ({ ...prev, confirmPassword: error }));
    };

    // Helper function to hash password (simplified for demo)
    const hashPassword = async (password: string): Promise<string> => {
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    };

    if (!isValidToken) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="bg-white p-8 rounded-lg shadow-md text-center">
                        <div className="mb-6">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </div>
                        </div>
                        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Invalid Reset Link</h2>
                        <p className="text-gray-600 mb-6">
                            This password reset link is invalid or has expired.
                        </p>
                        <div className="space-y-3">
                            <Link
                                to="/forgot-password"
                                className="block w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition text-center"
                            >
                                Request New Reset Link
                            </Link>
                            <Link
                                to="/login"
                                className="block w-full text-center text-blue-600 hover:text-blue-800 underline"
                            >
                                Back to Sign In
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (isSubmitted) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="bg-white p-8 rounded-lg shadow-md text-center">
                        <div className="mb-6">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        </div>
                        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Password Reset Complete</h2>
                        <p className="text-gray-600 mb-6">
                            Your password has been successfully reset. You can now sign in with your new password.
                        </p>
                        <p className="text-sm text-gray-500 mb-6">
                            Redirecting to sign in page in 3 seconds...
                        </p>
                        <Link
                            to="/login"
                            className="block w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition text-center"
                        >
                            Sign In Now
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="bg-white p-8 rounded-lg shadow-md">
                    <div className="text-center mb-6">
                        <h2 className="text-2xl font-semibold mb-2">Reset Your Password</h2>
                        <p className="text-gray-600 text-sm">
                            Enter your new password below.
                        </p>
                        {email && (
                            <p className="text-sm text-gray-500 mt-2">
                                Resetting password for: <strong>{email}</strong>
                            </p>
                        )}
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <FormField
                            label="New Password"
                            name="password"
                            type="password"
                            value={password}
                            onChange={handlePasswordChange}
                            onBlur={handlePasswordBlur}
                            error={errors.password}
                            touched={!!errors.password}
                            placeholder="Enter your new password"
                            autoFocus
                            required
                            minLength={6}
                        />

                        <FormField
                            label="Confirm New Password"
                            name="confirmPassword"
                            type="password"
                            value={confirmPassword}
                            onChange={handleConfirmPasswordChange}
                            onBlur={handleConfirmPasswordBlur}
                            error={errors.confirmPassword}
                            touched={!!errors.confirmPassword}
                            placeholder="Confirm your new password"
                            required
                            minLength={6}
                        />

                        <button
                            type="submit"
                            disabled={isLoading || !!errors.password || !!errors.confirmPassword || !password || !confirmPassword}
                            className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                            {isLoading ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Resetting Password...
                                </>
                            ) : (
                                "Reset Password"
                            )}
                        </button>
                    </form>

                    {/* Navigation Links */}
                    <div className="mt-6 text-center">
                        <Link
                            to="/login"
                            className="text-sm text-blue-600 hover:text-blue-800 underline"
                        >
                            Back to Sign In
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;
