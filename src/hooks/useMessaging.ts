import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { messagingService } from '../services/messagingService';
import type {
  MessageTemplate,
  ScheduledMessage,
  NotificationSettings,
  MessageHistory,
  MessagingStats,
  WeeklyAdminNotification
} from '../types/messaging';
import type { Contact } from '../types';
import { useAuth } from '../context/AuthContext';
import { useEffect } from 'react';

// Query Keys
export const messagingKeys = {
  all: ['messaging'] as const,
  templates: () => [...messagingKeys.all, 'templates'] as const,
  scheduledMessages: () => [...messagingKeys.all, 'scheduled'] as const,
  settings: () => [...messagingKeys.all, 'settings'] as const,
  history: () => [...messagingKeys.all, 'history'] as const,
  stats: () => [...messagingKeys.all, 'stats'] as const,
  weeklyNotifications: () => [...messagingKeys.all, 'weekly'] as const,
};

// Template Hooks
export const useTemplatesQuery = () => {
  const { user } = useAuth();

  // Set current user in messaging service
  useEffect(() => {
    if (user?.email) {
      messagingService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useQuery({
    queryKey: messagingKeys.templates(),
    queryFn: () => messagingService.getTemplates(),
    enabled: !!user?.email,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (template: Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>) =>
      messagingService.createTemplate(template),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.templates() });
    },
  });
};

export const useUpdateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<MessageTemplate>; }) =>
      messagingService.updateTemplate(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.templates() });
    },
  });
};

export const useDeleteTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => messagingService.deleteTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.templates() });
    },
  });
};

export const useResetTemplates = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => messagingService.resetToDefaultTemplates(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.templates() });
    },
  });
};

export const useCleanupStorage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => messagingService.cleanupStorage(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.templates() });
    },
  });
};

// Scheduled Messages Hooks
export const useScheduledMessagesQuery = () => {
  return useQuery({
    queryKey: messagingKeys.scheduledMessages(),
    queryFn: () => messagingService.getScheduledMessages(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useScheduleMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      contactId,
      templateId,
      scheduledFor,
      type,
      variables,
    }: {
      contactId: string;
      templateId: string;
      scheduledFor: Date;
      type: 'birthday' | 'landmark' | 'reminder';
      variables: Record<string, string>;
    }) => messagingService.scheduleMessage(contactId, templateId, scheduledFor, type, variables),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.scheduledMessages() });
    },
  });
};

// Settings Hooks
export const useSettingsQuery = () => {
  return useQuery({
    queryKey: messagingKeys.settings(),
    queryFn: () => messagingService.getSettings(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useUpdateSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: Partial<NotificationSettings>) =>
      messagingService.updateSettings(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.settings() });
    },
  });
};

// Message History Hooks
export const useMessageHistoryQuery = () => {
  return useQuery({
    queryKey: messagingKeys.history(),
    queryFn: () => messagingService.getMessageHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Statistics Hooks
export const useMessagingStatsQuery = () => {
  return useQuery({
    queryKey: messagingKeys.stats(),
    queryFn: () => messagingService.getMessagingStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Weekly Notifications Hooks
export const useGenerateWeeklyNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (contacts: Contact[]) =>
      messagingService.generateWeeklyAdminNotification(contacts),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.weeklyNotifications() });
      queryClient.invalidateQueries({ queryKey: messagingKeys.stats() });
    },
  });
};

// Birthday Processing Hook
export const useProcessBirthdayMessages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (contacts: Contact[]) =>
      messagingService.processBirthdayMessages(contacts),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.scheduledMessages() });
      queryClient.invalidateQueries({ queryKey: messagingKeys.stats() });
    },
  });
};

// Utility Hooks
export const useLandmarkBirthday = (age: number) => {
  return {
    isLandmark: messagingService.isLandmarkBirthday(age) !== null,
    landmark: messagingService.getLandmarkBirthday(age),
  };
};
