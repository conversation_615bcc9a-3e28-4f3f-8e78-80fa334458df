import { renderHook, act } from '@testing-library/react';
import { useFormValidation } from '../useFormValidation';

interface TestFormData {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
}

const initialValues: TestFormData = {
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
};

const validationConfig = {
    email: {
        required: true,
        email: true,
    },
    password: {
        required: true,
        minLength: 6,
        custom: (value: string) => {
            if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                return 'Password must contain uppercase, lowercase, and number';
            }
            return null;
        },
    },
    confirmPassword: {
        required: true,
        match: 'password',
    },
    name: {
        required: true,
        minLength: 2,
        maxLength: 50,
    },
};

describe('useFormValidation', () => {
    it('should initialize with default values', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        expect(result.current.values).toEqual(initialValues);
        expect(result.current.errors).toEqual({});
        expect(result.current.touched).toEqual({});
        expect(result.current.isSubmitting).toBe(false);
    });

    it('should update field values', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setValue('email', '<EMAIL>');
        });

        expect(result.current.values.email).toBe('<EMAIL>');
    });

    it('should validate required fields', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setFieldTouched('email');
        });

        expect(result.current.errors.email).toBe('email is required');
    });

    it('should validate email format', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setValue('email', 'invalid-email');
            result.current.setFieldTouched('email');
        });

        expect(result.current.errors.email).toBe('Please enter a valid email address');
    });

    it('should validate minimum length', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setValue('password', '123');
            result.current.setFieldTouched('password');
        });

        expect(result.current.errors.password).toBe('password must be at least 6 characters');
    });

    it('should validate custom validation rules', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setValue('password', 'weakpassword');
            result.current.setFieldTouched('password');
        });

        expect(result.current.errors.password).toBe('Password must contain uppercase, lowercase, and number');
    });

    it('should validate field matching', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            result.current.setValue('password', 'Password123');
            result.current.setValue('confirmPassword', 'DifferentPassword');
            result.current.setFieldTouched('confirmPassword');
        });

        expect(result.current.errors.confirmPassword).toBe('confirmPassword must match password');
    });

    it('should validate maximum length', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        const longName = 'a'.repeat(51);
        
        act(() => {
            result.current.setValue('name', longName);
            result.current.setFieldTouched('name');
        });

        expect(result.current.errors.name).toBe('name must be no more than 50 characters');
    });

    it('should clear errors when field value changes', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        // First set an error
        act(() => {
            result.current.setFieldTouched('email');
        });

        expect(result.current.errors.email).toBeTruthy();

        // Then update the field value
        act(() => {
            result.current.setValue('email', '<EMAIL>');
        });

        expect(result.current.errors.email).toBe('');
    });

    it('should validate entire form', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        act(() => {
            const isValid = result.current.validateForm();
            expect(isValid).toBe(false);
        });

        // Should have errors for all required fields
        expect(result.current.errors.email).toBeTruthy();
        expect(result.current.errors.password).toBeTruthy();
        expect(result.current.errors.name).toBeTruthy();
    });

    it('should handle form submission', async () => {
        const mockSubmit = jest.fn();
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        // Fill out valid form
        act(() => {
            result.current.setValue('email', '<EMAIL>');
            result.current.setValue('password', 'Password123');
            result.current.setValue('confirmPassword', 'Password123');
            result.current.setValue('name', 'Test User');
        });

        const handleSubmit = result.current.handleSubmit(mockSubmit);
        const mockEvent = { preventDefault: jest.fn() } as any;

        await act(async () => {
            await handleSubmit(mockEvent);
        });

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(mockSubmit).toHaveBeenCalledWith({
            email: '<EMAIL>',
            password: 'Password123',
            confirmPassword: 'Password123',
            name: 'Test User',
        });
    });

    it('should not submit invalid form', async () => {
        const mockSubmit = jest.fn();
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        const handleSubmit = result.current.handleSubmit(mockSubmit);
        const mockEvent = { preventDefault: jest.fn() } as any;

        await act(async () => {
            await handleSubmit(mockEvent);
        });

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(mockSubmit).not.toHaveBeenCalled();
    });

    it('should reset form', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        // Make some changes
        act(() => {
            result.current.setValue('email', '<EMAIL>');
            result.current.setFieldTouched('email');
        });

        expect(result.current.values.email).toBe('<EMAIL>');
        expect(result.current.touched.email).toBe(true);

        // Reset form
        act(() => {
            result.current.resetForm();
        });

        expect(result.current.values).toEqual(initialValues);
        expect(result.current.errors).toEqual({});
        expect(result.current.touched).toEqual({});
        expect(result.current.isSubmitting).toBe(false);
    });

    it('should calculate isValid correctly', () => {
        const { result } = renderHook(() =>
            useFormValidation(initialValues, validationConfig)
        );

        // Initially invalid (required fields empty)
        expect(result.current.isValid).toBe(false);

        // Fill out valid form
        act(() => {
            result.current.setValue('email', '<EMAIL>');
            result.current.setValue('password', 'Password123');
            result.current.setValue('confirmPassword', 'Password123');
            result.current.setValue('name', 'Test User');
        });

        expect(result.current.isValid).toBe(true);
    });
});
