import { renderHook, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useFeatureAccess, useUsageTracking } from '../useFeatureAccess';
import { useAuth } from '../../context/AuthContext';
import { useSubscription } from '../../context/SubscriptionContext';
import { useSubscriptionStatus } from '../useStripe';
import { usageTrackingService } from '../../services/usageTrackingService';

// Mock dependencies
jest.mock('../../context/AuthContext');
jest.mock('../../context/SubscriptionContext');
jest.mock('../useStripe');
jest.mock('../../services/usageTrackingService');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseSubscription = useSubscription as jest.MockedFunction<typeof useSubscription>;
const mockUseSubscriptionStatus = useSubscriptionStatus as jest.MockedFunction<typeof useSubscriptionStatus>;
const mockUsageTrackingService = usageTrackingService as jest.Mocked<typeof usageTrackingService>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useFeatureAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mocks
    mockUseAuth.mockReturnValue({
      user: { email: '<EMAIL>', uid: 'test-uid' },
      loading: false,
      signup: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
    } as any);

    mockUseSubscription.mockReturnValue({
      plan: 'free',
      isTrialActive: false,
      trialDaysLeft: 0,
    } as any);

    mockUseSubscriptionStatus.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    } as any);
  });

  describe('free plan access', () => {
    it('should allow basic features for free plan', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('CONTACTS', 'ADD')).toBe(true);
      expect(result.current.hasAccess('CONTACTS', 'VIEW')).toBe(true);
      expect(result.current.hasAccess('REMINDERS', 'CREATE')).toBe(true);
    });

    it('should restrict premium features for free plan', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('ANALYTICS', 'ADVANCED')).toBe(false);
      expect(result.current.hasAccess('MESSAGING', 'BULK')).toBe(false);
      expect(result.current.hasAccess('GIFTS', 'ADVANCED')).toBe(false);
    });

    it('should return correct usage limits for free plan', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getUsageLimit('contacts')).toBe(10);
      expect(result.current.getUsageLimit('reminders')).toBe(5);
      expect(result.current.getUsageLimit('messages')).toBe(3);
    });
  });

  describe('trial plan access', () => {
    beforeEach(() => {
      mockUseSubscription.mockReturnValue({
        plan: 'trial',
        isTrialActive: true,
        trialDaysLeft: 10,
      } as any);
    });

    it('should allow standard features during trial', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('CONTACTS', 'BULK_IMPORT')).toBe(true);
      expect(result.current.hasAccess('MESSAGING', 'TEMPLATES')).toBe(true);
      expect(result.current.hasAccess('ANALYTICS', 'BASIC')).toBe(true);
    });

    it('should return correct usage limits for trial', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getUsageLimit('contacts')).toBe(100);
      expect(result.current.getUsageLimit('reminders')).toBe(25);
      expect(result.current.getUsageLimit('messages')).toBe(50);
    });
  });

  describe('standard plan access', () => {
    beforeEach(() => {
      mockUseSubscription.mockReturnValue({
        plan: 'standard',
        isTrialActive: false,
        trialDaysLeft: 0,
      } as any);

      mockUseSubscriptionStatus.mockReturnValue({
        data: { status: 'active', plan: { nickname: 'Standard' } },
        isLoading: false,
        error: null,
      } as any);
    });

    it('should allow standard features', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('CONTACTS', 'BULK_IMPORT')).toBe(true);
      expect(result.current.hasAccess('MESSAGING', 'TEMPLATES')).toBe(true);
      expect(result.current.hasAccess('ANALYTICS', 'BASIC')).toBe(true);
    });

    it('should restrict elite/premium features', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('ANALYTICS', 'ADVANCED')).toBe(false);
      expect(result.current.hasAccess('MESSAGING', 'BULK')).toBe(false);
      expect(result.current.hasAccess('GIFTS', 'ADVANCED')).toBe(false);
    });

    it('should return correct usage limits for standard plan', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getUsageLimit('contacts')).toBe(100);
      expect(result.current.getUsageLimit('reminders')).toBe(25);
      expect(result.current.getUsageLimit('messages')).toBe(50);
    });
  });

  describe('elite plan access', () => {
    beforeEach(() => {
      mockUseSubscription.mockReturnValue({
        plan: 'elite',
        isTrialActive: false,
        trialDaysLeft: 0,
      } as any);

      mockUseSubscriptionStatus.mockReturnValue({
        data: { status: 'active', plan: { nickname: 'Elite' } },
        isLoading: false,
        error: null,
      } as any);
    });

    it('should allow elite features', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('ANALYTICS', 'ADVANCED')).toBe(true);
      expect(result.current.hasAccess('MESSAGING', 'BULK')).toBe(true);
      expect(result.current.hasAccess('GIFTS', 'ADVANCED')).toBe(true);
    });

    it('should restrict premium features', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('ADMIN', 'USER_MANAGEMENT')).toBe(false);
      expect(result.current.hasAccess('ADMIN', 'SYSTEM_CONFIG')).toBe(false);
    });

    it('should return correct usage limits for elite plan', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getUsageLimit('contacts')).toBe(500);
      expect(result.current.getUsageLimit('reminders')).toBe(100);
      expect(result.current.getUsageLimit('messages')).toBe(200);
    });
  });

  describe('premium plan access', () => {
    beforeEach(() => {
      mockUseSubscription.mockReturnValue({
        plan: 'premium',
        isTrialActive: false,
        trialDaysLeft: 0,
      } as any);

      mockUseSubscriptionStatus.mockReturnValue({
        data: { status: 'active', plan: { nickname: 'Premium' } },
        isLoading: false,
        error: null,
      } as any);
    });

    it('should allow all features', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasAccess('ADMIN', 'USER_MANAGEMENT')).toBe(true);
      expect(result.current.hasAccess('ADMIN', 'SYSTEM_CONFIG')).toBe(true);
      expect(result.current.hasAccess('ANALYTICS', 'ADVANCED')).toBe(true);
    });

    it('should return unlimited usage limits', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getUsageLimit('contacts')).toBe(-1);
      expect(result.current.getUsageLimit('reminders')).toBe(-1);
      expect(result.current.getUsageLimit('messages')).toBe(-1);
    });
  });

  describe('usage limit checking', () => {
    it('should return true when within usage limit', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isWithinUsageLimit('contacts', 5)).toBe(true);
    });

    it('should return false when exceeding usage limit', () => {
      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isWithinUsageLimit('contacts', 15)).toBe(false);
    });

    it('should return true for unlimited features', () => {
      mockUseSubscription.mockReturnValue({
        plan: 'premium',
        isTrialActive: false,
        trialDaysLeft: 0,
      } as any);

      const { result } = renderHook(() => useFeatureAccess(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isWithinUsageLimit('contacts', 1000)).toBe(true);
    });
  });
});

describe('useUsageTracking', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      user: { email: '<EMAIL>', uid: 'test-uid' },
      loading: false,
      signup: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
    } as any);

    mockUsageTrackingService.setCurrentUser.mockImplementation();
    mockUsageTrackingService.getCurrentUsage.mockResolvedValue(5);
    mockUsageTrackingService.incrementUsage.mockResolvedValue(true);
    mockUsageTrackingService.resetUsage.mockResolvedValue();
  });

  it('should initialize with current usage', async () => {
    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('test-feature'),
      { wrapper: createWrapper() }
    );

    expect(result.current.isLoading).toBe(true);

    await waitForNextUpdate();

    expect(mockUsageTrackingService.setCurrentUser).toHaveBeenCalledWith('<EMAIL>');
    expect(mockUsageTrackingService.getCurrentUsage).toHaveBeenCalledWith('test-feature');
    expect(result.current.currentUsage).toBe(5);
    expect(result.current.isLoading).toBe(false);
  });

  it('should increment usage successfully', async () => {
    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('test-feature'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    await act(async () => {
      const success = await result.current.incrementUsage();
      expect(success).toBe(true);
    });

    expect(mockUsageTrackingService.incrementUsage).toHaveBeenCalledWith('test-feature');
    expect(result.current.currentUsage).toBe(6);
  });

  it('should not increment when usage limit exceeded', async () => {
    // Mock free plan with limit of 10
    mockUseSubscription.mockReturnValue({
      plan: 'free',
      isTrialActive: false,
      trialDaysLeft: 0,
    } as any);

    mockUsageTrackingService.getCurrentUsage.mockResolvedValue(10);

    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('contacts'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    await act(async () => {
      const success = await result.current.incrementUsage();
      expect(success).toBe(false);
    });

    expect(mockUsageTrackingService.incrementUsage).not.toHaveBeenCalled();
  });

  it('should reset usage successfully', async () => {
    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('test-feature'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    await act(async () => {
      await result.current.resetUsage();
    });

    expect(mockUsageTrackingService.resetUsage).toHaveBeenCalledWith('test-feature');
    expect(result.current.currentUsage).toBe(0);
  });

  it('should handle errors gracefully', async () => {
    mockUsageTrackingService.getCurrentUsage.mockRejectedValue(new Error('Database error'));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('test-feature'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    expect(result.current.currentUsage).toBe(0);
    expect(result.current.isLoading).toBe(false);
    expect(consoleSpy).toHaveBeenCalledWith('Error loading usage:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('should refresh usage data', async () => {
    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('test-feature'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    mockUsageTrackingService.getCurrentUsage.mockResolvedValue(8);

    await act(async () => {
      await result.current.refresh();
    });

    expect(result.current.currentUsage).toBe(8);
  });

  it('should calculate usage percentage correctly', async () => {
    mockUseSubscription.mockReturnValue({
      plan: 'free',
      isTrialActive: false,
      trialDaysLeft: 0,
    } as any);

    const { result, waitForNextUpdate } = renderHook(
      () => useUsageTracking('contacts'),
      { wrapper: createWrapper() }
    );

    await waitForNextUpdate();

    expect(result.current.usagePercentage).toBe(50); // 5/10 * 100
    expect(result.current.limit).toBe(10);
    expect(result.current.withinLimit).toBe(true);
  });
});
