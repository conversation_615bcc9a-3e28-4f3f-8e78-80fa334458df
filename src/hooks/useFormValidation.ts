import { useState, useCallback, useMemo } from 'react';

export interface ValidationRule {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
    email?: boolean;
    match?: string; // field name to match against
}

export interface FieldConfig {
    [fieldName: string]: ValidationRule;
}

export interface FormErrors {
    [fieldName: string]: string;
}

export interface FormTouched {
    [fieldName: string]: boolean;
}

export interface UseFormValidationReturn<T> {
    values: T;
    errors: FormErrors;
    touched: FormTouched;
    isValid: boolean;
    isSubmitting: boolean;
    setValue: (field: keyof T, value: any) => void;
    setFieldTouched: (field: keyof T, touched?: boolean) => void;
    setSubmitting: (submitting: boolean) => void;
    validateField: (field: keyof T) => string | null;
    validateForm: () => boolean;
    resetForm: () => void;
    handleSubmit: (onSubmit: (values: T) => void | Promise<void>) => (e: React.FormEvent) => Promise<void>;
}

export function useFormValidation<T extends Record<string, any>>(
    initialValues: T,
    validationConfig: FieldConfig
): UseFormValidationReturn<T> {
    const [values, setValues] = useState<T>(initialValues);
    const [errors, setErrors] = useState<FormErrors>({});
    const [touched, setTouched] = useState<FormTouched>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Validation functions
    const validateField = useCallback((field: keyof T): string | null => {
        const value = values[field];
        const rules = validationConfig[field as string];
        
        if (!rules) return null;

        // Required validation
        if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
            return `${String(field)} is required`;
        }

        // Skip other validations if field is empty and not required
        if (!value || (typeof value === 'string' && value.trim() === '')) {
            return null;
        }

        // Email validation
        if (rules.email && typeof value === 'string') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                return 'Please enter a valid email address';
            }
        }

        // Pattern validation
        if (rules.pattern && typeof value === 'string') {
            if (!rules.pattern.test(value)) {
                return `${String(field)} format is invalid`;
            }
        }

        // Length validations
        if (typeof value === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
                return `${String(field)} must be at least ${rules.minLength} characters`;
            }
            if (rules.maxLength && value.length > rules.maxLength) {
                return `${String(field)} must be no more than ${rules.maxLength} characters`;
            }
        }

        // Match validation (for password confirmation)
        if (rules.match && values[rules.match] !== value) {
            return `${String(field)} must match ${rules.match}`;
        }

        // Custom validation
        if (rules.custom) {
            const customError = rules.custom(value);
            if (customError) return customError;
        }

        return null;
    }, [values, validationConfig]);

    const validateForm = useCallback((): boolean => {
        const newErrors: FormErrors = {};
        let isFormValid = true;

        Object.keys(validationConfig).forEach(field => {
            const error = validateField(field as keyof T);
            if (error) {
                newErrors[field] = error;
                isFormValid = false;
            }
        });

        setErrors(newErrors);
        return isFormValid;
    }, [validateField, validationConfig]);

    const setValue = useCallback((field: keyof T, value: any) => {
        setValues(prev => ({ ...prev, [field]: value }));
        
        // Clear error when user starts typing
        if (errors[field as string]) {
            setErrors(prev => ({ ...prev, [field as string]: '' }));
        }
    }, [errors]);

    const setFieldTouched = useCallback((field: keyof T, isTouched: boolean = true) => {
        setTouched(prev => ({ ...prev, [field as string]: isTouched }));
        
        // Validate field when it's touched
        if (isTouched) {
            const error = validateField(field);
            setErrors(prev => ({ ...prev, [field as string]: error || '' }));
        }
    }, [validateField]);

    const setSubmitting = useCallback((submitting: boolean) => {
        setIsSubmitting(submitting);
    }, []);

    const resetForm = useCallback(() => {
        setValues(initialValues);
        setErrors({});
        setTouched({});
        setIsSubmitting(false);
    }, [initialValues]);

    const handleSubmit = useCallback((onSubmit: (values: T) => void | Promise<void>) => {
        return async (e: React.FormEvent) => {
            e.preventDefault();
            setIsSubmitting(true);

            // Mark all fields as touched
            const allTouched: FormTouched = {};
            Object.keys(validationConfig).forEach(field => {
                allTouched[field] = true;
            });
            setTouched(allTouched);

            // Validate form
            if (validateForm()) {
                try {
                    await onSubmit(values);
                } catch (error) {
                    console.error('Form submission error:', error);
                } finally {
                    setIsSubmitting(false);
                }
            } else {
                setIsSubmitting(false);
            }
        };
    }, [values, validateForm, validationConfig]);

    const isValid = useMemo(() => {
        return Object.keys(validationConfig).every(field => !validateField(field as keyof T));
    }, [validationConfig, validateField]);

    return {
        values,
        errors,
        touched,
        isValid,
        isSubmitting,
        setValue,
        setFieldTouched,
        setSubmitting,
        validateField,
        validateForm,
        resetForm,
        handleSubmit,
    };
}
