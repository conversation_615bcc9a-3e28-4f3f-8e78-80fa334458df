import React, { useMemo, useState, useEffect } from 'react';
import { useSubscriptionStatus } from './useStripe';
import { useSubscription } from '../context/SubscriptionContext';
import { usageTrackingService } from '../services/usageTrackingService';
import { useAuth } from './useAuth';

// Feature definitions with plan requirements
export interface FeatureConfig {
  name: string;
  description: string;
  requiredPlan: 'Free' | 'Standard' | 'Elite' | 'Premium';
  usageLimit?: {
    free: number;
    standard: number;
    elite: number;
    premium: number;
  };
}

export const FEATURES: Record<string, FeatureConfig> = {
  CONTACTS: {
    name: 'Contacts',
    description: 'Store and manage birthday contacts',
    requiredPlan: 'Free',
    usageLimit: {
      free: 10,
      standard: 100,
      elite: 500,
      premium: -1, // unlimited
    },
  },
  BASIC_REMINDERS: {
    name: 'Basic Reminders',
    description: 'Set simple birthday reminders',
    requiredPlan: 'Free',
  },
  ADVANCED_REMINDERS: {
    name: 'Advanced Reminders',
    description: 'Custom reminder schedules and notifications',
    requiredPlan: 'Standard',
  },
  BASIC_ANALYTICS: {
    name: 'Basic Analytics',
    description: 'View basic birthday statistics',
    requiredPlan: 'Standard',
  },
  ADVANCED_ANALYTICS: {
    name: 'Advanced Analytics',
    description: 'Detailed insights and reporting',
    requiredPlan: 'Elite',
  },
  AUTOMATED_MESSAGING: {
    name: 'Automated Messaging',
    description: 'Automated birthday messages and workflows',
    requiredPlan: 'Elite',
  },
  LANDMARK_BIRTHDAYS: {
    name: 'Landmark Birthdays',
    description: 'Special milestone birthday detection',
    requiredPlan: 'Elite',
  },
  WEEKLY_NOTIFICATIONS: {
    name: 'Weekly Admin Notifications',
    description: 'Weekly summary of upcoming birthdays',
    requiredPlan: 'Elite',
  },
  MESSAGE_TEMPLATES: {
    name: 'Message Templates',
    description: 'Customizable message templates',
    requiredPlan: 'Elite',
  },
  GIFT_SUGGESTIONS: {
    name: 'Gift Suggestions',
    description: 'AI-powered gift recommendations',
    requiredPlan: 'Elite',
  },
  CUSTOM_INTEGRATIONS: {
    name: 'Custom Integrations',
    description: 'Connect with external services',
    requiredPlan: 'Premium',
  },
  TEAM_COLLABORATION: {
    name: 'Team Collaboration',
    description: 'Share contacts and collaborate with team members',
    requiredPlan: 'Premium',
  },
  PRIORITY_SUPPORT: {
    name: 'Priority Support',
    description: '24/7 priority customer support',
    requiredPlan: 'Elite',
  },
  API_ACCESS: {
    name: 'API Access',
    description: 'Access to WeWish API for custom integrations',
    requiredPlan: 'Premium',
  },
  WHITE_LABEL: {
    name: 'White Label',
    description: 'Remove WeWish branding and use your own',
    requiredPlan: 'Premium',
  },
};

// Plan hierarchy for comparison
const PLAN_HIERARCHY = {
  Free: 0,
  Trial: 4, // Trial has access to all Premium features
  Standard: 1,
  Elite: 2,
  Premium: 3,
} as const;

export const useFeatureAccess = () => {
  const { plan: contextPlan, isTrialActive } = useSubscription();
  const { subscription, hasActiveSubscription } = useSubscriptionStatus();

  // Determine current plan
  const currentPlan = useMemo(() => {
    if (isTrialActive) return 'Trial';
    if (hasActiveSubscription && subscription?.plan?.nickname) {
      return subscription.plan.nickname;
    }
    return contextPlan;
  }, [isTrialActive, hasActiveSubscription, subscription, contextPlan]);

  // Check if user has access to a specific feature
  const hasFeatureAccess = (featureKey: string): boolean => {
    const feature = FEATURES[featureKey];
    if (!feature) return false;

    const userPlanLevel = PLAN_HIERARCHY[currentPlan as keyof typeof PLAN_HIERARCHY] ?? 0;
    const requiredPlanLevel = PLAN_HIERARCHY[feature.requiredPlan];

    return userPlanLevel >= requiredPlanLevel;
  };

  // Check usage limit for a feature
  const getUsageLimit = (featureKey: string): number => {
    const feature = FEATURES[featureKey];
    if (!feature?.usageLimit) return -1; // unlimited

    const planKey = currentPlan.toLowerCase() as keyof typeof feature.usageLimit;
    return feature.usageLimit[planKey] ?? feature.usageLimit.free;
  };

  // Check if user is within usage limit
  const isWithinUsageLimit = (featureKey: string, currentUsage: number): boolean => {
    const limit = getUsageLimit(featureKey);
    return limit === -1 || currentUsage < limit;
  };

  // Get upgrade suggestion for a feature
  const getUpgradeSuggestion = (featureKey: string): string | null => {
    const feature = FEATURES[featureKey];
    if (!feature) return null;

    if (hasFeatureAccess(featureKey)) return null;

    return feature.requiredPlan;
  };

  // Get all available features for current plan
  const getAvailableFeatures = (): string[] => {
    return Object.keys(FEATURES).filter(hasFeatureAccess);
  };

  // Get all restricted features
  const getRestrictedFeatures = (): string[] => {
    return Object.keys(FEATURES).filter(key => !hasFeatureAccess(key));
  };

  // Feature access summary
  const getFeatureSummary = () => {
    const available = getAvailableFeatures();
    const restricted = getRestrictedFeatures();

    return {
      currentPlan,
      totalFeatures: Object.keys(FEATURES).length,
      availableCount: available.length,
      restrictedCount: restricted.length,
      available: available.map(key => ({
        key,
        ...FEATURES[key],
        usageLimit: getUsageLimit(key),
      })),
      restricted: restricted.map(key => ({
        key,
        ...FEATURES[key],
        upgradeTo: FEATURES[key].requiredPlan,
      })),
    };
  };

  return {
    currentPlan,
    hasFeatureAccess,
    getUsageLimit,
    isWithinUsageLimit,
    getUpgradeSuggestion,
    getAvailableFeatures,
    getRestrictedFeatures,
    getFeatureSummary,
    isTrialActive,
    hasActiveSubscription,
  };
};

// Higher-order component for feature gating
export const withFeatureAccess = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  requiredFeature: string,
  fallbackComponent?: React.ComponentType<any>
) => {
  return (props: P) => {
    const { hasFeatureAccess, getUpgradeSuggestion } = useFeatureAccess();

    if (hasFeatureAccess(requiredFeature)) {
      return React.createElement(WrappedComponent, props);
    }

    if (fallbackComponent) {
      const FallbackComponent = fallbackComponent;
      return React.createElement(FallbackComponent, { requiredPlan: getUpgradeSuggestion(requiredFeature) });
    }

    return React.createElement('div', {
      className: 'p-6 text-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300'
    }, [
      React.createElement('div', {
        key: 'content',
        className: 'text-gray-600 mb-4'
      }, [
        React.createElement('svg', {
          key: 'icon',
          className: 'mx-auto h-12 w-12 text-gray-400 mb-4',
          fill: 'none',
          viewBox: '0 0 24 24',
          stroke: 'currentColor'
        }, React.createElement('path', {
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          strokeWidth: 2,
          d: 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
        })),
        React.createElement('h3', {
          key: 'title',
          className: 'text-lg font-medium text-gray-900 mb-2'
        }, 'Premium Feature'),
        React.createElement('p', {
          key: 'description',
          className: 'text-gray-600 mb-4'
        }, `This feature requires a ${getUpgradeSuggestion(requiredFeature)} plan or higher.`),
        React.createElement('button', {
          key: 'button',
          className: 'bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors'
        }, 'Upgrade Now')
      ])
    ]);
  };
};

// Hook for usage tracking with SQLite
export const useUsageTracking = (featureKey: string) => {
  const { getUsageLimit, isWithinUsageLimit } = useFeatureAccess();
  const { user } = useAuth();
  const [currentUsage, setCurrentUsage] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Initialize usage tracking service with current user
  useEffect(() => {
    if (user?.uid) {
      usageTrackingService.setCurrentUser(user.uid);
      loadCurrentUsage();
    }
  }, [user?.uid, featureKey]);

  const loadCurrentUsage = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const usage = await usageTrackingService.getCurrentUsage(featureKey);
      setCurrentUsage(usage);
    } catch (error) {
      console.error('Error loading usage:', error);
      setCurrentUsage(0);
    } finally {
      setIsLoading(false);
    }
  };

  const incrementUsage = async (): Promise<boolean> => {
    try {
      const limit = getUsageLimit(featureKey);

      if (limit !== -1 && currentUsage >= limit) {
        return false; // Usage limit exceeded
      }

      const success = await usageTrackingService.incrementUsage(featureKey);
      if (success) {
        setCurrentUsage(prev => prev + 1);
      }
      return success;
    } catch (error) {
      console.error('Error incrementing usage:', error);
      return false;
    }
  };

  const resetUsage = async (): Promise<void> => {
    try {
      await usageTrackingService.resetUsage(featureKey);
      setCurrentUsage(0);
    } catch (error) {
      console.error('Error resetting usage:', error);
    }
  };

  const limit = getUsageLimit(featureKey);
  const withinLimit = isWithinUsageLimit(featureKey, currentUsage);

  return {
    currentUsage,
    limit,
    withinLimit,
    usagePercentage: limit === -1 ? 0 : (currentUsage / limit) * 100,
    incrementUsage,
    resetUsage,
    isLoading,
    refresh: loadCurrentUsage,
  };
};
