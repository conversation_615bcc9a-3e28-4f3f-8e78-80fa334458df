import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { giftService, type GiftHistoryItem, type GiftReminder } from '../services/giftService';
import { useAuth } from '../context/AuthContext';

// Query keys for gifts
export const giftKeys = {
  all: ['gifts'] as const,
  history: () => [...giftKeys.all, 'history'] as const,
  userHistory: (userEmail: string) => [...giftKeys.history(), userEmail] as const,
  contactHistory: (userEmail: string, contactId: string) => [...giftKeys.history(), userEmail, contactId] as const,
  reminders: () => [...giftKeys.all, 'reminders'] as const,
  userReminders: (userEmail: string) => [...giftKeys.reminders(), userEmail] as const,
  contactReminder: (userEmail: string, contactId: string) => [...giftKeys.reminders(), userEmail, contactId] as const,
  suggestions: () => [...giftKeys.all, 'suggestions'] as const,
  suggestionsByBudget: (budget?: number) => [...giftKeys.suggestions(), budget] as const,
};

// Hook to get all gift history for user
export const useGiftHistoryQuery = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: giftKeys.userHistory(user?.email || ''),
    queryFn: () => giftService.getGiftHistory(user?.email || ''),
    enabled: !!user?.email,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get gift history for a specific contact
export const useContactGiftHistoryQuery = (contactId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: giftKeys.contactHistory(user?.email || '', contactId),
    queryFn: () => giftService.getContactGiftHistory(user?.email || '', contactId),
    enabled: !!user?.email && !!contactId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get all gift reminders for user
export const useGiftRemindersQuery = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: giftKeys.userReminders(user?.email || ''),
    queryFn: () => giftService.getGiftReminders(user?.email || ''),
    enabled: !!user?.email,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get gift reminder for a specific contact
export const useContactGiftReminderQuery = (contactId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: giftKeys.contactReminder(user?.email || '', contactId),
    queryFn: () => giftService.getContactGiftReminder(user?.email || '', contactId),
    enabled: !!user?.email && !!contactId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Hook to get gift suggestions
export const useGiftSuggestionsQuery = (budget?: number) => {
  return useQuery({
    queryKey: giftKeys.suggestionsByBudget(budget),
    queryFn: () => giftService.getGiftSuggestions(budget),
    staleTime: 1000 * 60 * 30, // 30 minutes - suggestions don't change often
  });
};

// Hook to add gift to history
export const useAddGiftToHistory = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (gift: Omit<GiftHistoryItem, 'id'>) =>
      giftService.addGiftToHistory(user?.email || '', gift),
    onSuccess: (newGift) => {
      // Update user's gift history cache
      queryClient.setQueryData<GiftHistoryItem[]>(
        giftKeys.userHistory(user?.email || ''),
        (oldHistory) => oldHistory ? [newGift, ...oldHistory] : [newGift]
      );

      // Update contact-specific gift history cache
      queryClient.setQueryData<GiftHistoryItem[]>(
        giftKeys.contactHistory(user?.email || '', newGift.recipientId),
        (oldHistory) => oldHistory ? [newGift, ...oldHistory] : [newGift]
      );
    },
    onError: (error) => {
      console.error('Failed to add gift to history:', error);
    },
  });
};

// Hook to create gift reminder
export const useCreateGiftReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reminder: Omit<GiftReminder, 'id'>) =>
      giftService.createGiftReminder(user?.email || '', reminder),
    onSuccess: (newReminder) => {
      // Update user's reminders cache
      queryClient.setQueryData<GiftReminder[]>(
        giftKeys.userReminders(user?.email || ''),
        (oldReminders) => oldReminders ? [newReminder, ...oldReminders] : [newReminder]
      );

      // Update contact-specific reminder cache
      queryClient.setQueryData<GiftReminder | null>(
        giftKeys.contactReminder(user?.email || '', newReminder.contactId),
        newReminder
      );
    },
    onError: (error) => {
      console.error('Failed to create gift reminder:', error);
    },
  });
};

// Hook to update gift reminder
export const useUpdateGiftReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ reminderId, updates }: { reminderId: string; updates: Partial<GiftReminder>; }) =>
      giftService.updateGiftReminder(user?.email || '', reminderId, updates),
    onSuccess: (updatedReminder) => {
      // Update user's reminders cache
      queryClient.setQueryData<GiftReminder[]>(
        giftKeys.userReminders(user?.email || ''),
        (oldReminders) =>
          oldReminders?.map(reminder =>
            reminder.id === updatedReminder.id ? updatedReminder : reminder
          ) || []
      );

      // Update contact-specific reminder cache
      queryClient.setQueryData<GiftReminder | null>(
        giftKeys.contactReminder(user?.email || '', updatedReminder.contactId),
        updatedReminder
      );
    },
    onError: (error) => {
      console.error('Failed to update gift reminder:', error);
    },
  });
};

// Hook to delete gift reminder
export const useDeleteGiftReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ reminderId, contactId }: { reminderId: string; contactId: string; }) =>
      giftService.deleteGiftReminder(user?.email || '', reminderId),
    onSuccess: (_, { reminderId, contactId }) => {
      // Update user's reminders cache
      queryClient.setQueryData<GiftReminder[]>(
        giftKeys.userReminders(user?.email || ''),
        (oldReminders) => oldReminders?.filter(reminder => reminder.id !== reminderId) || []
      );

      // Update contact-specific reminder cache
      queryClient.setQueryData<GiftReminder | null>(
        giftKeys.contactReminder(user?.email || '', contactId),
        null
      );
    },
    onError: (error) => {
      console.error('Failed to delete gift reminder:', error);
    },
  });
};

// Hook to prefetch gift data for a contact (useful for hover states)
export const usePrefetchContactGiftData = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return (contactId: string) => {
    // Prefetch contact gift history
    queryClient.prefetchQuery({
      queryKey: giftKeys.contactHistory(user?.email || '', contactId),
      queryFn: () => giftService.getContactGiftHistory(user?.email || '', contactId),
      staleTime: 1000 * 60 * 5,
    });

    // Prefetch contact gift reminder
    queryClient.prefetchQuery({
      queryKey: giftKeys.contactReminder(user?.email || '', contactId),
      queryFn: () => giftService.getContactGiftReminder(user?.email || '', contactId),
      staleTime: 1000 * 60 * 10,
    });
  };
};
