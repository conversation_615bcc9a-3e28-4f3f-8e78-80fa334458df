import { useState, useCallback } from 'react';

interface AsyncState<T> {
    data: T | null;
    loading: boolean;
    error: string | null;
}

interface UseAsyncErrorReturn<T> {
    state: AsyncState<T>;
    execute: (asyncFunction: () => Promise<T>) => Promise<T | null>;
    reset: () => void;
    setError: (error: string) => void;
    setLoading: (loading: boolean) => void;
}

export function useAsyncError<T = any>(): UseAsyncErrorReturn<T> {
    const [state, setState] = useState<AsyncState<T>>({
        data: null,
        loading: false,
        error: null,
    });

    const execute = useCallback(async (asyncFunction: () => Promise<T>): Promise<T | null> => {
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        try {
            const result = await asyncFunction();
            setState(prev => ({ ...prev, data: result, loading: false }));
            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            setState(prev => ({ ...prev, error: errorMessage, loading: false }));
            console.error('Async operation failed:', error);
            return null;
        }
    }, []);

    const reset = useCallback(() => {
        setState({ data: null, loading: false, error: null });
    }, []);

    const setError = useCallback((error: string) => {
        setState(prev => ({ ...prev, error, loading: false }));
    }, []);

    const setLoading = useCallback((loading: boolean) => {
        setState(prev => ({ ...prev, loading }));
    }, []);

    return {
        state,
        execute,
        reset,
        setError,
        setLoading,
    };
}

// Utility hook for simple async operations with error handling
export function useAsyncOperation() {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const executeAsync = useCallback(async <T>(
        operation: () => Promise<T>,
        onSuccess?: (result: T) => void,
        onError?: (error: string) => void
    ): Promise<T | null> => {
        setLoading(true);
        setError(null);

        try {
            const result = await operation();
            onSuccess?.(result);
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
            setError(errorMessage);
            onError?.(errorMessage);
            console.error('Operation failed:', err);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    const clearError = useCallback(() => setError(null), []);

    return {
        loading,
        error,
        executeAsync,
        clearError,
    };
}
