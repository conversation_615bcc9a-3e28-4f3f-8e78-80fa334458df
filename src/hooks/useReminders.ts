import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../context/AuthContext';
import { reminderService, type Reminder } from '../services/reminderService';

// Query keys for reminders
export const reminderKeys = {
  all: ['reminders'] as const,
  lists: () => [...reminderKeys.all, 'list'] as const,
  list: (userEmail: string) => [...reminderKeys.lists(), userEmail] as const,
  details: () => [...reminderKeys.all, 'detail'] as const,
  detail: (userEmail: string, reminderId: string) => [...reminderKeys.details(), userEmail, reminderId] as const,
};

// Hook to get all reminders
export const useRemindersQuery = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: reminderKeys.list(user?.email || ''),
    queryFn: () => reminderService.getReminders(user?.email || ''),
    enabled: !!user?.email,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get a single reminder
export const useReminderQuery = (reminderId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: reminderKeys.detail(user?.email || '', reminderId),
    queryFn: () => reminderService.getReminder(user?.email || '', reminderId),
    enabled: !!user?.email && !!reminderId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Hook to create a reminder
export const useCreateReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reminder: Omit<Reminder, 'id'>) =>
      reminderService.createReminder(user?.email || '', reminder),
    onSuccess: (newReminder) => {
      // Update reminders list cache
      queryClient.setQueryData<Reminder[]>(
        reminderKeys.list(user?.email || ''),
        (old) => old ? [...old, newReminder] : [newReminder]
      );
      
      // Invalidate reminders list to ensure consistency
      queryClient.invalidateQueries({
        queryKey: reminderKeys.lists()
      });
    },
    onError: (error) => {
      console.error('Failed to create reminder:', error);
    },
  });
};

// Hook to update a reminder
export const useUpdateReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ reminderId, updates }: { reminderId: string; updates: Partial<Reminder> }) =>
      reminderService.updateReminder(user?.email || '', reminderId, updates),
    onSuccess: (updatedReminder) => {
      // Update reminders list cache
      queryClient.setQueryData<Reminder[]>(
        reminderKeys.list(user?.email || ''),
        (old) => old ? old.map(r => r.id === updatedReminder.id ? updatedReminder : r) : [updatedReminder]
      );
      
      // Update individual reminder cache
      queryClient.setQueryData(
        reminderKeys.detail(user?.email || '', updatedReminder.id),
        updatedReminder
      );
    },
    onError: (error) => {
      console.error('Failed to update reminder:', error);
    },
  });
};

// Hook to delete a reminder
export const useDeleteReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reminderId: string) =>
      reminderService.deleteReminder(user?.email || '', reminderId),
    onSuccess: (_, reminderId) => {
      // Update reminders list cache
      queryClient.setQueryData<Reminder[]>(
        reminderKeys.list(user?.email || ''),
        (old) => old ? old.filter(r => r.id !== reminderId) : []
      );
      
      // Remove individual reminder cache
      queryClient.removeQueries({
        queryKey: reminderKeys.detail(user?.email || '', reminderId)
      });
    },
    onError: (error) => {
      console.error('Failed to delete reminder:', error);
    },
  });
};

// Hook to toggle reminder active status
export const useToggleReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reminderId: string) =>
      reminderService.toggleReminder(user?.email || '', reminderId),
    onSuccess: (updatedReminder) => {
      // Update reminders list cache
      queryClient.setQueryData<Reminder[]>(
        reminderKeys.list(user?.email || ''),
        (old) => old ? old.map(r => r.id === updatedReminder.id ? updatedReminder : r) : [updatedReminder]
      );
      
      // Update individual reminder cache
      queryClient.setQueryData(
        reminderKeys.detail(user?.email || '', updatedReminder.id),
        updatedReminder
      );
    },
    onError: (error) => {
      console.error('Failed to toggle reminder:', error);
    },
  });
};

// Hook to prefetch reminder data
export const usePrefetchReminder = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return (reminderId: string) => {
    queryClient.prefetchQuery({
      queryKey: reminderKeys.detail(user?.email || '', reminderId),
      queryFn: () => reminderService.getReminder(user?.email || '', reminderId),
      staleTime: 1000 * 60 * 10,
    });
  };
};
