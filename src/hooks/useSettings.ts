import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../context/AuthContext';
import { settingsService, type NotificationSettings } from '../services/settingsService';

// Query keys for settings
export const settingsKeys = {
  all: ['settings'] as const,
  notifications: () => [...settingsKeys.all, 'notifications'] as const,
  notification: (userEmail: string) => [...settingsKeys.notifications(), userEmail] as const,
};

// Hook to get notification settings
export const useNotificationSettingsQuery = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: settingsKeys.notification(user?.email || ''),
    queryFn: () => settingsService.getNotificationSettings(user?.email || ''),
    enabled: !!user?.email,
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
};

// Hook to update notification settings
export const useUpdateNotificationSettings = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: Partial<NotificationSettings>) =>
      settingsService.updateNotificationSettings(user?.email || '', settings),
    onSuccess: (updatedSettings) => {
      // Update settings cache
      queryClient.setQueryData(
        settingsKeys.notification(user?.email || ''),
        updatedSettings
      );
    },
    onError: (error) => {
      console.error('Failed to update notification settings:', error);
    },
  });
};

// Hook to update a specific setting
export const useUpdateSetting = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: <K extends keyof NotificationSettings>({
      key,
      value,
    }: {
      key: K;
      value: NotificationSettings[K];
    }) => settingsService.updateSetting(user?.email || '', key, value),
    onSuccess: (updatedSettings) => {
      // Update settings cache
      queryClient.setQueryData(
        settingsKeys.notification(user?.email || ''),
        updatedSettings
      );
    },
    onError: (error) => {
      console.error('Failed to update setting:', error);
    },
  });
};

// Hook to reset settings to default
export const useResetNotificationSettings = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => settingsService.resetNotificationSettings(user?.email || ''),
    onSuccess: (defaultSettings) => {
      // Update settings cache
      queryClient.setQueryData(
        settingsKeys.notification(user?.email || ''),
        defaultSettings
      );
    },
    onError: (error) => {
      console.error('Failed to reset notification settings:', error);
    },
  });
};

// Hook to bulk update settings
export const useBulkUpdateSettings = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: Partial<NotificationSettings>) =>
      settingsService.bulkUpdateSettings(user?.email || '', updates),
    onSuccess: (updatedSettings) => {
      // Update settings cache
      queryClient.setQueryData(
        settingsKeys.notification(user?.email || ''),
        updatedSettings
      );
    },
    onError: (error) => {
      console.error('Failed to bulk update settings:', error);
    },
  });
};

// Hook to export settings
export const useExportSettings = () => {
  const { user } = useAuth();

  return useMutation({
    mutationFn: () => settingsService.exportSettings(user?.email || ''),
    onSuccess: (settingsJson) => {
      // Create and download file
      const blob = new Blob([settingsJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `birthday-saas-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },
    onError: (error) => {
      console.error('Failed to export settings:', error);
    },
  });
};

// Hook to import settings
export const useImportSettings = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settingsJson: string) =>
      settingsService.importSettings(user?.email || '', settingsJson),
    onSuccess: (importedSettings) => {
      // Update settings cache
      queryClient.setQueryData(
        settingsKeys.notification(user?.email || ''),
        importedSettings
      );
    },
    onError: (error) => {
      console.error('Failed to import settings:', error);
    },
  });
};

// Hook to prefetch settings
export const usePrefetchNotificationSettings = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: settingsKeys.notification(user?.email || ''),
      queryFn: () => settingsService.getNotificationSettings(user?.email || ''),
      staleTime: 1000 * 60 * 10,
    });
  };
};

// Hook for optimistic setting updates
export const useOptimisticSetting = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return <K extends keyof NotificationSettings>(
    key: K,
    value: NotificationSettings[K]
  ) => {
    // Optimistically update the cache
    queryClient.setQueryData<NotificationSettings>(
      settingsKeys.notification(user?.email || ''),
      (old) => old ? { ...old, [key]: value } : undefined
    );
  };
};
