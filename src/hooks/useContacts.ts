import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { contactsService } from '../services/contactsService';
import type { Contact } from '../types';
import { useAuth } from '../context/AuthContext';
import { useEffect } from 'react';

// Query keys for contacts
export const contactsKeys = {
  all: ['contacts'] as const,
  lists: () => [...contactsKeys.all, 'list'] as const,
  list: (userEmail: string) => [...contactsKeys.lists(), userEmail] as const,
  details: () => [...contactsKeys.all, 'detail'] as const,
  detail: (userEmail: string, id: string) => [...contactsKeys.details(), userEmail, id] as const,
  search: (userEmail: string, query: string) => [...contactsKeys.all, 'search', userEmail, query] as const,
};

// Hook to get all contacts
export const useContactsQuery = () => {
  const { user } = useAuth();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useQuery({
    queryKey: contactsKeys.list(user?.email || ''),
    queryFn: () => contactsService.getContacts(),
    enabled: !!user?.email,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get a single contact
export const useContactQuery = (contactId: string) => {
  const { user } = useAuth();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useQuery({
    queryKey: contactsKeys.detail(user?.email || '', contactId),
    queryFn: () => contactsService.getContactById(contactId),
    enabled: !!user?.email && !!contactId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Hook to search contacts
export const useContactsSearch = (query: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: contactsKeys.search(user?.email || '', query),
    queryFn: () => contactsService.searchContacts(user?.email || '', query),
    enabled: !!user?.email && query.length > 0,
    staleTime: 1000 * 30, // 30 seconds
  });
};

// Hook to create a contact
export const useCreateContact = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useMutation({
    mutationFn: (contact: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>) =>
      contactsService.createContact(contact),
    onSuccess: (newContact) => {
      // Invalidate and refetch contacts list
      queryClient.invalidateQueries({ queryKey: contactsKeys.list(user?.email || '') });

      // Optimistically update the cache
      queryClient.setQueryData<Contact[]>(
        contactsKeys.list(user?.email || ''),
        (oldContacts) => oldContacts ? [...oldContacts, newContact] : [newContact]
      );
    },
    onError: (error) => {
      console.error('Failed to create contact:', error);
    },
  });
};

// Hook to create multiple contacts
export const useCreateContacts = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useMutation({
    mutationFn: (contacts: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>[]) =>
      contactsService.createContacts(contacts),
    onSuccess: (newContacts) => {
      // Invalidate and refetch contacts list
      queryClient.invalidateQueries({ queryKey: contactsKeys.list(user?.email || '') });

      // Optimistically update the cache
      queryClient.setQueryData<Contact[]>(
        contactsKeys.list(user?.email || ''),
        (oldContacts) => oldContacts ? [...oldContacts, ...newContacts] : newContacts
      );
    },
    onError: (error) => {
      console.error('Failed to create contacts:', error);
    },
  });
};

// Hook to update a contact
export const useUpdateContact = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useMutation({
    mutationFn: ({ contactId, updates }: { contactId: string; updates: Partial<Contact>; }) =>
      contactsService.updateContact(contactId, updates),
    onSuccess: (updatedContact) => {
      // Update the specific contact in cache
      queryClient.setQueryData<Contact | null>(
        contactsKeys.detail(user?.email || '', updatedContact.id),
        updatedContact
      );

      // Update the contact in the list cache
      queryClient.setQueryData<Contact[]>(
        contactsKeys.list(user?.email || ''),
        (oldContacts) =>
          oldContacts?.map(contact =>
            contact.id === updatedContact.id ? updatedContact : contact
          ) || []
      );
    },
    onError: (error) => {
      console.error('Failed to update contact:', error);
    },
  });
};

// Hook to delete a contact
export const useDeleteContact = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Set current user in contacts service
  useEffect(() => {
    if (user?.email) {
      contactsService.setCurrentUser(user.email);
    }
  }, [user?.email]);

  return useMutation({
    mutationFn: (contactId: string) =>
      contactsService.deleteContact(contactId),
    onSuccess: (_, contactId) => {
      // Remove the contact from cache
      queryClient.removeQueries({ queryKey: contactsKeys.detail(user?.email || '', contactId) });

      // Update the list cache
      queryClient.setQueryData<Contact[]>(
        contactsKeys.list(user?.email || ''),
        (oldContacts) => oldContacts?.filter(contact => contact.id !== contactId) || []
      );
    },
    onError: (error) => {
      console.error('Failed to delete contact:', error);
    },
  });
};

// Hook to prefetch a contact (useful for hover states)
export const usePrefetchContact = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return (contactId: string) => {
    queryClient.prefetchQuery({
      queryKey: contactsKeys.detail(user?.email || '', contactId),
      queryFn: () => contactsService.getContact(user?.email || '', contactId),
      staleTime: 1000 * 60 * 10, // 10 minutes
    });
  };
};
