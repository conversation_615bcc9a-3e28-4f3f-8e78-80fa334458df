import { describe, it, expect, beforeEach, vi } from 'vitest';
import { messagingService } from '../services/messagingService';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Anniversary Templates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Default Anniversary Templates', () => {
    it('should include anniversary templates in default templates', async () => {
      const templates = await messagingService.getTemplates();
      
      // Filter anniversary templates
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      
      expect(anniversaryTemplates.length).toBe(4);
      
      // Check specific anniversary templates exist
      const templateNames = anniversaryTemplates.map(t => t.name);
      expect(templateNames).toContain('Wedding Anniversary - General');
      expect(templateNames).toContain('Wedding Anniversary - Milestone');
      expect(templateNames).toContain('Dating Anniversary');
      expect(templateNames).toContain('Business Anniversary');
    });

    it('should have correct structure for anniversary templates', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      
      anniversaryTemplates.forEach(template => {
        expect(template).toHaveProperty('id');
        expect(template).toHaveProperty('name');
        expect(template).toHaveProperty('type', 'anniversary');
        expect(template).toHaveProperty('subject');
        expect(template).toHaveProperty('message');
        expect(template).toHaveProperty('variables');
        expect(template).toHaveProperty('isActive');
        expect(template).toHaveProperty('createdAt');
        expect(template).toHaveProperty('updatedAt');
      });
    });

    it('should have proper variables for anniversary templates', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      
      const weddingGeneral = anniversaryTemplates.find(t => t.name === 'Wedding Anniversary - General');
      expect(weddingGeneral?.variables).toContain('name');
      expect(weddingGeneral?.variables).toContain('partnerName');
      expect(weddingGeneral?.variables).toContain('years');
      expect(weddingGeneral?.variables).toContain('senderName');
      
      const businessAnniversary = anniversaryTemplates.find(t => t.name === 'Business Anniversary');
      expect(businessAnniversary?.variables).toContain('name');
      expect(businessAnniversary?.variables).toContain('years');
      expect(businessAnniversary?.variables).toContain('senderName');
      expect(businessAnniversary?.variables).not.toContain('partnerName');
    });

    it('should have anniversary-specific content', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      
      const weddingGeneral = anniversaryTemplates.find(t => t.name === 'Wedding Anniversary - General');
      expect(weddingGeneral?.subject).toContain('Anniversary');
      expect(weddingGeneral?.message).toContain('anniversary');
      expect(weddingGeneral?.message).toContain('{name}');
      expect(weddingGeneral?.message).toContain('{partnerName}');
      expect(weddingGeneral?.message).toContain('{years}');
      
      const businessAnniversary = anniversaryTemplates.find(t => t.name === 'Business Anniversary');
      expect(businessAnniversary?.subject).toContain('Business');
      expect(businessAnniversary?.message).toContain('business');
      expect(businessAnniversary?.message).toContain('{years}');
    });

    it('should be active by default', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      
      anniversaryTemplates.forEach(template => {
        expect(template.isActive).toBe(true);
      });
    });
  });

  describe('Anniversary Template Management', () => {
    it('should be able to create new anniversary template', async () => {
      const newTemplate = {
        name: 'Custom Anniversary',
        type: 'anniversary' as const,
        subject: 'Happy {years}th Anniversary!',
        message: 'Congratulations on {years} years together, {name}!',
        variables: ['name', 'years'],
        isActive: true,
      };

      const created = await messagingService.createTemplate(newTemplate);
      
      expect(created.id).toBeDefined();
      expect(created.name).toBe(newTemplate.name);
      expect(created.type).toBe('anniversary');
      expect(created.subject).toBe(newTemplate.subject);
      expect(created.message).toBe(newTemplate.message);
    });

    it('should be able to update anniversary template', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplate = templates.find(t => t.type === 'anniversary');
      
      if (anniversaryTemplate) {
        const updated = await messagingService.updateTemplate(anniversaryTemplate.id, {
          subject: 'Updated Anniversary Subject'
        });
        
        expect(updated.subject).toBe('Updated Anniversary Subject');
        expect(updated.id).toBe(anniversaryTemplate.id);
      }
    });

    it('should be able to toggle anniversary template active status', async () => {
      const templates = await messagingService.getTemplates();
      const anniversaryTemplate = templates.find(t => t.type === 'anniversary');
      
      if (anniversaryTemplate) {
        const originalStatus = anniversaryTemplate.isActive;
        
        const updated = await messagingService.updateTemplate(anniversaryTemplate.id, {
          isActive: !originalStatus
        });
        
        expect(updated.isActive).toBe(!originalStatus);
      }
    });

    it('should be able to delete anniversary template', async () => {
      // Create a test template first
      const newTemplate = {
        name: 'Test Anniversary Template',
        type: 'anniversary' as const,
        subject: 'Test Subject',
        message: 'Test message',
        variables: ['name'],
        isActive: true,
      };

      const created = await messagingService.createTemplate(newTemplate);
      
      // Delete it
      await messagingService.deleteTemplate(created.id);
      
      // Verify it's gone
      const templates = await messagingService.getTemplates();
      const deletedTemplate = templates.find(t => t.id === created.id);
      expect(deletedTemplate).toBeUndefined();
    });
  });

  describe('Anniversary Template Filtering', () => {
    it('should filter anniversary templates correctly', async () => {
      const allTemplates = await messagingService.getTemplates();
      const anniversaryTemplates = allTemplates.filter(t => t.type === 'anniversary');
      const birthdayTemplates = allTemplates.filter(t => t.type === 'birthday');
      
      expect(anniversaryTemplates.length).toBeGreaterThan(0);
      expect(birthdayTemplates.length).toBeGreaterThan(0);
      
      // Ensure no overlap
      anniversaryTemplates.forEach(template => {
        expect(template.type).toBe('anniversary');
      });
      
      birthdayTemplates.forEach(template => {
        expect(template.type).toBe('birthday');
      });
    });
  });
});
