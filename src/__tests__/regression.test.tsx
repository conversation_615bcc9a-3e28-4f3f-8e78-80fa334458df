import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import ContactsPage from '../components/ContactsPage';
import AddContactsPage from '../components/AddContactsPage';
import Dashboard from '../components/Dashboard';
import MainLayout from '../components/MainLayout';

// Mock all the hooks to ensure existing functionality still works
vi.mock('../hooks/useContacts', () => ({
  useContactsQuery: () => ({
    data: [
      {
        id: '1',
        name: '<PERSON>',
        birthday: '1990-06-15',
        category: 'Friends',
      },
      {
        id: '2',
        name: '<PERSON>',
        birthday: '1985-12-25',
        category: 'Family',
      },
    ],
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
  useCreateContact: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useCreateContacts: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useUpdateContact: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useDeleteContact: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
}));

vi.mock('../hooks/useGifts', () => ({
  useContactGiftHistoryQuery: () => ({
    data: [],
    isLoading: false,
  }),
  useContactGiftReminderQuery: () => ({
    data: [],
    isLoading: false,
  }),
  useGiftSuggestionsQuery: () => ({
    data: [],
    isLoading: false,
  }),
  useAddGiftToHistory: () => ({
    mutateAsync: vi.fn(),
  }),
  useCreateGiftReminder: () => ({
    mutateAsync: vi.fn(),
  }),
  useDeleteGiftReminder: () => ({
    mutateAsync: vi.fn(),
  }),
}));

vi.mock('../context/AuthContext', () => ({
  useAuth: () => ({
    user: { email: '<EMAIL>' },
    isAuthenticated: true,
  }),
}));

vi.mock('../context/SubscriptionContext', () => ({
  useSubscription: () => ({
    plan: 'premium',
    isTrialActive: false,
    trialDaysLeft: 0,
  }),
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
};

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Regression Tests - Existing Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ContactsPage - Core Functionality', () => {
    it('should render contacts list without breaking', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      expect(screen.getByText('Contacts')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('should handle contact search functionality', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      const searchInput = screen.getByPlaceholderText(/search contacts/i);
      expect(searchInput).toBeInTheDocument();
      
      fireEvent.change(searchInput, { target: { value: 'John' } });
      // Should filter contacts (implementation may vary)
    });

    it('should handle contact filtering by category', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Should have filter options
      expect(screen.getByText('All') || screen.getByDisplayValue('All')).toBeInTheDocument();
    });

    it('should handle pagination', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Should show pagination controls if there are enough contacts
      // This test ensures pagination still works after React Query migration
    });

    it('should handle Add Contacts navigation', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      const addButton = screen.getByText('Add Contacts');
      fireEvent.click(addButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('add-contacts');
    });

    it('should handle refresh functionality', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      const refreshButton = screen.getByText('Refresh');
      expect(refreshButton).toBeInTheDocument();
      
      fireEvent.click(refreshButton);
      // Should call refetch function
    });
  });

  describe('AddContactsPage - Core Functionality', () => {
    it('should render add contacts form', () => {
      renderWithQueryClient(<AddContactsPage />);
      
      expect(screen.getByText('Add Multiple Contacts')).toBeInTheDocument();
      expect(screen.getByText('Add Contact')).toBeInTheDocument();
    });

    it('should handle adding new contact rows', () => {
      renderWithQueryClient(<AddContactsPage />);
      
      const addButton = screen.getByText('+ Add Another Contact');
      fireEvent.click(addButton);
      
      // Should add new contact form row
      const nameInputs = screen.getAllByLabelText(/name/i);
      expect(nameInputs.length).toBeGreaterThan(1);
    });

    it('should handle form submission', async () => {
      renderWithQueryClient(<AddContactsPage />);
      
      // Fill in contact form
      const nameInput = screen.getByLabelText(/name/i);
      const birthdayInput = screen.getByLabelText(/birthday/i);
      
      fireEvent.change(nameInput, { target: { value: 'Test Contact' } });
      fireEvent.change(birthdayInput, { target: { value: '1990-01-01' } });
      
      const submitButton = screen.getByText('Add Contacts');
      fireEvent.click(submitButton);
      
      // Should handle form submission
      await waitFor(() => {
        // Form should be processed
      });
    });

    it('should handle form validation', () => {
      renderWithQueryClient(<AddContactsPage />);
      
      const submitButton = screen.getByText('Add Contacts');
      fireEvent.click(submitButton);
      
      // Should show validation errors for empty required fields
    });
  });

  describe('Dashboard - Core Functionality', () => {
    it('should render dashboard with navigation', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<Dashboard onNavigate={mockNavigate} />);
      
      expect(screen.getByText('Welcome back! 🎉')).toBeInTheDocument();
      expect(screen.getByText('+ Add Contact')).toBeInTheDocument();
    });

    it('should handle Add Contact button click', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<Dashboard onNavigate={mockNavigate} />);
      
      const addButton = screen.getByText('+ Add Contact');
      fireEvent.click(addButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('add-contacts');
    });

    it('should display contact statistics', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<Dashboard onNavigate={mockNavigate} />);
      
      // Should show various stats about contacts and birthdays
      // The exact numbers may be hardcoded in the component
    });

    it('should handle loading and error states', () => {
      // Test with loading state
      vi.mocked(require('../hooks/useContacts').useContactsQuery).mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
      });

      const mockNavigate = vi.fn();
      renderWithQueryClient(<Dashboard onNavigate={mockNavigate} />);
      
      // Should show loading state
      expect(screen.getByText('Loading dashboard...')).toBeInTheDocument();
    });
  });

  describe('React Query Integration - No Breaking Changes', () => {
    it('should maintain the same component interfaces', () => {
      // Test that components still accept the same props
      const mockNavigate = vi.fn();
      
      expect(() => {
        renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      }).not.toThrow();
      
      expect(() => {
        renderWithQueryClient(<AddContactsPage />);
      }).not.toThrow();
      
      expect(() => {
        renderWithQueryClient(<Dashboard onNavigate={mockNavigate} />);
      }).not.toThrow();
    });

    it('should handle data fetching with React Query', () => {
      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Should display data from React Query hooks
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('should handle mutations with React Query', async () => {
      renderWithQueryClient(<AddContactsPage />);
      
      // Fill and submit form
      const nameInput = screen.getByLabelText(/name/i);
      fireEvent.change(nameInput, { target: { value: 'New Contact' } });
      
      const submitButton = screen.getByText('Add Contacts');
      fireEvent.click(submitButton);
      
      // Should use React Query mutations
      await waitFor(() => {
        // Mutation should be called
      });
    });
  });

  describe('Navigation and Routing', () => {
    it('should maintain navigation between pages', () => {
      // Test that navigation still works after React Query integration
      const mockNavigate = vi.fn();
      
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      const addButton = screen.getByText('Add Contacts');
      fireEvent.click(addButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('add-contacts');
    });
  });

  describe('Performance and Memory', () => {
    it('should not cause memory leaks', () => {
      // Test that components can be mounted and unmounted without issues
      const mockNavigate = vi.fn();
      
      const { unmount } = renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      expect(() => {
        unmount();
      }).not.toThrow();
    });

    it('should handle rapid re-renders', () => {
      const mockNavigate = vi.fn();
      
      const { rerender } = renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Rapid re-renders should not cause issues
      for (let i = 0; i < 5; i++) {
        rerender(
          <QueryClientProvider client={createTestQueryClient()}>
            <ContactsPage onNavigate={mockNavigate} />
          </QueryClientProvider>
        );
      }
      
      expect(screen.getByText('Contacts')).toBeInTheDocument();
    });
  });

  describe('Error Boundaries and Resilience', () => {
    it('should handle API errors gracefully', () => {
      // Mock error state
      vi.mocked(require('../hooks/useContacts').useContactsQuery).mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('API Error'),
      });

      const mockNavigate = vi.fn();
      renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Should show error state without crashing
      expect(screen.getByText('Failed to load contacts') || screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('should recover from errors', () => {
      // Test error recovery mechanisms
      const mockNavigate = vi.fn();
      
      // Start with error state
      vi.mocked(require('../hooks/useContacts').useContactsQuery).mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('API Error'),
      });

      const { rerender } = renderWithQueryClient(<ContactsPage onNavigate={mockNavigate} />);
      
      // Then recover with data
      vi.mocked(require('../hooks/useContacts').useContactsQuery).mockReturnValue({
        data: [{ id: '1', name: 'John Doe', birthday: '1990-06-15', category: 'Friends' }],
        isLoading: false,
        error: null,
      });

      rerender(
        <QueryClientProvider client={createTestQueryClient()}>
          <ContactsPage onNavigate={mockNavigate} />
        </QueryClientProvider>
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});
