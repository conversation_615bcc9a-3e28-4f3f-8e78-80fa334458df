import { describe, it, expect, beforeEach, vi } from 'vitest';
import { messagingService, LANDMARK_BIRTHDAYS } from '../services/messagingService';
import type { Contact } from '../types';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock contacts data
const mockContacts: Contact[] = [
  {
    id: '1',
    name: '<PERSON>',
    birthday: '1990-06-15',
    category: 'Friends',
  },
  {
    id: '2',
    name: '<PERSON>',
    birthday: '1985-12-25',
    category: 'Family',
  },
  {
    id: '3',
    name: '<PERSON>',
    birthday: '2003-03-10', // Will be 21 this year
    category: 'Friends',
  },
];

describe('MessagingService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Template Management', () => {
    it('should create a new template', async () => {
      const templateData = {
        name: 'Test Template',
        type: 'birthday' as const,
        subject: 'Happy Birthday {name}!',
        message: 'Dear {name}, Happy {age}th birthday!',
        variables: ['name', 'age'],
        isActive: true,
      };

      const template = await messagingService.createTemplate(templateData);

      expect(template).toMatchObject(templateData);
      expect(template.id).toBeDefined();
      expect(template.createdAt).toBeDefined();
      expect(template.updatedAt).toBeDefined();
    });

    it('should get all templates', async () => {
      const templates = await messagingService.getTemplates();

      expect(Array.isArray(templates)).toBe(true);
      expect(templates.length).toBeGreaterThan(0);

      // Should include default templates
      const birthdayTemplate = templates.find(t => t.type === 'birthday');
      const landmarkTemplate = templates.find(t => t.type === 'landmark');
      const anniversaryTemplates = templates.filter(t => t.type === 'anniversary');
      const weeklyTemplate = templates.find(t => t.type === 'weekly_admin');

      expect(birthdayTemplate).toBeDefined();
      expect(landmarkTemplate).toBeDefined();
      expect(anniversaryTemplates.length).toBe(4); // Should have 4 anniversary templates
      expect(weeklyTemplate).toBeDefined();
    });

    it('should update a template', async () => {
      const templates = await messagingService.getTemplates();
      const template = templates[0];

      const updates = {
        name: 'Updated Template Name',
        isActive: false,
      };

      const updatedTemplate = await messagingService.updateTemplate(template.id, updates);

      expect(updatedTemplate.name).toBe(updates.name);
      expect(updatedTemplate.isActive).toBe(updates.isActive);
      expect(updatedTemplate.updatedAt).not.toBe(template.updatedAt);
    });

    it('should delete a template', async () => {
      const templateData = {
        name: 'Template to Delete',
        type: 'birthday' as const,
        subject: 'Test',
        message: 'Test content',
        variables: [],
        isActive: true,
      };

      const template = await messagingService.createTemplate(templateData);
      await messagingService.deleteTemplate(template.id);

      const templates = await messagingService.getTemplates();
      const deletedTemplate = templates.find(t => t.id === template.id);

      expect(deletedTemplate).toBeUndefined();
    });
  });

  describe('Landmark Birthday Detection', () => {
    it('should detect landmark birthdays correctly', () => {
      const landmarkAges = [18, 21, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120];

      landmarkAges.forEach(age => {
        expect(messagingService.isLandmarkBirthday(age)).toBe(true);
      });

      const nonLandmarkAges = [19, 22, 35, 45, 55];
      nonLandmarkAges.forEach(age => {
        expect(messagingService.isLandmarkBirthday(age)).toBe(false);
      });
    });

    it('should return correct landmark information', () => {
      const landmark21 = messagingService.getLandmarkBirthday(21);
      expect(landmark21).toMatchObject({
        age: 21,
        name: 'Twenty-First',
        description: 'Traditional coming of age',
        isSpecial: true,
      });

      const landmark50 = messagingService.getLandmarkBirthday(50);
      expect(landmark50).toMatchObject({
        age: 50,
        name: 'Nifty Fifty',
        description: 'Half-century milestone',
        isSpecial: true,
      });

      const nonLandmark = messagingService.getLandmarkBirthday(35);
      expect(nonLandmark).toBeNull();
    });

    it('should have all expected landmark ages defined', () => {
      const expectedAges = [18, 21, 25, 30, 40, 50, 60, 65, 70, 75, 80, 90, 100, 110, 120];

      expectedAges.forEach(age => {
        const landmark = LANDMARK_BIRTHDAYS.find(l => l.age === age);
        expect(landmark).toBeDefined();
        expect(landmark?.name).toBeDefined();
        expect(landmark?.description).toBeDefined();
      });
    });
  });

  describe('Message Scheduling', () => {
    it('should schedule a birthday message', async () => {
      const scheduledFor = new Date('2024-06-15T06:00:00Z');
      const variables = { name: 'John', age: '34' };

      const message = await messagingService.scheduleMessage(
        '1',
        'template_1',
        scheduledFor,
        'birthday',
        variables
      );

      expect(message).toMatchObject({
        contactId: '1',
        templateId: 'template_1',
        type: 'birthday',
        status: 'pending',
        variables,
        retryCount: 0,
      });
      expect(message.id).toBeDefined();
      expect(message.scheduledFor).toBe(scheduledFor.toISOString());
    });

    it('should get scheduled messages', async () => {
      const scheduledFor = new Date('2024-06-15T06:00:00Z');

      await messagingService.scheduleMessage(
        '1',
        'template_1',
        scheduledFor,
        'birthday',
        { name: 'John', age: '34' }
      );

      const messages = await messagingService.getScheduledMessages();

      expect(Array.isArray(messages)).toBe(true);
      expect(messages.length).toBeGreaterThan(0);

      const scheduledMessage = messages.find(m => m.contactId === '1');
      expect(scheduledMessage).toBeDefined();
    });
  });

  describe('Weekly Admin Notifications', () => {
    it('should generate weekly admin notification', async () => {
      const notification = await messagingService.generateWeeklyAdminNotification(mockContacts);

      expect(notification).toMatchObject({
        status: 'sent',
      });
      expect(notification.id).toBeDefined();
      expect(notification.weekStartDate).toBeDefined();
      expect(notification.weekEndDate).toBeDefined();
      expect(notification.sentAt).toBeDefined();
      expect(Array.isArray(notification.upcomingBirthdays)).toBe(true);
    });

    it('should identify landmark birthdays in weekly notification', async () => {
      // Mock a contact who will have a landmark birthday
      const contactsWithLandmark: Contact[] = [
        {
          id: '3',
          name: 'Young Person',
          birthday: new Date().toISOString().split('T')[0].replace(/\d{4}/, '2003'), // Will be 21
          category: 'Friends',
        },
      ];

      const notification = await messagingService.generateWeeklyAdminNotification(contactsWithLandmark);

      // Check if any upcoming birthdays are marked as landmarks
      const landmarkBirthdays = notification.upcomingBirthdays.filter(b => b.isLandmark);

      if (landmarkBirthdays.length > 0) {
        expect(landmarkBirthdays[0].landmarkType).toBeDefined();
      }
    });
  });

  describe('Settings Management', () => {
    it('should get default settings', async () => {
      const settings = await messagingService.getSettings();

      expect(settings).toMatchObject({
        weeklyAdminNotifications: {
          enabled: true,
          dayOfWeek: 1,
          time: '09:00',
          email: '<EMAIL>',
        },
        birthdayMessages: {
          enabled: true,
          defaultTime: '06:00',
          timezone: 'UTC',
        },
        landmarkBirthdays: {
          enabled: true,
          customMessage: true,
        },
      });
    });

    it('should update settings', async () => {
      const updates = {
        birthdayMessages: {
          enabled: false,
          defaultTime: '08:00',
          timezone: 'America/New_York',
        },
      };

      const updatedSettings = await messagingService.updateSettings(updates);

      expect(updatedSettings.birthdayMessages).toMatchObject(updates.birthdayMessages);
    });
  });

  describe('Message History and Statistics', () => {
    it('should get message history', async () => {
      const history = await messagingService.getMessageHistory();

      expect(Array.isArray(history)).toBe(true);
    });

    it('should get messaging statistics', async () => {
      const stats = await messagingService.getMessagingStats();

      expect(stats).toMatchObject({
        totalMessagesSent: expect.any(Number),
        birthdayMessagesSent: expect.any(Number),
        landmarkMessagesSent: expect.any(Number),
        weeklyNotificationsSent: expect.any(Number),
        failedMessages: expect.any(Number),
        successRate: expect.any(Number),
        lastWeekStats: {
          sent: expect.any(Number),
          failed: expect.any(Number),
        },
      });

      expect(stats.successRate).toBeGreaterThanOrEqual(0);
      expect(stats.successRate).toBeLessThanOrEqual(100);
    });
  });

  describe('Birthday Message Processing', () => {
    it('should process birthday messages for today', async () => {
      // Mock a contact with today's birthday
      const today = new Date();
      const todayBirthday = `${today.getFullYear() - 30}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

      const contactsWithTodayBirthday: Contact[] = [
        {
          id: 'today-birthday',
          name: 'Birthday Person',
          birthday: todayBirthday,
          category: 'Friends',
        },
      ];

      await messagingService.processBirthdayMessages(contactsWithTodayBirthday);

      const scheduledMessages = await messagingService.getScheduledMessages();
      const todayMessages = scheduledMessages.filter(m =>
        m.contactId === 'today-birthday' &&
        m.type === 'birthday'
      );

      expect(todayMessages.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle template not found error', async () => {
      await expect(
        messagingService.updateTemplate('non-existent-id', { name: 'Updated' })
      ).rejects.toThrow('Template not found');
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      // Should not throw when localStorage fails
      expect(() => {
        new (messagingService.constructor as any)();
      }).not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete birthday workflow', async () => {
      // 1. Create a birthday template
      const template = await messagingService.createTemplate({
        name: 'Integration Test Template',
        type: 'birthday',
        subject: 'Happy Birthday {name}!',
        message: 'Dear {name}, Happy {age}th birthday!',
        variables: ['name', 'age'],
        isActive: true,
      });

      // 2. Process birthday messages
      await messagingService.processBirthdayMessages(mockContacts);

      // 3. Check scheduled messages
      const scheduledMessages = await messagingService.getScheduledMessages();
      expect(scheduledMessages.length).toBeGreaterThanOrEqual(0);

      // 4. Generate weekly notification
      const weeklyNotification = await messagingService.generateWeeklyAdminNotification(mockContacts);
      expect(weeklyNotification.status).toBe('sent');

      // 5. Check statistics
      const stats = await messagingService.getMessagingStats();
      expect(stats.weeklyNotificationsSent).toBeGreaterThan(0);
    });

    it('should handle landmark birthday workflow', async () => {
      // Create a contact with a landmark birthday
      const landmarkContact: Contact = {
        id: 'landmark-test',
        name: 'Landmark Person',
        birthday: '1974-01-01', // Will be 50
        category: 'Family',
      };

      // Check if it's detected as landmark
      const age = new Date().getFullYear() - 1974;
      const isLandmark = messagingService.isLandmarkBirthday(age);
      const landmark = messagingService.getLandmarkBirthday(age);

      if (isLandmark && landmark) {
        expect(landmark.name).toBeDefined();
        expect(landmark.description).toBeDefined();
      }

      // Process messages for landmark birthday
      await messagingService.processBirthdayMessages([landmarkContact]);

      const scheduledMessages = await messagingService.getScheduledMessages();
      const landmarkMessages = scheduledMessages.filter(m =>
        m.contactId === 'landmark-test' && m.type === 'landmark'
      );

      // Should schedule landmark message if it's a landmark birthday
      if (isLandmark) {
        expect(landmarkMessages.length).toBeGreaterThan(0);
      }
    });
  });
});
