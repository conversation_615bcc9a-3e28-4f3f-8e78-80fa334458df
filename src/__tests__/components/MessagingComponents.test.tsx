import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import MessagingDashboard from '../../components/MessagingDashboard';
import MessageTemplateManager from '../../components/MessageTemplateManager';
import LandmarkBirthdayDetector from '../../components/LandmarkBirthdayDetector';
import WeeklyNotificationSystem from '../../components/WeeklyNotificationSystem';

// Mock the messaging hooks
vi.mock('../../hooks/useMessaging', () => ({
  useTemplatesQuery: () => ({
    data: [
      {
        id: '1',
        name: 'Birthday Template',
        type: 'birthday',
        subject: 'Happy Birthday {name}!',
        content: 'Dear {name}, Happy {age}th birthday!',
        variables: ['name', 'age'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    ],
    isLoading: false,
    error: null,
  }),
  useCreateTemplate: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useUpdateTemplate: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useDeleteTemplate: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useMessagingStatsQuery: () => ({
    data: {
      totalMessagesSent: 150,
      birthdayMessagesSent: 120,
      landmarkMessagesSent: 20,
      weeklyNotificationsSent: 10,
      failedMessages: 5,
      successRate: 96.7,
      lastWeekStats: { sent: 25, failed: 1 },
    },
  }),
  useMessageHistoryQuery: () => ({
    data: [
      {
        id: '1',
        contactId: '1',
        templateId: '1',
        type: 'birthday',
        subject: 'Happy Birthday John!',
        content: 'Dear John, Happy 30th birthday!',
        sentAt: '2024-01-15T06:00:00Z',
        status: 'sent',
        recipient: '<EMAIL>',
      },
    ],
  }),
  useScheduledMessagesQuery: () => ({
    data: [
      {
        id: '1',
        contactId: '1',
        templateId: '1',
        scheduledFor: '2024-01-20T06:00:00Z',
        type: 'birthday',
        status: 'pending',
        variables: { name: 'Jane', age: '25' },
        retryCount: 0,
      },
    ],
    isLoading: false,
  }),
  useSettingsQuery: () => ({
    data: {
      id: 'settings',
      userId: 'user1',
      weeklyAdminNotifications: {
        enabled: true,
        dayOfWeek: 1,
        time: '09:00',
        email: '<EMAIL>',
      },
      birthdayMessages: {
        enabled: true,
        defaultTime: '06:00',
        timezone: 'UTC',
      },
      landmarkBirthdays: {
        enabled: true,
        customMessage: true,
      },
    },
  }),
  useUpdateSettings: () => ({
    mutateAsync: vi.fn(),
  }),
  useGenerateWeeklyNotification: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useLandmarkBirthday: (age: number) => ({
    isLandmark: [18, 21, 30, 40, 50].includes(age),
    landmark: age === 21 ? { name: 'Twenty-First', description: 'Coming of age' } : null,
  }),
}));

// Mock the contacts hook
vi.mock('../../hooks/useContacts', () => ({
  useContactsQuery: () => ({
    data: [
      {
        id: '1',
        name: 'John Doe',
        birthday: '1994-06-15',
        category: 'Friends',
      },
      {
        id: '2',
        name: 'Jane Smith',
        birthday: '2003-12-25', // Will be 21
        category: 'Family',
      },
    ],
    isLoading: false,
  }),
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
};

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Messaging Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('MessagingDashboard', () => {
    it('should render dashboard with stats', () => {
      renderWithQueryClient(<MessagingDashboard />);
      
      expect(screen.getByText('Messaging Dashboard')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument(); // Total messages
      expect(screen.getByText('120')).toBeInTheDocument(); // Birthday messages
      expect(screen.getByText('96.7%')).toBeInTheDocument(); // Success rate
    });

    it('should switch between tabs', () => {
      renderWithQueryClient(<MessagingDashboard />);
      
      // Should start on overview tab
      expect(screen.getByText('Recent Messages')).toBeInTheDocument();
      
      // Click templates tab
      fireEvent.click(screen.getByText('Templates'));
      expect(screen.getByText('Message Templates')).toBeInTheDocument();
      
      // Click automation tab
      fireEvent.click(screen.getByText('Birthday Messages'));
      expect(screen.getByText('Automated Birthday Messaging')).toBeInTheDocument();
    });

    it('should display recent messages and scheduled messages', () => {
      renderWithQueryClient(<MessagingDashboard />);
      
      expect(screen.getByText('Recent Messages')).toBeInTheDocument();
      expect(screen.getByText('Scheduled Messages')).toBeInTheDocument();
      expect(screen.getByText('Happy Birthday John!')).toBeInTheDocument();
    });
  });

  describe('MessageTemplateManager', () => {
    it('should render template list', () => {
      renderWithQueryClient(<MessageTemplateManager />);
      
      expect(screen.getByText('Message Templates')).toBeInTheDocument();
      expect(screen.getByText('Birthday Template')).toBeInTheDocument();
      expect(screen.getByText('Happy Birthday {name}!')).toBeInTheDocument();
    });

    it('should show create template form', () => {
      renderWithQueryClient(<MessageTemplateManager />);
      
      fireEvent.click(screen.getByText('+ New Template'));
      
      expect(screen.getByText('Create New Template')).toBeInTheDocument();
      expect(screen.getByLabelText('Template Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Template Type')).toBeInTheDocument();
      expect(screen.getByLabelText('Subject Line')).toBeInTheDocument();
      expect(screen.getByLabelText('Message Content')).toBeInTheDocument();
    });

    it('should extract variables from template content', async () => {
      renderWithQueryClient(<MessageTemplateManager />);
      
      fireEvent.click(screen.getByText('+ New Template'));
      
      const subjectInput = screen.getByLabelText('Subject Line');
      const contentInput = screen.getByLabelText('Message Content');
      
      fireEvent.change(subjectInput, { target: { value: 'Hello {name}!' } });
      fireEvent.change(contentInput, { target: { value: 'Dear {name}, you are {age} years old.' } });
      
      await waitFor(() => {
        expect(screen.getByText('{name}')).toBeInTheDocument();
        expect(screen.getByText('{age}')).toBeInTheDocument();
      });
    });
  });

  describe('LandmarkBirthdayDetector', () => {
    it('should render landmark birthdays', () => {
      renderWithQueryClient(<LandmarkBirthdayDetector />);
      
      expect(screen.getByText('Landmark Birthdays')).toBeInTheDocument();
      expect(screen.getByText('Special milestone birthdays to celebrate')).toBeInTheDocument();
    });

    it('should show stats cards', () => {
      renderWithQueryClient(<LandmarkBirthdayDetector />);
      
      expect(screen.getByText('Current Year')).toBeInTheDocument();
      expect(screen.getByText('Upcoming')).toBeInTheDocument();
      expect(screen.getByText('This Month')).toBeInTheDocument();
    });

    it('should filter landmark birthdays', () => {
      renderWithQueryClient(<LandmarkBirthdayDetector />);
      
      // Test filter buttons
      expect(screen.getByText('All Landmarks')).toBeInTheDocument();
      expect(screen.getByText('Current Year')).toBeInTheDocument();
      expect(screen.getByText('Upcoming')).toBeInTheDocument();
      
      fireEvent.click(screen.getByText('Upcoming'));
      // Should filter to only upcoming landmarks
    });

    it('should show landmark ages reference', () => {
      renderWithQueryClient(<LandmarkBirthdayDetector />);
      
      expect(screen.getByText('Landmark Birthday Ages')).toBeInTheDocument();
      // Should show various landmark ages like 18, 21, 30, etc.
    });
  });

  describe('WeeklyNotificationSystem', () => {
    it('should render weekly notification settings', () => {
      renderWithQueryClient(<WeeklyNotificationSystem />);
      
      expect(screen.getByText('Weekly Admin Notifications')).toBeInTheDocument();
      expect(screen.getByText('Automated weekly summaries of upcoming birthdays')).toBeInTheDocument();
      expect(screen.getByText('Enabled')).toBeInTheDocument();
    });

    it('should show notification settings form', () => {
      renderWithQueryClient(<WeeklyNotificationSystem />);
      
      expect(screen.getByLabelText('Notification Day')).toBeInTheDocument();
      expect(screen.getByLabelText('Notification Time')).toBeInTheDocument();
      expect(screen.getByLabelText('Admin Email')).toBeInTheDocument();
    });

    it('should show preview functionality', () => {
      renderWithQueryClient(<WeeklyNotificationSystem />);
      
      fireEvent.click(screen.getByText('Preview This Week'));
      
      expect(screen.getByText('Hide Preview')).toBeInTheDocument();
      // Should show weekly preview content
    });

    it('should handle enable/disable toggle', () => {
      renderWithQueryClient(<WeeklyNotificationSystem />);
      
      const toggle = screen.getByRole('button', { name: /enable weekly notifications/i });
      expect(toggle).toBeInTheDocument();
      
      fireEvent.click(toggle);
      // Should call update settings
    });
  });

  describe('Error Handling', () => {
    it('should handle loading states', () => {
      // Mock loading state
      vi.mocked(require('../../hooks/useMessaging').useTemplatesQuery).mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      });

      renderWithQueryClient(<MessageTemplateManager />);
      
      // Should show loading skeleton
      expect(screen.getByTestId('loading-skeleton') || document.querySelector('.animate-pulse')).toBeInTheDocument();
    });

    it('should handle error states', () => {
      // Mock error state
      vi.mocked(require('../../hooks/useMessaging').useTemplatesQuery).mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load templates'),
      });

      renderWithQueryClient(<MessageTemplateManager />);
      
      expect(screen.getByText('Failed to load templates')).toBeInTheDocument();
      expect(screen.getByText('Try again')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithQueryClient(<MessagingDashboard />);
      
      // Check for proper button labels
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-label', expect.any(String));
      });
    });

    it('should support keyboard navigation', () => {
      renderWithQueryClient(<MessagingDashboard />);
      
      const tabs = screen.getAllByRole('button');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('tabIndex', expect.any(String));
      });
    });
  });
});
