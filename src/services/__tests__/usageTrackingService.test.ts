import { usageTrackingService, UsageTrackingService } from '../usageTrackingService';
import { databaseService } from '../databaseService';

// Mock the database service
jest.mock('../databaseService', () => ({
  databaseService: {
    initialize: jest.fn(),
    query: jest.fn(),
  },
}));

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-123'),
}));

const mockDatabaseService = databaseService as jest.Mocked<typeof databaseService>;

describe('UsageTrackingService', () => {
  let service: UsageTrackingService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new UsageTrackingService();
    service.setCurrentUser('test-user-123');
    mockDatabaseService.initialize.mockResolvedValue();
  });

  describe('user management', () => {
    it('should set current user', () => {
      service.setCurrentUser('new-user-456');
      expect(service.getCurrentUserId()).toBe('new-user-456');
    });

    it('should throw error when no user is set', () => {
      const newService = new UsageTrackingService();
      expect(() => newService.getCurrentUserId()).toThrow('No user set. Call setCurrentUser() first.');
    });
  });

  describe('getCurrentUsage', () => {
    it('should return current usage for existing feature', async () => {
      const mockUsageRecord = [{ usage_count: 5 }];
      mockDatabaseService.query.mockResolvedValue(mockUsageRecord);

      const result = await service.getCurrentUsage('test-feature');

      expect(result).toBe(5);
      expect(mockDatabaseService.initialize).toHaveBeenCalled();
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT usage_count FROM usage_tracking WHERE user_id = ? AND feature_key = ?',
        ['test-user-123', 'test-feature']
      );
    });

    it('should return 0 for non-existing feature', async () => {
      mockDatabaseService.query.mockResolvedValue([]);

      const result = await service.getCurrentUsage('non-existing-feature');

      expect(result).toBe(0);
    });

    it('should return 0 on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await service.getCurrentUsage('test-feature');

      expect(result).toBe(0);
      expect(consoleSpy).toHaveBeenCalledWith('Error getting current usage:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('incrementUsage', () => {
    it('should increment usage for existing feature', async () => {
      const mockExistingRecord = [{ id: 'existing-id', usage_count: 3 }];
      mockDatabaseService.query
        .mockResolvedValueOnce(mockExistingRecord) // SELECT query
        .mockResolvedValueOnce([]); // UPDATE query

      const result = await service.incrementUsage('test-feature');

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT * FROM usage_tracking WHERE user_id = ? AND feature_key = ?',
        ['test-user-123', 'test-feature']
      );
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'UPDATE usage_tracking SET usage_count = usage_count + 1, last_used = ?, updated_at = ? WHERE user_id = ? AND feature_key = ?',
        [expect.any(String), expect.any(String), 'test-user-123', 'test-feature']
      );
    });

    it('should create new record for non-existing feature', async () => {
      mockDatabaseService.query
        .mockResolvedValueOnce([]) // SELECT query (no existing record)
        .mockResolvedValueOnce([]); // INSERT query

      const result = await service.incrementUsage('new-feature');

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'INSERT INTO usage_tracking (id, user_id, feature_key, usage_count, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
        ['mock-uuid-123', 'test-user-123', 'new-feature', 1, expect.any(String), expect.any(String), expect.any(String)]
      );
    });

    it('should return false on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await service.incrementUsage('test-feature');

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error incrementing usage:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('setUsage', () => {
    it('should set usage for existing feature', async () => {
      const mockExistingRecord = [{ id: 'existing-id' }];
      mockDatabaseService.query
        .mockResolvedValueOnce(mockExistingRecord) // SELECT query
        .mockResolvedValueOnce([]); // UPDATE query

      const result = await service.setUsage('test-feature', 10);

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'UPDATE usage_tracking SET usage_count = ?, last_used = ?, updated_at = ? WHERE user_id = ? AND feature_key = ?',
        [10, expect.any(String), expect.any(String), 'test-user-123', 'test-feature']
      );
    });

    it('should create new record for non-existing feature', async () => {
      mockDatabaseService.query
        .mockResolvedValueOnce([]) // SELECT query (no existing record)
        .mockResolvedValueOnce([]); // INSERT query

      const result = await service.setUsage('new-feature', 5);

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'INSERT INTO usage_tracking (id, user_id, feature_key, usage_count, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
        ['mock-uuid-123', 'test-user-123', 'new-feature', 5, expect.any(String), expect.any(String), expect.any(String)]
      );
    });

    it('should return false on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await service.setUsage('test-feature', 5);

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error setting usage:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('resetUsage', () => {
    it('should reset usage to 0', async () => {
      const setUsageSpy = jest.spyOn(service, 'setUsage').mockResolvedValue(true);

      const result = await service.resetUsage('test-feature');

      expect(result).toBe(true);
      expect(setUsageSpy).toHaveBeenCalledWith('test-feature', 0);
      
      setUsageSpy.mockRestore();
    });
  });

  describe('getAllUsage', () => {
    it('should return all usage records for user', async () => {
      const mockRecords = [
        { id: '1', user_id: 'test-user-123', feature_key: 'feature1', usage_count: 5 },
        { id: '2', user_id: 'test-user-123', feature_key: 'feature2', usage_count: 3 },
      ];
      mockDatabaseService.query.mockResolvedValue(mockRecords);

      const result = await service.getAllUsage();

      expect(result).toEqual(mockRecords);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT * FROM usage_tracking WHERE user_id = ? ORDER BY last_used DESC',
        ['test-user-123']
      );
    });

    it('should return empty array on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await service.getAllUsage();

      expect(result).toEqual([]);
      expect(consoleSpy).toHaveBeenCalledWith('Error getting all usage:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('getUsageStats', () => {
    it('should return usage statistics', async () => {
      const mockRecords = [
        { feature_key: 'feature1', usage_count: 10, last_used: '2023-12-01T10:00:00Z' },
        { feature_key: 'feature2', usage_count: 5, last_used: '2023-12-02T10:00:00Z' },
        { feature_key: 'feature3', usage_count: 15, last_used: '2023-11-30T10:00:00Z' },
      ];
      jest.spyOn(service, 'getAllUsage').mockResolvedValue(mockRecords as any);

      const result = await service.getUsageStats();

      expect(result).toEqual({
        totalFeatures: 3,
        totalUsage: 30,
        mostUsedFeature: 'feature3',
        lastActivity: '2023-12-02T10:00:00Z',
      });
    });

    it('should return default stats for empty usage', async () => {
      jest.spyOn(service, 'getAllUsage').mockResolvedValue([]);

      const result = await service.getUsageStats();

      expect(result).toEqual({
        totalFeatures: 0,
        totalUsage: 0,
        mostUsedFeature: null,
        lastActivity: null,
      });
    });

    it('should return default stats on error', async () => {
      jest.spyOn(service, 'getAllUsage').mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await service.getUsageStats();

      expect(result).toEqual({
        totalFeatures: 0,
        totalUsage: 0,
        mostUsedFeature: null,
        lastActivity: null,
      });
      expect(consoleSpy).toHaveBeenCalledWith('Error getting usage stats:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('clearAllUsage', () => {
    it('should clear all usage data for user', async () => {
      mockDatabaseService.query.mockResolvedValue([]);
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await service.clearAllUsage();

      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'DELETE FROM usage_tracking WHERE user_id = ?',
        ['test-user-123']
      );
      expect(consoleSpy).toHaveBeenCalledWith('✅ Cleared all usage tracking data');
      
      consoleSpy.mockRestore();
    });

    it('should handle clear errors gracefully', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await service.clearAllUsage();

      expect(consoleSpy).toHaveBeenCalledWith('Error clearing usage data:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('singleton instance', () => {
    it('should export singleton instance', () => {
      expect(usageTrackingService).toBeInstanceOf(UsageTrackingService);
    });
  });
});
