import { contactsService } from '../contactsService';
import { databaseService } from '../databaseService';

// Mock the database service
jest.mock('../databaseService', () => ({
  databaseService: {
    initialize: jest.fn(),
    query: jest.fn(),
  },
}));

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-contact-id-123'),
}));

const mockDatabaseService = databaseService as jest.Mocked<typeof databaseService>;

describe('ContactsService', () => {
  const mockUser = '<EMAIL>';
  const mockContact = {
    id: 'mock-contact-id-123',
    name: '<PERSON>',
    birthday: '1990-05-15',
    category: 'Family',
    email: '<EMAIL>',
    phone: '+1234567890',
    notes: 'Test notes',
    user_email: mockUser,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockDatabaseService.initialize.mockResolvedValue();
  });

  describe('getAllContacts', () => {
    it('should return all contacts for user', async () => {
      const mockContacts = [mockContact];
      mockDatabaseService.query.mockResolvedValue(mockContacts);

      const result = await contactsService.getAllContacts(mockUser);

      expect(result).toEqual(mockContacts);
      expect(mockDatabaseService.initialize).toHaveBeenCalled();
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT * FROM contacts WHERE user_email = ? ORDER BY name ASC',
        [mockUser]
      );
    });

    it('should return empty array on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await contactsService.getAllContacts(mockUser);

      expect(result).toEqual([]);
      expect(consoleSpy).toHaveBeenCalledWith('Error getting contacts:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('getContactById', () => {
    it('should return contact by id', async () => {
      mockDatabaseService.query.mockResolvedValue([mockContact]);

      const result = await contactsService.getContactById(mockUser, 'contact-123');

      expect(result).toEqual(mockContact);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT * FROM contacts WHERE user_email = ? AND id = ?',
        [mockUser, 'contact-123']
      );
    });

    it('should return null if contact not found', async () => {
      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.getContactById(mockUser, 'non-existing');

      expect(result).toBeNull();
    });

    it('should return null on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await contactsService.getContactById(mockUser, 'contact-123');

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Error getting contact by id:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('addContact', () => {
    it('should add new contact successfully', async () => {
      const newContact = {
        name: 'Jane Doe',
        birthday: '1985-03-20',
        category: 'Friend',
        email: '<EMAIL>',
        phone: '+0987654321',
        notes: 'New contact',
      };

      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.addContact(mockUser, newContact);

      expect(result).toEqual({
        id: 'mock-contact-id-123',
        ...newContact,
        user_email: mockUser,
        created_at: expect.any(String),
        updated_at: expect.any(String),
      });

      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO contacts'),
        expect.arrayContaining([
          'mock-contact-id-123',
          mockUser,
          newContact.name,
          newContact.birthday,
          newContact.category,
          newContact.email,
          newContact.phone,
          newContact.notes,
          expect.any(String),
          expect.any(String),
        ])
      );
    });

    it('should throw error on database failure', async () => {
      const newContact = {
        name: 'Jane Doe',
        birthday: '1985-03-20',
        category: 'Friend',
      };

      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));

      await expect(contactsService.addContact(mockUser, newContact)).rejects.toThrow('Database error');
    });
  });

  describe('updateContact', () => {
    it('should update existing contact successfully', async () => {
      const updates = {
        name: 'John Updated',
        category: 'Work',
      };

      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.updateContact(mockUser, 'contact-123', updates);

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE contacts SET'),
        expect.arrayContaining([
          updates.name,
          updates.category,
          expect.any(String),
          mockUser,
          'contact-123',
        ])
      );
    });

    it('should handle partial updates', async () => {
      const updates = { name: 'John Updated' };

      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.updateContact(mockUser, 'contact-123', updates);

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'UPDATE contacts SET name = ?, updated_at = ? WHERE user_email = ? AND id = ?',
        [updates.name, expect.any(String), mockUser, 'contact-123']
      );
    });

    it('should return false on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await contactsService.updateContact(mockUser, 'contact-123', { name: 'Test' });

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error updating contact:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('deleteContact', () => {
    it('should delete contact successfully', async () => {
      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.deleteContact(mockUser, 'contact-123');

      expect(result).toBe(true);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'DELETE FROM contacts WHERE user_email = ? AND id = ?',
        [mockUser, 'contact-123']
      );
    });

    it('should return false on database error', async () => {
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await contactsService.deleteContact(mockUser, 'contact-123');

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Error deleting contact:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('getUpcomingBirthdays', () => {
    it('should return upcoming birthdays within specified days', async () => {
      const upcomingContacts = [mockContact];
      mockDatabaseService.query.mockResolvedValue(upcomingContacts);

      const result = await contactsService.getUpcomingBirthdays(mockUser, 7);

      expect(result).toEqual(upcomingContacts);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.stringContaining('upcoming birthdays'),
        [mockUser, 7]
      );
    });

    it('should use default 30 days if not specified', async () => {
      mockDatabaseService.query.mockResolvedValue([]);

      await contactsService.getUpcomingBirthdays(mockUser);

      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.any(String),
        [mockUser, 30]
      );
    });
  });

  describe('getTodaysBirthdays', () => {
    it('should return today\'s birthdays', async () => {
      const todaysContacts = [mockContact];
      mockDatabaseService.query.mockResolvedValue(todaysContacts);

      const result = await contactsService.getTodaysBirthdays(mockUser);

      expect(result).toEqual(todaysContacts);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.stringContaining('today\'s birthdays'),
        [mockUser]
      );
    });
  });

  describe('searchContacts', () => {
    it('should search contacts by query', async () => {
      const searchResults = [mockContact];
      mockDatabaseService.query.mockResolvedValue(searchResults);

      const result = await contactsService.searchContacts(mockUser, 'John');

      expect(result).toEqual(searchResults);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_email = ? AND'),
        [mockUser, '%john%', '%john%', '%john%']
      );
    });

    it('should return empty array for empty query', async () => {
      const result = await contactsService.searchContacts(mockUser, '');

      expect(result).toEqual([]);
      expect(mockDatabaseService.query).not.toHaveBeenCalled();
    });
  });

  describe('getContactsByCategory', () => {
    it('should return contacts by category', async () => {
      const categoryContacts = [mockContact];
      mockDatabaseService.query.mockResolvedValue(categoryContacts);

      const result = await contactsService.getContactsByCategory(mockUser, 'Family');

      expect(result).toEqual(categoryContacts);
      expect(mockDatabaseService.query).toHaveBeenCalledWith(
        'SELECT * FROM contacts WHERE user_email = ? AND category = ? ORDER BY name ASC',
        [mockUser, 'Family']
      );
    });
  });

  describe('bulkAddContacts', () => {
    it('should add multiple contacts successfully', async () => {
      const contacts = [
        { name: 'Contact 1', birthday: '1990-01-01', category: 'Family' },
        { name: 'Contact 2', birthday: '1985-02-02', category: 'Friend' },
      ];

      mockDatabaseService.query.mockResolvedValue([]);

      const result = await contactsService.bulkAddContacts(mockUser, contacts);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expect.objectContaining({
        id: 'mock-contact-id-123',
        name: 'Contact 1',
        user_email: mockUser,
      }));
    });

    it('should return empty array on database error', async () => {
      const contacts = [{ name: 'Contact 1', birthday: '1990-01-01', category: 'Family' }];
      mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await contactsService.bulkAddContacts(mockUser, contacts);

      expect(result).toEqual([]);
      expect(consoleSpy).toHaveBeenCalledWith('Error bulk adding contacts:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });
});
