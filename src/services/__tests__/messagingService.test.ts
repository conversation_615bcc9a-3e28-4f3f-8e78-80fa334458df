import { messagingService } from '../messagingService';
import { databaseService } from '../databaseService';

// Mock the database service
jest.mock('../databaseService', () => ({
  databaseService: {
    initialize: jest.fn(),
    query: jest.fn(),
  },
}));

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-message-id-123'),
}));

const mockDatabaseService = databaseService as jest.Mocked<typeof databaseService>;

describe('MessagingService', () => {
  const mockUser = '<EMAIL>';
  const mockTemplate = {
    id: 'template-123',
    user_email: mockUser,
    name: 'Birthday Template',
    type: 'birthday' as const,
    subject: 'Happy Birthday!',
    content: 'Happy birthday {{name}}! Hope you have a wonderful day.',
    variables: ['name'],
    is_default: false,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockMessage = {
    id: 'message-123',
    user_email: mockUser,
    contact_id: 'contact-123',
    template_id: 'template-123',
    type: 'birthday' as const,
    subject: 'Happy Birthday John!',
    content: 'Happy birthday John! Hope you have a wonderful day.',
    status: 'sent' as const,
    sent_at: '2023-01-01T10:00:00Z',
    created_at: '2023-01-01T09:00:00Z',
    updated_at: '2023-01-01T10:00:00Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockDatabaseService.initialize.mockResolvedValue();
  });

  describe('template management', () => {
    describe('getMessageTemplates', () => {
      it('should return all templates for user', async () => {
        const mockTemplates = [mockTemplate];
        mockDatabaseService.query.mockResolvedValue(mockTemplates);

        const result = await messagingService.getMessageTemplates(mockUser);

        expect(result).toEqual(mockTemplates);
        expect(mockDatabaseService.initialize).toHaveBeenCalled();
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'SELECT * FROM message_templates WHERE user_email = ? ORDER BY created_at DESC',
          [mockUser]
        );
      });

      it('should return empty array on database error', async () => {
        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = await messagingService.getMessageTemplates(mockUser);

        expect(result).toEqual([]);
        expect(consoleSpy).toHaveBeenCalledWith('Error getting message templates:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });
    });

    describe('getTemplatesByType', () => {
      it('should return templates filtered by type', async () => {
        const mockTemplates = [mockTemplate];
        mockDatabaseService.query.mockResolvedValue(mockTemplates);

        const result = await messagingService.getTemplatesByType(mockUser, 'birthday');

        expect(result).toEqual(mockTemplates);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'SELECT * FROM message_templates WHERE user_email = ? AND type = ? ORDER BY created_at DESC',
          [mockUser, 'birthday']
        );
      });
    });

    describe('createMessageTemplate', () => {
      it('should create new template successfully', async () => {
        const newTemplate = {
          name: 'New Template',
          type: 'anniversary' as const,
          subject: 'Happy Anniversary!',
          content: 'Happy anniversary {{name}}!',
          variables: ['name'],
        };

        mockDatabaseService.query.mockResolvedValue([]);

        const result = await messagingService.createMessageTemplate(mockUser, newTemplate);

        expect(result).toEqual({
          id: 'mock-message-id-123',
          user_email: mockUser,
          ...newTemplate,
          is_default: false,
          created_at: expect.any(String),
          updated_at: expect.any(String),
        });

        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO message_templates'),
          expect.arrayContaining([
            'mock-message-id-123',
            mockUser,
            newTemplate.name,
            newTemplate.type,
            newTemplate.subject,
            newTemplate.content,
            JSON.stringify(newTemplate.variables),
            false,
            expect.any(String),
            expect.any(String),
          ])
        );
      });

      it('should throw error on database failure', async () => {
        const newTemplate = {
          name: 'New Template',
          type: 'birthday' as const,
          subject: 'Happy Birthday!',
          content: 'Happy birthday!',
          variables: [],
        };

        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));

        await expect(messagingService.createMessageTemplate(mockUser, newTemplate)).rejects.toThrow('Database error');
      });
    });

    describe('updateMessageTemplate', () => {
      it('should update existing template successfully', async () => {
        const updates = {
          name: 'Updated Template',
          subject: 'Updated Subject',
        };

        mockDatabaseService.query.mockResolvedValue([]);

        const result = await messagingService.updateMessageTemplate(mockUser, 'template-123', updates);

        expect(result).toBe(true);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE message_templates SET'),
          expect.arrayContaining([
            updates.name,
            updates.subject,
            expect.any(String),
            mockUser,
            'template-123',
          ])
        );
      });

      it('should handle partial updates', async () => {
        const updates = { name: 'Updated Name Only' };

        mockDatabaseService.query.mockResolvedValue([]);

        const result = await messagingService.updateMessageTemplate(mockUser, 'template-123', updates);

        expect(result).toBe(true);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'UPDATE message_templates SET name = ?, updated_at = ? WHERE user_email = ? AND id = ?',
          [updates.name, expect.any(String), mockUser, 'template-123']
        );
      });

      it('should return false on database error', async () => {
        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = await messagingService.updateMessageTemplate(mockUser, 'template-123', { name: 'Test' });

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error updating message template:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });
    });

    describe('deleteMessageTemplate', () => {
      it('should delete template successfully', async () => {
        mockDatabaseService.query.mockResolvedValue([]);

        const result = await messagingService.deleteMessageTemplate(mockUser, 'template-123');

        expect(result).toBe(true);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'DELETE FROM message_templates WHERE user_email = ? AND id = ?',
          [mockUser, 'template-123']
        );
      });

      it('should return false on database error', async () => {
        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = await messagingService.deleteMessageTemplate(mockUser, 'template-123');

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error deleting message template:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });
    });
  });

  describe('message history', () => {
    describe('getMessageHistory', () => {
      it('should return all message history for user', async () => {
        const mockMessages = [mockMessage];
        mockDatabaseService.query.mockResolvedValue(mockMessages);

        const result = await messagingService.getMessageHistory(mockUser);

        expect(result).toEqual(mockMessages);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'SELECT * FROM message_history WHERE user_email = ? ORDER BY created_at DESC',
          [mockUser]
        );
      });

      it('should return empty array on database error', async () => {
        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = await messagingService.getMessageHistory(mockUser);

        expect(result).toEqual([]);
        expect(consoleSpy).toHaveBeenCalledWith('Error getting message history:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });
    });

    describe('getContactMessageHistory', () => {
      it('should return message history for specific contact', async () => {
        const mockMessages = [mockMessage];
        mockDatabaseService.query.mockResolvedValue(mockMessages);

        const result = await messagingService.getContactMessageHistory(mockUser, 'contact-123');

        expect(result).toEqual(mockMessages);
        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          'SELECT * FROM message_history WHERE user_email = ? AND contact_id = ? ORDER BY created_at DESC',
          [mockUser, 'contact-123']
        );
      });
    });

    describe('addMessageToHistory', () => {
      it('should add message to history successfully', async () => {
        const messageData = {
          contact_id: 'contact-123',
          template_id: 'template-123',
          type: 'birthday' as const,
          subject: 'Happy Birthday!',
          content: 'Happy birthday John!',
          status: 'sent' as const,
        };

        mockDatabaseService.query.mockResolvedValue([]);

        const result = await messagingService.addMessageToHistory(mockUser, messageData);

        expect(result).toEqual({
          id: 'mock-message-id-123',
          user_email: mockUser,
          ...messageData,
          sent_at: expect.any(String),
          created_at: expect.any(String),
          updated_at: expect.any(String),
        });

        expect(mockDatabaseService.query).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO message_history'),
          expect.arrayContaining([
            'mock-message-id-123',
            mockUser,
            messageData.contact_id,
            messageData.template_id,
            messageData.type,
            messageData.subject,
            messageData.content,
            messageData.status,
            expect.any(String),
            expect.any(String),
            expect.any(String),
          ])
        );
      });

      it('should throw error on database failure', async () => {
        const messageData = {
          contact_id: 'contact-123',
          template_id: 'template-123',
          type: 'birthday' as const,
          subject: 'Happy Birthday!',
          content: 'Happy birthday!',
          status: 'sent' as const,
        };

        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));

        await expect(messagingService.addMessageToHistory(mockUser, messageData)).rejects.toThrow('Database error');
      });
    });
  });

  describe('message processing', () => {
    describe('processTemplate', () => {
      it('should replace template variables with contact data', () => {
        const template = 'Happy birthday {{name}}! You are turning {{age}} today.';
        const variables = { name: 'John', age: '30' };

        const result = messagingService.processTemplate(template, variables);

        expect(result).toBe('Happy birthday John! You are turning 30 today.');
      });

      it('should handle missing variables gracefully', () => {
        const template = 'Happy birthday {{name}}! You are turning {{age}} today.';
        const variables = { name: 'John' };

        const result = messagingService.processTemplate(template, variables);

        expect(result).toBe('Happy birthday John! You are turning {{age}} today.');
      });

      it('should handle empty variables object', () => {
        const template = 'Happy birthday {{name}}!';
        const variables = {};

        const result = messagingService.processTemplate(template, variables);

        expect(result).toBe('Happy birthday {{name}}!');
      });

      it('should handle template without variables', () => {
        const template = 'Happy birthday!';
        const variables = { name: 'John' };

        const result = messagingService.processTemplate(template, variables);

        expect(result).toBe('Happy birthday!');
      });
    });

    describe('extractVariables', () => {
      it('should extract variables from template', () => {
        const template = 'Happy birthday {{name}}! You are turning {{age}} today. Best wishes, {{sender}}.';

        const result = messagingService.extractVariables(template);

        expect(result).toEqual(['name', 'age', 'sender']);
      });

      it('should handle duplicate variables', () => {
        const template = 'Hello {{name}}, this is {{name}} speaking.';

        const result = messagingService.extractVariables(template);

        expect(result).toEqual(['name']);
      });

      it('should handle template without variables', () => {
        const template = 'Happy birthday! Hope you have a great day.';

        const result = messagingService.extractVariables(template);

        expect(result).toEqual([]);
      });

      it('should handle malformed variable syntax', () => {
        const template = 'Hello {name} and {{incomplete and {{valid}}.';

        const result = messagingService.extractVariables(template);

        expect(result).toEqual(['valid']);
      });
    });
  });

  describe('default templates', () => {
    describe('getDefaultTemplates', () => {
      it('should return default templates for all types', () => {
        const result = messagingService.getDefaultTemplates();

        expect(result).toHaveLength(3);
        expect(result.find(t => t.type === 'birthday')).toBeDefined();
        expect(result.find(t => t.type === 'anniversary')).toBeDefined();
        expect(result.find(t => t.type === 'reminder')).toBeDefined();
      });

      it('should return templates with proper structure', () => {
        const result = messagingService.getDefaultTemplates();

        result.forEach(template => {
          expect(template).toHaveProperty('id');
          expect(template).toHaveProperty('name');
          expect(template).toHaveProperty('type');
          expect(template).toHaveProperty('subject');
          expect(template).toHaveProperty('content');
          expect(template).toHaveProperty('variables');
          expect(template.is_default).toBe(true);
        });
      });
    });

    describe('createDefaultTemplates', () => {
      it('should create default templates for user', async () => {
        mockDatabaseService.query.mockResolvedValue([]);

        await messagingService.createDefaultTemplates(mockUser);

        expect(mockDatabaseService.query).toHaveBeenCalledTimes(3); // One for each default template
      });

      it('should handle database errors gracefully', async () => {
        mockDatabaseService.query.mockRejectedValue(new Error('Database error'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        await messagingService.createDefaultTemplates(mockUser);

        expect(consoleSpy).toHaveBeenCalledWith('Error creating default templates:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });
    });
  });
});
