import { databaseService } from '../databaseService';

// Mock sql.js
jest.mock('sql.js', () => ({
  default: jest.fn().mockImplementation(() => ({
    Database: jest.fn().mockImplementation(() => ({
      run: jest.fn(),
      exec: jest.fn(),
      prepare: jest.fn().mockReturnValue({
        getAsObject: jest.fn(),
        step: jest.fn(),
        free: jest.fn(),
      }),
      export: jest.fn().mockReturnValue(new Uint8Array()),
      close: jest.fn(),
    })),
  })),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

describe('DatabaseService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('initialization', () => {
    it('should initialize database successfully', async () => {
      await expect(databaseService.initialize()).resolves.not.toThrow();
    });

    it('should load existing database from localStorage', async () => {
      const mockDbData = new Uint8Array([1, 2, 3, 4]);
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(Array.from(mockDbData)));

      await databaseService.initialize();

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('wewish_sqlite_db');
    });

    it('should handle initialization errors gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      await expect(databaseService.initialize()).resolves.not.toThrow();
    });
  });

  describe('query operations', () => {
    beforeEach(async () => {
      await databaseService.initialize();
    });

    it('should execute SELECT queries successfully', async () => {
      const mockResults = [{ id: '1', name: 'Test' }];
      const mockDb = {
        prepare: jest.fn().mockReturnValue({
          getAsObject: jest.fn().mockReturnValueOnce(mockResults[0]).mockReturnValueOnce(undefined),
          step: jest.fn().mockReturnValueOnce(true).mockReturnValueOnce(false),
          free: jest.fn(),
        }),
      };
      
      // Mock the database instance
      (databaseService as any).db = mockDb;

      const result = await databaseService.query('SELECT * FROM users WHERE id = ?', ['1']);
      
      expect(result).toEqual(mockResults);
      expect(mockDb.prepare).toHaveBeenCalledWith('SELECT * FROM users WHERE id = ?');
    });

    it('should execute INSERT/UPDATE/DELETE queries successfully', async () => {
      const mockDb = {
        run: jest.fn(),
      };
      
      (databaseService as any).db = mockDb;

      await databaseService.query('INSERT INTO users (name) VALUES (?)', ['Test User']);
      
      expect(mockDb.run).toHaveBeenCalledWith('INSERT INTO users (name) VALUES (?)', ['Test User']);
    });

    it('should handle query errors gracefully', async () => {
      const mockDb = {
        prepare: jest.fn().mockImplementation(() => {
          throw new Error('SQL error');
        }),
        run: jest.fn().mockImplementation(() => {
          throw new Error('SQL error');
        }),
      };
      
      (databaseService as any).db = mockDb;

      await expect(databaseService.query('INVALID SQL')).rejects.toThrow('SQL error');
    });

    it('should handle queries with no parameters', async () => {
      const mockDb = {
        prepare: jest.fn().mockReturnValue({
          getAsObject: jest.fn().mockReturnValue(undefined),
          step: jest.fn().mockReturnValue(false),
          free: jest.fn(),
        }),
      };
      
      (databaseService as any).db = mockDb;

      const result = await databaseService.query('SELECT COUNT(*) FROM users');
      
      expect(result).toEqual([]);
      expect(mockDb.prepare).toHaveBeenCalledWith('SELECT COUNT(*) FROM users');
    });
  });

  describe('transaction operations', () => {
    beforeEach(async () => {
      await databaseService.initialize();
    });

    it('should execute transactions successfully', async () => {
      const mockDb = {
        run: jest.fn(),
      };
      
      (databaseService as any).db = mockDb;

      const operations = [
        { query: 'INSERT INTO users (name) VALUES (?)', params: ['User 1'] },
        { query: 'INSERT INTO users (name) VALUES (?)', params: ['User 2'] },
      ];

      await databaseService.transaction(operations);

      expect(mockDb.run).toHaveBeenCalledWith('BEGIN TRANSACTION');
      expect(mockDb.run).toHaveBeenCalledWith('INSERT INTO users (name) VALUES (?)', ['User 1']);
      expect(mockDb.run).toHaveBeenCalledWith('INSERT INTO users (name) VALUES (?)', ['User 2']);
      expect(mockDb.run).toHaveBeenCalledWith('COMMIT');
    });

    it('should rollback transaction on error', async () => {
      const mockDb = {
        run: jest.fn().mockImplementation((query) => {
          if (query.includes('INSERT')) {
            throw new Error('Insert failed');
          }
        }),
      };
      
      (databaseService as any).db = mockDb;

      const operations = [
        { query: 'INSERT INTO users (name) VALUES (?)', params: ['User 1'] },
      ];

      await expect(databaseService.transaction(operations)).rejects.toThrow('Insert failed');

      expect(mockDb.run).toHaveBeenCalledWith('BEGIN TRANSACTION');
      expect(mockDb.run).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('backup and restore', () => {
    beforeEach(async () => {
      await databaseService.initialize();
    });

    it('should backup database successfully', async () => {
      const mockExportData = new Uint8Array([1, 2, 3, 4]);
      const mockDb = {
        export: jest.fn().mockReturnValue(mockExportData),
      };
      
      (databaseService as any).db = mockDb;

      const backup = await databaseService.backup();

      expect(backup).toEqual(mockExportData);
      expect(mockDb.export).toHaveBeenCalled();
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'wewish_sqlite_db',
        JSON.stringify(Array.from(mockExportData))
      );
    });

    it('should restore database successfully', async () => {
      const mockBackupData = new Uint8Array([1, 2, 3, 4]);
      
      await databaseService.restore(mockBackupData);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'wewish_sqlite_db',
        JSON.stringify(Array.from(mockBackupData))
      );
    });

    it('should handle backup errors gracefully', async () => {
      const mockDb = {
        export: jest.fn().mockImplementation(() => {
          throw new Error('Export failed');
        }),
      };
      
      (databaseService as any).db = mockDb;

      await expect(databaseService.backup()).rejects.toThrow('Export failed');
    });
  });

  describe('database management', () => {
    beforeEach(async () => {
      await databaseService.initialize();
    });

    it('should clear database successfully', async () => {
      const mockDb = {
        run: jest.fn(),
      };
      
      (databaseService as any).db = mockDb;

      await databaseService.clearDatabase();

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('wewish_sqlite_db');
    });

    it('should check if database is initialized', () => {
      expect(databaseService.isInitialized()).toBe(true);
    });

    it('should return false when database is not initialized', () => {
      (databaseService as any).db = null;
      expect(databaseService.isInitialized()).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should handle database not initialized error', async () => {
      (databaseService as any).db = null;

      await expect(databaseService.query('SELECT * FROM users')).rejects.toThrow('Database not initialized');
    });

    it('should handle transaction with empty operations', async () => {
      await databaseService.initialize();
      
      const mockDb = {
        run: jest.fn(),
      };
      
      (databaseService as any).db = mockDb;

      await databaseService.transaction([]);

      expect(mockDb.run).toHaveBeenCalledWith('BEGIN TRANSACTION');
      expect(mockDb.run).toHaveBeenCalledWith('COMMIT');
    });
  });
});
