import { v4 as uuidv4 } from 'uuid';
import { type User, type UserRole, type Organization } from '../types';
import { persistenceService } from './persistenceService';

class UserManagementService {
  private currentUserId: string | null = null;

  setCurrentUser(userId: string) {
    this.currentUserId = userId;
    persistenceService.setCurrentUser(userId);
  }

  private getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  private simulateDelay(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  }

  // User Management
  async getAllUsers(): Promise<User[]> {
    await this.simulateDelay();
    return persistenceService.getAllGlobal<User>('users');
  }

  async getUserByEmail(email: string): Promise<User | null> {
    await this.simulateDelay();
    const users = await this.getAllUsers();
    return users.find(user => user.email === email) || null;
  }

  async createUser(userData: Omit<User, 'createdAt' | 'updatedAt' | 'lastLogin' | 'isActive'>): Promise<User> {
    await this.simulateDelay();

    const user: User = {
      ...userData,
      role: userData.role || 'user',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store in global users table
    const users = persistenceService.getAllGlobal<User>('users');
    users.push(user);
    localStorage.setItem('wewish_global_users', JSON.stringify(users));

    return user;
  }

  async updateUser(email: string, updates: Partial<User>): Promise<User | null> {
    await this.simulateDelay();

    const users = persistenceService.getAllGlobal<User>('users');
    const userIndex = users.findIndex(user => user.email === email);

    if (userIndex === -1) return null;

    const updatedUser = {
      ...users[userIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    users[userIndex] = updatedUser;
    localStorage.setItem('wewish_global_users', JSON.stringify(users));

    return updatedUser;
  }

  async updateLastLogin(email: string): Promise<void> {
    await this.updateUser(email, { lastLogin: new Date().toISOString() });
  }

  async deactivateUser(email: string): Promise<boolean> {
    const result = await this.updateUser(email, { isActive: false });
    return result !== null;
  }

  async activateUser(email: string): Promise<boolean> {
    const result = await this.updateUser(email, { isActive: true });
    return result !== null;
  }

  // Role Management
  async getUserRole(email: string): Promise<UserRole> {
    const user = await this.getUserByEmail(email);
    return user?.role || 'user';
  }

  async updateUserRole(email: string, role: UserRole): Promise<boolean> {
    const result = await this.updateUser(email, { role });
    return result !== null;
  }

  async isSystemAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'system_admin';
  }

  async isChurchAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'church_admin';
  }

  async isAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'system_admin' || role === 'church_admin';
  }

  // Organization Management
  async getAllOrganizations(): Promise<Organization[]> {
    await this.simulateDelay();
    return persistenceService.getAllGlobal<Organization>('organizations');
  }

  async getOrganizationById(id: string): Promise<Organization | null> {
    await this.simulateDelay();
    const organizations = await this.getAllOrganizations();
    return organizations.find(org => org.id === id) || null;
  }

  async createOrganization(orgData: Omit<Organization, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>): Promise<Organization> {
    await this.simulateDelay();

    const organization: Organization = {
      ...orgData,
      id: uuidv4(),
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const organizations = persistenceService.getAllGlobal<Organization>('organizations');
    organizations.push(organization);
    localStorage.setItem('wewish_global_organizations', JSON.stringify(organizations));

    return organization;
  }

  async updateOrganization(id: string, updates: Partial<Organization>): Promise<Organization | null> {
    await this.simulateDelay();

    const organizations = persistenceService.getAllGlobal<Organization>('organizations');
    const orgIndex = organizations.findIndex(org => org.id === id);

    if (orgIndex === -1) return null;

    const updatedOrg = {
      ...organizations[orgIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    organizations[orgIndex] = updatedOrg;
    localStorage.setItem('wewish_global_organizations', JSON.stringify(organizations));

    return updatedOrg;
  }

  // Initialize default admin users
  async initializeDefaultAdmins(): Promise<void> {
    await this.simulateDelay();

    const existingUsers = await this.getAllUsers();

    // Create default system admin if doesn't exist
    const systemAdminEmail = '<EMAIL>';
    const systemAdminPassword = 'admin123'; // In production, use secure password
    if (!existingUsers.find(user => user.email === systemAdminEmail)) {
      await this.createUser({
        email: systemAdminEmail,
        name: 'System Administrator',
        role: 'system_admin',
      });

      // Add to password database
      await this.addToPasswordDB(systemAdminEmail, systemAdminPassword);
      console.log('✅ Created default system admin:', systemAdminEmail);
      console.log('🔑 Default password:', systemAdminPassword);
    }

    // Create default church admin if doesn't exist
    const churchAdminEmail = '<EMAIL>';
    const churchAdminPassword = 'church123'; // In production, use secure password
    if (!existingUsers.find(user => user.email === churchAdminEmail)) {
      // First create a default church organization
      const existingOrgs = await this.getAllOrganizations();
      let churchOrg = existingOrgs.find(org => org.type === 'church');

      if (!churchOrg) {
        churchOrg = await this.createOrganization({
          name: 'Sample Community Church',
          type: 'church',
          description: 'A sample church organization for birthday management',
          adminEmail: churchAdminEmail,
          settings: {
            allowMemberSelfRegistration: false,
            requireAdminApproval: true,
            defaultContactCategory: 'Members',
            enableAnniversaries: true,
            enableLandmarkBirthdays: true,
            timezone: 'America/New_York',
            emailNotifications: true,
          },
        });
      }

      await this.createUser({
        email: churchAdminEmail,
        name: 'Church Administrator',
        role: 'church_admin',
        organizationId: churchOrg.id,
      });

      // Add to password database
      await this.addToPasswordDB(churchAdminEmail, churchAdminPassword);
      console.log('✅ Created default church admin:', churchAdminEmail);
      console.log('🔑 Default password:', churchAdminPassword);
      console.log('✅ Created default church organization:', churchOrg.name);
    }
  }

  // Helper method to add users to password database
  private async addToPasswordDB(email: string, password: string): Promise<void> {
    const LOCAL_STORAGE_KEY = "birthdaySaaSUsers";

    // Simple password hashing (in production, use proper bcrypt)
    const hashPassword = async (password: string): Promise<string> => {
      const encoder = new TextEncoder();
      const data = encoder.encode(password);
      const hash = await crypto.subtle.digest('SHA-256', data);
      return Array.from(new Uint8Array(hash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    };

    const db = JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY) || '{}');
    const hashedPassword = await hashPassword(password);
    db[email] = hashedPassword;
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(db));
  }

  // Permission checks
  async canManageUsers(userEmail: string): Promise<boolean> {
    return await this.isSystemAdmin(userEmail);
  }

  async canManageOrganization(userEmail: string, organizationId?: string): Promise<boolean> {
    const user = await this.getUserByEmail(userEmail);
    if (!user) return false;

    // System admins can manage all organizations
    if (user.role === 'system_admin') return true;

    // Church admins can only manage their own organization
    if (user.role === 'church_admin' && organizationId) {
      return user.organizationId === organizationId;
    }

    return false;
  }

  async canAccessAdminDashboard(userEmail: string): Promise<boolean> {
    return await this.isAdmin(userEmail);
  }

  // Get users by organization (for church admins)
  async getUsersByOrganization(organizationId: string): Promise<User[]> {
    await this.simulateDelay();
    const users = await this.getAllUsers();
    return users.filter(user => user.organizationId === organizationId);
  }
}

export const userManagementService = new UserManagementService();
