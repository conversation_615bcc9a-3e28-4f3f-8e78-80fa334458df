import { type User, type UserRole, type Organization } from '../types';
import { databaseService } from './databaseService';

// Database row interfaces
interface UserRow {
  id: string;
  email: string;
  name?: string;
  role: string;
  organization_id?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

interface OrganizationRow {
  id: string;
  name: string;
  type: string;
  description?: string;
  admin_email: string;
  settings: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class UserManagementService {
  private currentUserId: string | null = null;

  // Helper functions to convert between database rows and interface types
  private userRowToUser(row: UserRow): User {
    return {
      email: row.email,
      name: row.name,
      role: row.role as UserRole,
      organizationId: row.organization_id,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      lastLogin: row.last_login
    };
  }

  private organizationRowToOrganization(row: OrganizationRow): Organization {
    return {
      id: row.id,
      name: row.name,
      type: row.type as Organization['type'],
      description: row.description,
      adminEmail: row.admin_email,
      settings: JSON.parse(row.settings || '{}'),
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  async initialize(): Promise<void> {
    await databaseService.initialize();
  }

  setCurrentUser(userId: string) {
    this.currentUserId = userId;
    databaseService.setCurrentUser(userId);
  }

  private getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  private simulateDelay(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  }

  // User Management
  async getAllUsers(): Promise<User[]> {
    await this.simulateDelay();
    const rows = await databaseService.query<UserRow>('SELECT * FROM users ORDER BY created_at DESC');
    return rows.map(row => this.userRowToUser(row));
  }

  async getUserByEmail(email: string): Promise<User | null> {
    await this.simulateDelay();
    const rows = await databaseService.query<UserRow>('SELECT * FROM users WHERE email = ?', [email]);
    return rows[0] ? this.userRowToUser(rows[0]) : null;
  }

  async createUser(userData: Omit<User, 'createdAt' | 'updatedAt' | 'lastLogin' | 'isActive'>): Promise<User> {
    await this.simulateDelay();

    // Check if user already exists
    const existingUser = await this.getUserByEmail(userData.email);
    if (existingUser) {
      throw new Error(`User with email ${userData.email} already exists`);
    }

    const userRow = await databaseService.create<UserRow>('users', {
      email: userData.email,
      name: userData.name,
      role: userData.role || 'user',
      organization_id: userData.organizationId,
      is_active: true
    });

    return this.userRowToUser(userRow);
  }

  async createUserIfNotExists(userData: Omit<User, 'createdAt' | 'updatedAt' | 'lastLogin' | 'isActive'>): Promise<User> {
    await this.simulateDelay();

    // Check if user already exists
    const existingUser = await this.getUserByEmail(userData.email);
    if (existingUser) {
      console.log(`User ${userData.email} already exists, returning existing user`);
      return existingUser;
    }

    // Create new user
    return await this.createUser(userData);
  }

  async updateUser(email: string, updates: Partial<User>): Promise<User | null> {
    await this.simulateDelay();

    // Convert User interface updates to database field names
    const dbUpdates: Record<string, string | boolean> = {};
    if (updates.name !== undefined) dbUpdates.name = updates.name;
    if (updates.role !== undefined) dbUpdates.role = updates.role;
    if (updates.organizationId !== undefined) dbUpdates.organization_id = updates.organizationId;
    if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;
    if (updates.lastLogin !== undefined) dbUpdates.last_login = updates.lastLogin;

    if (Object.keys(dbUpdates).length > 0) {
      await databaseService.query<UserRow>(
        'UPDATE users SET ' + Object.keys(dbUpdates).map(key => `${key} = ?`).join(', ') + ', updated_at = ? WHERE email = ?',
        [...Object.values(dbUpdates), new Date().toISOString(), email]
      );
    }

    // Return updated user
    return await this.getUserByEmail(email);
  }

  async updateLastLogin(email: string): Promise<void> {
    await this.updateUser(email, { lastLogin: new Date().toISOString() });
  }

  async deactivateUser(email: string): Promise<boolean> {
    const result = await this.updateUser(email, { isActive: false });
    return result !== null;
  }

  async activateUser(email: string): Promise<boolean> {
    const result = await this.updateUser(email, { isActive: true });
    return result !== null;
  }

  // Role Management
  async getUserRole(email: string): Promise<UserRole> {
    const user = await this.getUserByEmail(email);
    return user?.role || 'user';
  }

  async updateUserRole(email: string, role: UserRole): Promise<boolean> {
    const result = await this.updateUser(email, { role });
    return result !== null;
  }

  async isSystemAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'system_admin';
  }

  async isChurchAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'church_admin';
  }

  async isAdmin(email: string): Promise<boolean> {
    const role = await this.getUserRole(email);
    return role === 'system_admin' || role === 'church_admin';
  }

  // Organization Management
  async getAllOrganizations(): Promise<Organization[]> {
    await this.simulateDelay();
    const rows = await databaseService.query<OrganizationRow>('SELECT * FROM organizations ORDER BY created_at DESC');
    return rows.map(row => this.organizationRowToOrganization(row));
  }

  async getOrganizationById(id: string): Promise<Organization | null> {
    await this.simulateDelay();
    const rows = await databaseService.query<OrganizationRow>('SELECT * FROM organizations WHERE id = ?', [id]);
    return rows[0] ? this.organizationRowToOrganization(rows[0]) : null;
  }

  async createOrganization(orgData: Omit<Organization, 'id' | 'createdAt' | 'updatedAt' | 'isActive'>): Promise<Organization> {
    await this.simulateDelay();

    const orgRow = await databaseService.create<OrganizationRow>('organizations', {
      name: orgData.name,
      type: orgData.type,
      description: orgData.description,
      admin_email: orgData.adminEmail,
      settings: JSON.stringify(orgData.settings),
      is_active: true
    });

    return this.organizationRowToOrganization(orgRow);
  }

  async updateOrganization(id: string, updates: Partial<Organization>): Promise<Organization | null> {
    await this.simulateDelay();

    // Convert Organization interface updates to database field names
    const dbUpdates: Record<string, string | boolean> = {};
    if (updates.name !== undefined) dbUpdates.name = updates.name;
    if (updates.type !== undefined) dbUpdates.type = updates.type;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.adminEmail !== undefined) dbUpdates.admin_email = updates.adminEmail;
    if (updates.settings !== undefined) dbUpdates.settings = JSON.stringify(updates.settings);
    if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;

    if (Object.keys(dbUpdates).length > 0) {
      await databaseService.query<OrganizationRow>(
        'UPDATE organizations SET ' + Object.keys(dbUpdates).map(key => `${key} = ?`).join(', ') + ', updated_at = ? WHERE id = ?',
        [...Object.values(dbUpdates), new Date().toISOString(), id]
      );
    }

    // Return updated organization
    return await this.getOrganizationById(id);
  }

  // Initialize default admin users
  async initializeDefaultAdmins(): Promise<void> {
    await this.simulateDelay();

    // Create default system admin if doesn't exist
    const systemAdminEmail = '<EMAIL>';
    const systemAdminPassword = 'admin123'; // In production, use secure password

    try {
      await this.createUserIfNotExists({
        email: systemAdminEmail,
        name: 'System Administrator',
        role: 'system_admin',
      });

      // Add to password database (this handles duplicates internally)
      await this.addToPasswordDB(systemAdminEmail, systemAdminPassword);
      console.log('✅ System admin ready:', systemAdminEmail);
      console.log('🔑 Default password:', systemAdminPassword);
    } catch (error) {
      console.error('❌ Failed to initialize system admin:', error);
    }

    // Create default church admin if doesn't exist
    const churchAdminEmail = '<EMAIL>';
    const churchAdminPassword = 'church123'; // In production, use secure password

    try {
      // First create a default church organization
      const existingOrgs = await this.getAllOrganizations();
      let churchOrg = existingOrgs.find(org => org.type === 'church');

      if (!churchOrg) {
        churchOrg = await this.createOrganization({
          name: 'Sample Community Church',
          type: 'church',
          description: 'A sample church organization for birthday management',
          adminEmail: churchAdminEmail,
          settings: {
            allowMemberSelfRegistration: false,
            requireAdminApproval: true,
            defaultContactCategory: 'Members',
            enableAnniversaries: true,
            enableLandmarkBirthdays: true,
            timezone: 'America/New_York',
            emailNotifications: true,
          },
        });
        console.log('✅ Created church organization:', churchOrg.name);
      }

      // Create church admin user
      await this.createUserIfNotExists({
        email: churchAdminEmail,
        name: 'Church Administrator',
        role: 'church_admin',
        organizationId: churchOrg.id,
      });

      // Add to password database
      await this.addToPasswordDB(churchAdminEmail, churchAdminPassword);
      console.log('✅ Church admin ready:', churchAdminEmail);
      console.log('🔑 Default password:', churchAdminPassword);
      console.log('✅ Church organization ready:', churchOrg.name);
    } catch (error) {
      console.error('❌ Failed to initialize church admin:', error);
    }
  }

  // Helper method to add users to password database
  private async addToPasswordDB(email: string, password: string): Promise<void> {
    // Simple password hashing (in production, use proper bcrypt)
    const hashPassword = async (password: string): Promise<string> => {
      const encoder = new TextEncoder();
      const data = encoder.encode(password);
      const hash = await crypto.subtle.digest('SHA-256', data);
      return Array.from(new Uint8Array(hash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    };

    const hashedPassword = await hashPassword(password);

    try {
      // Check if user auth already exists
      const existingAuth = await databaseService.query('SELECT * FROM user_auth WHERE email = ?', [email]);

      if (existingAuth.length === 0) {
        await databaseService.create('user_auth', {
          email,
          password_hash: hashedPassword
        });
        console.log(`✅ Password set for user: ${email}`);
      } else {
        console.log(`ℹ️ Password already exists for user: ${email}`);
      }
    } catch (error) {
      console.error(`❌ Failed to set password for ${email}:`, error);
      // Don't throw - this is not critical
    }
  }

  // Permission checks
  async canManageUsers(userEmail: string): Promise<boolean> {
    return await this.isSystemAdmin(userEmail);
  }

  async canManageOrganization(userEmail: string, organizationId?: string): Promise<boolean> {
    const user = await this.getUserByEmail(userEmail);
    if (!user) return false;

    // System admins can manage all organizations
    if (user.role === 'system_admin') return true;

    // Church admins can only manage their own organization
    if (user.role === 'church_admin' && organizationId) {
      return user.organizationId === organizationId;
    }

    return false;
  }

  async canAccessAdminDashboard(userEmail: string): Promise<boolean> {
    return await this.isAdmin(userEmail);
  }

  // Get users by organization (for church admins)
  async getUsersByOrganization(organizationId: string): Promise<User[]> {
    await this.simulateDelay();
    const users = await this.getAllUsers();
    return users.filter(user => user.organizationId === organizationId);
  }
}

export const userManagementService = new UserManagementService();
