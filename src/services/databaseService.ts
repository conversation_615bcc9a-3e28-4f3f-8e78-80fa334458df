import initSqlJs, { Database } from 'sql.js';
import { v4 as uuidv4 } from 'uuid';

// Database service using SQLite (sql.js) for browser-based storage
export class DatabaseService {
  private db: Database | null = null;
  private currentUserId: string | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize sql.js
      const SQL = await initSqlJs({
        // You can specify the path to the wasm file if needed
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`
      });

      // Try to load existing database from localStorage
      const savedDb = localStorage.getItem('wewish_sqlite_db');
      if (savedDb) {
        // Load existing database
        const uint8Array = new Uint8Array(JSON.parse(savedDb));
        this.db = new SQL.Database(uint8Array);
        console.log('✅ Loaded existing SQLite database from localStorage');
      } else {
        // Create new database
        this.db = new SQL.Database();
        console.log('✅ Created new SQLite database');
      }

      // Create tables if they don't exist
      await this.createTables();
      
      // Save database to localStorage
      await this.saveDatabase();
      
      this.isInitialized = true;
      console.log('✅ Database service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize database:', error);
      throw new Error('Database initialization failed');
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tables = [
      // Users table with role-based access control
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT,
        role TEXT DEFAULT 'user' CHECK (role IN ('system_admin', 'church_admin', 'user')),
        organization_id TEXT,
        is_active BOOLEAN DEFAULT 1,
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (organization_id) REFERENCES organizations (id) ON DELETE SET NULL
      )`,

      // Organizations table for multi-tenant support
      `CREATE TABLE IF NOT EXISTS organizations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT DEFAULT 'other' CHECK (type IN ('church', 'business', 'family', 'other')),
        description TEXT,
        admin_email TEXT NOT NULL,
        settings TEXT, -- JSON object for organization settings
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contacts table
      `CREATE TABLE IF NOT EXISTS contacts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        birthday TEXT, -- ISO date string yyyy-mm-dd
        category TEXT CHECK (category IN ('Family', 'Friends', 'Colleagues', 'Clients', 'Members')),
        confirmed BOOLEAN DEFAULT 0,
        anniversary_date TEXT, -- ISO date string yyyy-mm-dd
        anniversary_type TEXT CHECK (anniversary_type IN ('Wedding', 'Dating', 'Business', 'Engagement', 'Other')),
        partner_name TEXT,
        anniversary_notes TEXT,
        email TEXT,
        phone TEXT,
        notes TEXT,
        tags TEXT, -- JSON array of tags
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Gift history table
      `CREATE TABLE IF NOT EXISTS gift_history (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        gift_name TEXT NOT NULL,
        recipient TEXT NOT NULL,
        recipient_id TEXT NOT NULL,
        date TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (recipient_id) REFERENCES contacts (id) ON DELETE CASCADE
      )`,

      // Gift reminders table
      `CREATE TABLE IF NOT EXISTS gift_reminders (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        contact_id TEXT NOT NULL,
        contact_name TEXT NOT NULL,
        reminder_date TEXT NOT NULL,
        event_date TEXT NOT NULL,
        gift_ideas TEXT, -- JSON array of gift ideas
        budget REAL,
        notes TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE CASCADE
      )`,

      // Reminders table
      `CREATE TABLE IF NOT EXISTS reminders (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        contact_id TEXT NOT NULL,
        reminder_date TEXT NOT NULL,
        type TEXT CHECK (type IN ('birthday', 'anniversary', 'custom')) NOT NULL,
        message TEXT,
        is_sent BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE CASCADE
      )`,

      // Message templates table
      `CREATE TABLE IF NOT EXISTS message_templates (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT CHECK (type IN ('birthday', 'anniversary', 'landmark', 'reminder', 'weekly_admin')) NOT NULL,
        variables TEXT, -- JSON array of available variables
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Notification settings table
      `CREATE TABLE IF NOT EXISTS notification_settings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL UNIQUE,
        reminder_enabled BOOLEAN DEFAULT 0,
        email_notifications BOOLEAN DEFAULT 1,
        push_notifications BOOLEAN DEFAULT 0,
        reminder_days_before INTEGER DEFAULT 7,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User authentication table (for password storage)
      `CREATE TABLE IF NOT EXISTS user_auth (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    // Execute table creation
    for (const tableSQL of tables) {
      this.db.exec(tableSQL);
    }

    // Create indexes for performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users (role)',
      'CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users (organization_id)',
      'CREATE INDEX IF NOT EXISTS idx_organizations_admin_email ON organizations (admin_email)',
      'CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON contacts (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_contacts_birthday ON contacts (birthday)',
      'CREATE INDEX IF NOT EXISTS idx_contacts_anniversary_date ON contacts (anniversary_date)',
      'CREATE INDEX IF NOT EXISTS idx_gift_history_user_id ON gift_history (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_gift_reminders_user_id ON gift_reminders (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_reminders_user_id ON reminders (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_message_templates_user_id ON message_templates (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_auth_email ON user_auth (email)'
    ];

    for (const indexSQL of indexes) {
      this.db.exec(indexSQL);
    }

    console.log('✅ Database tables and indexes created successfully');
  }

  async saveDatabase(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Export database to Uint8Array
      const data = this.db.export();
      // Convert to JSON string for localStorage
      const jsonString = JSON.stringify(Array.from(data));
      localStorage.setItem('wewish_sqlite_db', jsonString);
    } catch (error) {
      console.error('❌ Failed to save database:', error);
    }
  }

  setCurrentUser(userId: string): void {
    this.currentUserId = userId;
  }

  getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  // Generic CRUD operations
  async create<T extends Record<string, any>>(
    table: string, 
    data: Omit<T, 'id' | 'created_at' | 'updated_at'>
  ): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    const id = uuidv4();
    const now = new Date().toISOString();
    
    const record = {
      ...data,
      id,
      created_at: now,
      updated_at: now,
    } as T;

    // Build INSERT statement
    const columns = Object.keys(record);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(record);

    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
    
    try {
      this.db.run(sql, values);
      await this.saveDatabase();
      return record;
    } catch (error) {
      console.error(`❌ Failed to create record in ${table}:`, error);
      throw error;
    }
  }

  async getById<T>(table: string, id: string): Promise<T | null> {
    if (!this.db) throw new Error('Database not initialized');

    const sql = `SELECT * FROM ${table} WHERE id = ?`;
    const stmt = this.db.prepare(sql);
    const result = stmt.getAsObject([id]);
    
    return result ? (result as T) : null;
  }

  async getAll<T>(table: string, userId?: string): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    let sql = `SELECT * FROM ${table}`;
    let params: any[] = [];

    // Add user filter for user-scoped tables
    if (userId && this.isUserScopedTable(table)) {
      sql += ' WHERE user_id = ?';
      params = [userId];
    }

    sql += ' ORDER BY created_at DESC';

    const stmt = this.db.prepare(sql);
    const results = stmt.getAsObject(params);
    
    return Array.isArray(results) ? results as T[] : (results ? [results as T] : []);
  }

  private isUserScopedTable(table: string): boolean {
    const userScopedTables = [
      'contacts', 'gift_history', 'gift_reminders', 
      'reminders', 'message_templates', 'notification_settings'
    ];
    return userScopedTables.includes(table);
  }

  async update<T extends Record<string, any>>(
    table: string, 
    id: string, 
    updates: Partial<T>
  ): Promise<T | null> {
    if (!this.db) throw new Error('Database not initialized');

    const updatedData = {
      ...updates,
      updated_at: new Date().toISOString(),
    };

    const columns = Object.keys(updatedData);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const values = [...Object.values(updatedData), id];

    const sql = `UPDATE ${table} SET ${setClause} WHERE id = ?`;
    
    try {
      this.db.run(sql, values);
      await this.saveDatabase();
      return await this.getById<T>(table, id);
    } catch (error) {
      console.error(`❌ Failed to update record in ${table}:`, error);
      throw error;
    }
  }

  async delete(table: string, id: string): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized');

    const sql = `DELETE FROM ${table} WHERE id = ?`;
    
    try {
      const result = this.db.run(sql, [id]);
      await this.saveDatabase();
      return true; // sql.js doesn't return affected rows count easily
    } catch (error) {
      console.error(`❌ Failed to delete record from ${table}:`, error);
      return false;
    }
  }

  // Query operations
  async query<T>(sql: string, params: any[] = []): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const stmt = this.db.prepare(sql);
      const results = stmt.getAsObject(params);
      return Array.isArray(results) ? results as T[] : (results ? [results as T] : []);
    } catch (error) {
      console.error('❌ Query failed:', error);
      throw error;
    }
  }

  // Utility methods
  async clearUserData(userId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const userTables = [
      'contacts', 'gift_history', 'gift_reminders', 
      'reminders', 'message_templates', 'notification_settings'
    ];

    for (const table of userTables) {
      this.db.run(`DELETE FROM ${table} WHERE user_id = ?`, [userId]);
    }

    await this.saveDatabase();
    console.log(`✅ Cleared all data for user: ${userId}`);
  }

  async getStats(userId?: string): Promise<Record<string, number>> {
    if (!this.db) throw new Error('Database not initialized');

    const stats: Record<string, number> = {};
    const tables = ['contacts', 'gift_history', 'gift_reminders', 'reminders', 'message_templates'];

    for (const table of tables) {
      let sql = `SELECT COUNT(*) as count FROM ${table}`;
      let params: any[] = [];

      if (userId && this.isUserScopedTable(table)) {
        sql += ' WHERE user_id = ?';
        params = [userId];
      }

      const result = await this.query<{ count: number }>(sql, params);
      stats[table] = result[0]?.count || 0;
    }

    return stats;
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
export default databaseService;
