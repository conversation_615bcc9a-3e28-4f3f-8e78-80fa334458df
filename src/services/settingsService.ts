// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  reminderTiming: {
    birthday: number; // days before
    anniversary: number; // days before
    custom: number; // days before
  };
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string; // HH:MM format
  };
  frequency: {
    birthday: 'once' | 'daily' | 'weekly';
    anniversary: 'once' | 'daily' | 'weekly';
    custom: 'once' | 'daily' | 'weekly';
  };
  autoMarkComplete: boolean;
  weeklyDigest: boolean;
  landmarkBirthdays: boolean;
}

// Default settings
const defaultSettings: NotificationSettings = {
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  reminderTiming: {
    birthday: 1, // 1 day before
    anniversary: 3, // 3 days before
    custom: 1, // 1 day before
  },
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00',
  },
  frequency: {
    birthday: 'once',
    anniversary: 'once',
    custom: 'once',
  },
  autoMarkComplete: false,
  weeklyDigest: true,
  landmarkBirthdays: true,
};

export const settingsService = {
  // Get notification settings for a user
  getNotificationSettings: async (userEmail: string): Promise<NotificationSettings> => {
    await delay(300);
    
    try {
      const saved = localStorage.getItem(`birthdaySaaSSettings_${userEmail}`);
      return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    } catch (error) {
      console.error('Error loading settings:', error);
      throw new Error('Failed to load notification settings');
    }
  },

  // Update notification settings
  updateNotificationSettings: async (
    userEmail: string, 
    settings: Partial<NotificationSettings>
  ): Promise<NotificationSettings> => {
    await delay(500);
    
    try {
      const currentSettings = await settingsService.getNotificationSettings(userEmail);
      const updatedSettings = { ...currentSettings, ...settings };
      
      localStorage.setItem(`birthdaySaaSSettings_${userEmail}`, JSON.stringify(updatedSettings));
      
      return updatedSettings;
    } catch (error) {
      console.error('Error updating settings:', error);
      throw new Error('Failed to update notification settings');
    }
  },

  // Reset settings to default
  resetNotificationSettings: async (userEmail: string): Promise<NotificationSettings> => {
    await delay(400);
    
    try {
      localStorage.setItem(`birthdaySaaSSettings_${userEmail}`, JSON.stringify(defaultSettings));
      return defaultSettings;
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw new Error('Failed to reset notification settings');
    }
  },

  // Update a specific setting
  updateSetting: async <K extends keyof NotificationSettings>(
    userEmail: string,
    key: K,
    value: NotificationSettings[K]
  ): Promise<NotificationSettings> => {
    await delay(300);
    
    try {
      const currentSettings = await settingsService.getNotificationSettings(userEmail);
      const updatedSettings = { ...currentSettings, [key]: value };
      
      localStorage.setItem(`birthdaySaaSSettings_${userEmail}`, JSON.stringify(updatedSettings));
      
      return updatedSettings;
    } catch (error) {
      console.error('Error updating setting:', error);
      throw new Error('Failed to update setting');
    }
  },

  // Bulk update settings
  bulkUpdateSettings: async (
    userEmail: string,
    updates: Partial<NotificationSettings>
  ): Promise<NotificationSettings> => {
    await delay(600);
    
    try {
      const currentSettings = await settingsService.getNotificationSettings(userEmail);
      const updatedSettings = { ...currentSettings, ...updates };
      
      localStorage.setItem(`birthdaySaaSSettings_${userEmail}`, JSON.stringify(updatedSettings));
      
      return updatedSettings;
    } catch (error) {
      console.error('Error bulk updating settings:', error);
      throw new Error('Failed to bulk update settings');
    }
  },

  // Export settings
  exportSettings: async (userEmail: string): Promise<string> => {
    await delay(200);
    
    try {
      const settings = await settingsService.getNotificationSettings(userEmail);
      return JSON.stringify(settings, null, 2);
    } catch (error) {
      console.error('Error exporting settings:', error);
      throw new Error('Failed to export settings');
    }
  },

  // Import settings
  importSettings: async (userEmail: string, settingsJson: string): Promise<NotificationSettings> => {
    await delay(500);
    
    try {
      const importedSettings = JSON.parse(settingsJson);
      const validatedSettings = { ...defaultSettings, ...importedSettings };
      
      localStorage.setItem(`birthdaySaaSSettings_${userEmail}`, JSON.stringify(validatedSettings));
      
      return validatedSettings;
    } catch (error) {
      console.error('Error importing settings:', error);
      throw new Error('Failed to import settings - invalid format');
    }
  }
};
