import { databaseService } from './databaseService';
import { v4 as uuidv4 } from 'uuid';

export interface UsageRecord {
  id: string;
  user_id: string;
  feature_key: string;
  usage_count: number;
  last_used: string;
  created_at: string;
  updated_at: string;
}

export class UsageTrackingService {
  private currentUserId: string | null = null;

  setCurrentUser(userId: string): void {
    this.currentUserId = userId;
  }

  getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  // Get current usage for a feature
  async getCurrentUsage(featureKey: string): Promise<number> {
    try {
      await databaseService.initialize();
      const userId = this.getCurrentUserId();

      const result = await databaseService.query<UsageRecord>(
        'SELECT usage_count FROM usage_tracking WHERE user_id = ? AND feature_key = ?',
        [userId, featureKey]
      );

      return result.length > 0 ? result[0].usage_count : 0;
    } catch (error) {
      console.error('Error getting current usage:', error);
      return 0;
    }
  }

  // Increment usage for a feature
  async incrementUsage(featureKey: string): Promise<boolean> {
    try {
      await databaseService.initialize();
      const userId = this.getCurrentUserId();
      const now = new Date().toISOString();

      // Check if record exists
      const existing = await databaseService.query<UsageRecord>(
        'SELECT * FROM usage_tracking WHERE user_id = ? AND feature_key = ?',
        [userId, featureKey]
      );

      if (existing.length > 0) {
        // Update existing record
        await databaseService.query(
          'UPDATE usage_tracking SET usage_count = usage_count + 1, last_used = ?, updated_at = ? WHERE user_id = ? AND feature_key = ?',
          [now, now, userId, featureKey]
        );
      } else {
        // Create new record
        const id = uuidv4();
        await databaseService.query(
          'INSERT INTO usage_tracking (id, user_id, feature_key, usage_count, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [id, userId, featureKey, 1, now, now, now]
        );
      }

      return true;
    } catch (error) {
      console.error('Error incrementing usage:', error);
      return false;
    }
  }

  // Set usage count for a feature
  async setUsage(featureKey: string, count: number): Promise<boolean> {
    try {
      await databaseService.initialize();
      const userId = this.getCurrentUserId();
      const now = new Date().toISOString();

      // Check if record exists
      const existing = await databaseService.query<UsageRecord>(
        'SELECT * FROM usage_tracking WHERE user_id = ? AND feature_key = ?',
        [userId, featureKey]
      );

      if (existing.length > 0) {
        // Update existing record
        await databaseService.query(
          'UPDATE usage_tracking SET usage_count = ?, last_used = ?, updated_at = ? WHERE user_id = ? AND feature_key = ?',
          [count, now, now, userId, featureKey]
        );
      } else {
        // Create new record
        const id = uuidv4();
        await databaseService.query(
          'INSERT INTO usage_tracking (id, user_id, feature_key, usage_count, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [id, userId, featureKey, count, now, now, now]
        );
      }

      return true;
    } catch (error) {
      console.error('Error setting usage:', error);
      return false;
    }
  }

  // Reset usage for a feature
  async resetUsage(featureKey: string): Promise<boolean> {
    return this.setUsage(featureKey, 0);
  }

  // Get all usage records for current user
  async getAllUsage(): Promise<UsageRecord[]> {
    try {
      await databaseService.initialize();
      const userId = this.getCurrentUserId();

      return await databaseService.query<UsageRecord>(
        'SELECT * FROM usage_tracking WHERE user_id = ? ORDER BY last_used DESC',
        [userId]
      );
    } catch (error) {
      console.error('Error getting all usage:', error);
      return [];
    }
  }

  // Get usage statistics
  async getUsageStats(): Promise<{
    totalFeatures: number;
    totalUsage: number;
    mostUsedFeature: string | null;
    lastActivity: string | null;
  }> {
    try {
      const allUsage = await this.getAllUsage();
      
      if (allUsage.length === 0) {
        return {
          totalFeatures: 0,
          totalUsage: 0,
          mostUsedFeature: null,
          lastActivity: null
        };
      }

      const totalUsage = allUsage.reduce((sum, record) => sum + record.usage_count, 0);
      const mostUsed = allUsage.reduce((max, record) => 
        record.usage_count > max.usage_count ? record : max
      );
      const lastActivity = allUsage.reduce((latest, record) => 
        record.last_used > latest ? record.last_used : latest, 
        allUsage[0].last_used
      );

      return {
        totalFeatures: allUsage.length,
        totalUsage,
        mostUsedFeature: mostUsed.feature_key,
        lastActivity
      };
    } catch (error) {
      console.error('Error getting usage stats:', error);
      return {
        totalFeatures: 0,
        totalUsage: 0,
        mostUsedFeature: null,
        lastActivity: null
      };
    }
  }

  // Migrate from localStorage
  async migrateFromLocalStorage(): Promise<void> {
    try {
      console.log('🔄 Migrating usage tracking from localStorage...');
      
      const userId = this.getCurrentUserId();
      let migratedCount = 0;

      // Scan localStorage for usage tracking keys
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('usage_')) {
          const featureKey = key.replace('usage_', '');
          const usageCount = parseInt(localStorage.getItem(key) || '0', 10);
          
          if (usageCount > 0) {
            await this.setUsage(featureKey, usageCount);
            migratedCount++;
            console.log(`✅ Migrated ${featureKey}: ${usageCount} uses`);
          }
        }
      }

      console.log(`✅ Migrated ${migratedCount} usage tracking records`);
    } catch (error) {
      console.error('❌ Error migrating usage tracking:', error);
    }
  }

  // Clear all usage data for current user
  async clearAllUsage(): Promise<void> {
    try {
      await databaseService.initialize();
      const userId = this.getCurrentUserId();

      await databaseService.query(
        'DELETE FROM usage_tracking WHERE user_id = ?',
        [userId]
      );

      console.log('✅ Cleared all usage tracking data');
    } catch (error) {
      console.error('Error clearing usage data:', error);
    }
  }
}

// Export singleton instance
export const usageTrackingService = new UsageTrackingService();
export default usageTrackingService;
