import { databaseService } from './databaseService';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Database row interface
interface ReminderRow {
  id: string;
  user_id: string;
  contact_id: string;
  reminder_date: string;
  type: string;
  message?: string;
  is_sent: boolean;
  created_at: string;
  updated_at: string;
}

export interface Reminder {
  id: string;
  title: string;
  person: string;
  contactId?: string;
  date: string;
  reminderDate: string;
  type: 'birthday' | 'anniversary' | 'custom';
  isActive: boolean;
  notifications: string[];
  notes?: string;
  recurring?: boolean;
}

// Mock reminder data
const defaultReminders: Reminder[] = [
  {
    id: '1',
    title: "<PERSON>'s Birthday",
    person: '<PERSON>',
    contactId: 'contact-1',
    date: '2024-03-15',
    reminderDate: '2024-03-14',
    type: 'birthday',
    isActive: true,
    notifications: ['Email', 'Push'],
    recurring: true
  },
  {
    id: '2',
    title: "<PERSON>'s Birthday",
    person: '<PERSON>',
    contactId: 'contact-2',
    date: '2024-07-22',
    reminderDate: '2024-07-21',
    type: 'birthday',
    isActive: true,
    notifications: ['Email', 'SMS'],
    recurring: true
  },
  {
    id: '3',
    title: "Wedding Anniversary",
    person: '<PERSON> & <PERSON>',
    contactId: 'contact-3',
    date: '2024-06-10',
    reminderDate: '2024-06-09',
    type: 'anniversary',
    isActive: true,
    notifications: ['Email'],
    recurring: true
  }
];

// Helper function to convert database row to Reminder interface
const reminderRowToReminder = (row: ReminderRow, contactName: string): Reminder => ({
  id: row.id,
  title: `${contactName}'s ${row.type}`,
  person: contactName,
  contactId: row.contact_id,
  date: row.reminder_date, // Using reminder_date as the event date
  reminderDate: row.reminder_date,
  type: row.type as Reminder['type'],
  isActive: !row.is_sent,
  notifications: ['Email'], // Default notification method
  notes: row.message,
  recurring: row.type === 'birthday' || row.type === 'anniversary'
});

export const reminderService = {
  // Get all reminders for a user
  getReminders: async (userEmail: string): Promise<Reminder[]> => {
    await delay(400);

    try {
      await databaseService.initialize();

      // Get reminders with contact names
      const reminderRows = await databaseService.query<ReminderRow & { contact_name: string; }>(
        `SELECT r.*, c.name as contact_name
         FROM reminders r
         LEFT JOIN contacts c ON r.contact_id = c.id
         WHERE r.user_id = ?
         ORDER BY r.reminder_date ASC`,
        [userEmail]
      );

      return reminderRows.map(row => reminderRowToReminder(row, row.contact_name || 'Unknown Contact'));
    } catch (error) {
      console.error('Error loading reminders:', error);
      throw new Error('Failed to load reminders');
    }
  },

  // Get a single reminder
  getReminder: async (userEmail: string, reminderId: string): Promise<Reminder | null> => {
    await delay(300);

    try {
      await databaseService.initialize();

      const reminderRows = await databaseService.query<ReminderRow & { contact_name: string; }>(
        `SELECT r.*, c.name as contact_name
         FROM reminders r
         LEFT JOIN contacts c ON r.contact_id = c.id
         WHERE r.user_id = ? AND r.id = ?`,
        [userEmail, reminderId]
      );

      if (reminderRows.length === 0) return null;

      return reminderRowToReminder(reminderRows[0], reminderRows[0].contact_name || 'Unknown Contact');
    } catch (error) {
      console.error('Error loading reminder:', error);
      throw new Error('Failed to load reminder');
    }
  },

  // Create a new reminder
  createReminder: async (userEmail: string, reminder: Omit<Reminder, 'id'>): Promise<Reminder> => {
    await delay(600);

    try {
      const reminders = await reminderService.getReminders(userEmail);
      const newReminder: Reminder = {
        ...reminder,
        id: Date.now().toString()
      };

      const updatedReminders = [...reminders, newReminder];
      localStorage.setItem(`birthdaySaaSReminders_${userEmail}`, JSON.stringify(updatedReminders));

      return newReminder;
    } catch (error) {
      console.error('Error creating reminder:', error);
      throw new Error('Failed to create reminder');
    }
  },

  // Update a reminder
  updateReminder: async (userEmail: string, reminderId: string, updates: Partial<Reminder>): Promise<Reminder> => {
    await delay(500);

    try {
      const reminders = await reminderService.getReminders(userEmail);
      const reminderIndex = reminders.findIndex(r => r.id === reminderId);

      if (reminderIndex === -1) {
        throw new Error('Reminder not found');
      }

      const updatedReminder = { ...reminders[reminderIndex], ...updates };
      const updatedReminders = [...reminders];
      updatedReminders[reminderIndex] = updatedReminder;

      localStorage.setItem(`birthdaySaaSReminders_${userEmail}`, JSON.stringify(updatedReminders));

      return updatedReminder;
    } catch (error) {
      console.error('Error updating reminder:', error);
      throw new Error('Failed to update reminder');
    }
  },

  // Delete a reminder
  deleteReminder: async (userEmail: string, reminderId: string): Promise<void> => {
    await delay(400);

    try {
      const reminders = await reminderService.getReminders(userEmail);
      const updatedReminders = reminders.filter(r => r.id !== reminderId);

      localStorage.setItem(`birthdaySaaSReminders_${userEmail}`, JSON.stringify(updatedReminders));
    } catch (error) {
      console.error('Error deleting reminder:', error);
      throw new Error('Failed to delete reminder');
    }
  },

  // Toggle reminder active status
  toggleReminder: async (userEmail: string, reminderId: string): Promise<Reminder> => {
    await delay(300);

    try {
      const reminders = await reminderService.getReminders(userEmail);
      const reminder = reminders.find(r => r.id === reminderId);

      if (!reminder) {
        throw new Error('Reminder not found');
      }

      return reminderService.updateReminder(userEmail, reminderId, { isActive: !reminder.isActive });
    } catch (error) {
      console.error('Error toggling reminder:', error);
      throw new Error('Failed to toggle reminder');
    }
  }
};
