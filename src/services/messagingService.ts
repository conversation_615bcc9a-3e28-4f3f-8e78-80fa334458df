import type {
  MessageTemplate,
  ScheduledMessage,
  WeeklyAdminNotification,
  NotificationSettings,
  MessageHistory,
  MessagingStats,
  LandmarkBirthday
} from '../types/messaging';
import type { Contact } from '../types';
import { persistenceService } from './persistenceService';
import { format, addDays, startOfWeek, endOfWeek, differenceInYears, parseISO } from 'date-fns';

// Landmark birthday definitions
export const LANDMARK_BIRTHDAYS: LandmarkBirthday[] = [
  { age: 18, name: 'Coming of Age', description: 'Legal adulthood milestone', isSpecial: true },
  { age: 21, name: 'Legal Drinking Age', description: 'Full legal rights in most countries', isSpecial: true },
  { age: 30, name: 'Thirty and Thriving', description: 'Entering a new decade of life', isSpecial: false },
  { age: 40, name: '<PERSON> Begins at Forty', description: 'Wisdom and experience milestone', isSpecial: false },
  { age: 50, name: 'Golden Jubilee', description: 'Half a century of life', isSpecial: true },
  { age: 60, name: 'Diamond Years', description: 'Entering the golden years', isSpecial: false },
  { age: 65, name: 'Retirement Milestone', description: 'Traditional retirement age', isSpecial: false },
  { age: 70, name: 'Platinum Years', description: 'Seven decades of wisdom', isSpecial: false },
  { age: 75, name: 'Diamond Anniversary', description: 'Three quarters of a century', isSpecial: true },
  { age: 80, name: 'Octogenarian', description: 'Eight decades of life', isSpecial: false },
  { age: 90, name: 'Nonagenarian', description: 'Nine decades of wisdom', isSpecial: true },
  { age: 100, name: 'Centenarian', description: 'A full century of life!', isSpecial: true },
];

class MessagingService {
  private currentUserId: string | null = null;
  private settings: NotificationSettings = {
    id: 'default_settings',
    userId: 'current_user',
    weeklyAdminNotifications: {
      enabled: true,
      dayOfWeek: 1, // Monday
      time: '09:00',
      email: '<EMAIL>',
    },
    birthdayMessages: {
      enabled: true,
      defaultTime: '06:00',
      timezone: 'UTC',
    },
    landmarkBirthdays: {
      enabled: true,
      customMessage: true,
    },
  };

  constructor() {
    this.initializeDefaults();
  }

  // Set current user for database operations
  setCurrentUser(userId: string) {
    this.currentUserId = userId;
    persistenceService.setCurrentUser(userId);
  }

  private async initializeDefaults() {
    try {
      await persistenceService.initializeDefaults();
      await this.seedDefaultTemplatesIfNeeded();
    } catch (error) {
      console.error('Failed to initialize messaging service:', error);
    }
  }

  private async seedDefaultTemplatesIfNeeded() {
    // Check if default templates already exist
    const existingTemplates = persistenceService.getAllGlobal<MessageTemplate>('default_templates');

    if (existingTemplates.length === 0) {
      // Create default templates
      const defaultTemplates = this.getDefaultTemplateData();

      for (const template of defaultTemplates) {
        await persistenceService.createGlobal<MessageTemplate>('default_templates', template);
      }

      console.log(`✅ Seeded ${defaultTemplates.length} default templates`);
    }
  }

  private getDefaultTemplateData(): Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>[] {
    return [
      {
        name: 'Standard Birthday',
        type: 'birthday',
        subject: 'Happy Birthday, {name}! 🎉',
        message: 'Dear {name},\n\nWishing you a very happy {age}th birthday! May this special day bring you joy, happiness, and wonderful memories.\n\nHave a fantastic celebration!\n\nBest wishes,\n{senderName}',
        variables: ['name', 'age', 'senderName'],
        isActive: true,
      },
      {
        name: 'Wedding Anniversary - General',
        type: 'anniversary',
        subject: 'Happy Anniversary, {name}! 💕',
        message: 'Dear {name},\n\nWishing you and {partnerName} a wonderful anniversary! May your love continue to grow stronger with each passing year.\n\nCelebrating {years} years of love and happiness!\n\nWith warm wishes,\n{senderName}',
        variables: ['name', 'partnerName', 'years', 'senderName'],
        isActive: true,
      },
      {
        name: 'Wedding Anniversary - Milestone',
        type: 'anniversary',
        subject: 'Congratulations on {years} Amazing Years! 🎊',
        message: 'Dear {name} and {partnerName},\n\nWhat an incredible milestone - {years} years of marriage! Your love story continues to inspire everyone around you.\n\nMay this special anniversary be filled with joy, reflection on beautiful memories, and excitement for the years ahead.\n\nCheers to your enduring love!\n\nWith heartfelt congratulations,\n{senderName}',
        variables: ['name', 'partnerName', 'years', 'senderName'],
        isActive: true,
      },
      {
        name: 'Dating Anniversary',
        type: 'anniversary',
        subject: 'Happy Dating Anniversary, {name}! 💕',
        message: 'Dear {name},\n\nCelebrating {years} wonderful years together! From your first date to now, watching your relationship bloom has been such a joy.\n\nHere\'s to many more years of love, laughter, and beautiful memories with {partnerName}!\n\nWith love and best wishes,\n{senderName}',
        variables: ['name', 'partnerName', 'years', 'senderName'],
        isActive: true,
      },
      {
        name: 'Business Anniversary',
        type: 'anniversary',
        subject: 'Congratulations on {years} Years in Business! 🏢',
        message: 'Dear {name},\n\nCongratulations on reaching this significant milestone - {years} years in business! Your dedication, hard work, and vision have built something truly remarkable.\n\nHere\'s to continued success and many more years of growth and achievement!\n\nWith admiration and best wishes,\n{senderName}',
        variables: ['name', 'years', 'senderName'],
        isActive: true,
      },
      {
        name: 'Landmark Birthday - General',
        type: 'landmark',
        subject: 'A Very Special {age}th Birthday! 🎊',
        message: 'Dear {name},\n\nWhat a milestone - your {age}th birthday! This is such a special age and deserves an extra special celebration.\n\n{landmarkMessage}\n\nWishing you a day filled with joy, surrounded by loved ones, and a year ahead full of wonderful adventures!\n\nWith warmest birthday wishes,\n{senderName}',
        variables: ['name', 'age', 'landmarkMessage', 'senderName'],
        isActive: true,
      },
      {
        name: 'Coming of Age - 18th Birthday',
        type: 'landmark',
        subject: 'Welcome to Adulthood, {name}! 🗝️',
        message: 'Dear {name},\n\nHappy 18th Birthday! Today marks your official entry into adulthood - what an exciting milestone! You now have all the rights and responsibilities that come with being a legal adult.\n\nThis is the beginning of a new chapter filled with independence, opportunities, and adventures. May this special day be the start of an amazing journey ahead!\n\nCongratulations on reaching this important milestone!\n\nWith warm wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Legal Drinking Age - 21st Birthday',
        type: 'landmark',
        subject: 'Cheers to 21 Years, {name}! 🍾',
        message: 'Dear {name},\n\nHappy 21st Birthday! You\'ve reached the age where you can legally celebrate with a toast in most places around the world. This milestone represents full legal adulthood and all the freedoms that come with it.\n\nMay this special birthday be filled with joy, celebration, and the excitement of new possibilities ahead. Here\'s to making the most of this incredible time in your life!\n\nCheers to you and many more wonderful years!\n\nWith celebration wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Thirty and Thriving - 30th Birthday',
        type: 'landmark',
        subject: 'Welcome to Your Thirties, {name}! 🎈',
        message: 'Dear {name},\n\nHappy 30th Birthday! Welcome to a new decade of life - your thirties! This is often considered the time when life really begins to take shape, with wisdom gained from your twenties and exciting opportunities ahead.\n\nMay this milestone birthday mark the beginning of your most fulfilling decade yet, filled with personal growth, meaningful relationships, and achieving your dreams.\n\nHere\'s to thirty years of amazing memories and many more to come!\n\nWith warm wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Life Begins at Forty - 40th Birthday',
        type: 'landmark',
        subject: 'Life Begins at 40, {name}! 🌟',
        message: 'Dear {name},\n\nHappy 40th Birthday! They say life begins at forty, and what a perfect time to celebrate all you\'ve accomplished and look forward to all the adventures still to come.\n\nYour forties are a time of wisdom, confidence, and knowing yourself better than ever. May this decade bring you joy, success, and the fulfillment of your deepest aspirations.\n\nCelebrating four decades of your wonderful life!\n\nWith admiration and best wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Golden Jubilee - 50th Birthday',
        type: 'landmark',
        subject: 'Golden 50th Birthday, {name}! 🎯',
        message: 'Dear {name},\n\nHappy 50th Birthday! What a golden milestone - half a century of life, love, and incredible experiences! Your fifties represent the perfect blend of wisdom, experience, and vitality.\n\nThis is your golden jubilee, a time to celebrate all you\'ve achieved and embrace the exciting possibilities that lie ahead. May this special birthday be the beginning of your most golden years yet!\n\nCelebrating 50 amazing years of you!\n\nWith heartfelt congratulations,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Diamond Years - 60th Birthday',
        type: 'landmark',
        subject: 'Diamond 60th Birthday, {name}! 💎',
        message: 'Dear {name},\n\nHappy 60th Birthday! Welcome to your diamond years - a time that sparkles with wisdom, experience, and the beauty of a life well-lived. Six decades of memories, achievements, and relationships that have shaped who you are today.\n\nMay this milestone birthday be celebrated with joy and surrounded by all the love you\'ve given to others throughout your remarkable journey.\n\nCelebrating 60 brilliant years of you!\n\nWith deep respect and warm wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Retirement Milestone - 65th Birthday',
        type: 'landmark',
        subject: 'Celebrating 65 Years, {name}! 🏆',
        message: 'Dear {name},\n\nHappy 65th Birthday! This traditional retirement milestone represents not just the end of one chapter, but the exciting beginning of another. You\'ve earned the right to enjoy the fruits of your labor and pursue the dreams you\'ve been saving for this time.\n\nMay this new phase of life be filled with relaxation, adventure, and all the things that bring you the greatest joy.\n\nCongratulations on reaching this wonderful milestone!\n\nWith celebration and best wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Platinum Years - 70th Birthday',
        type: 'landmark',
        subject: 'Platinum 70th Birthday, {name}! 🌟',
        message: 'Dear {name},\n\nHappy 70th Birthday! Seven decades of life - what a platinum achievement! Your seventies represent the culmination of wisdom, the richness of experience, and the precious value of a life filled with meaning.\n\nLike platinum, you are rare, valuable, and enduring. May this special birthday be celebrated with all the honor and joy you deserve.\n\nCelebrating seven magnificent decades of your life!\n\nWith profound respect and warm wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Diamond Anniversary - 75th Birthday',
        type: 'landmark',
        subject: 'Diamond 75th Birthday, {name}! 💎',
        message: 'Dear {name},\n\nHappy 75th Birthday! Three quarters of a century - what a diamond anniversary of life! Your 75 years represent a treasure trove of experiences, wisdom, and the countless lives you\'ve touched along the way.\n\nLike a diamond, you have been shaped by time and pressure into something truly precious and brilliant. May this special milestone be celebrated with all the sparkle and joy you bring to others.\n\nCelebrating 75 magnificent years of your extraordinary life!\n\nWith deep admiration and warmest wishes,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Octogenarian - 80th Birthday',
        type: 'landmark',
        subject: 'Magnificent 80th Birthday, {name}! 🏆',
        message: 'Dear {name},\n\nHappy 80th Birthday! Welcome to your eighties - eight decades of an incredible journey! As an octogenarian, you represent the pinnacle of wisdom, grace, and the beauty of a life richly lived.\n\nEighty years of memories, love, laughter, and the legacy you\'ve created for future generations. May this milestone birthday be filled with joy and surrounded by all who love and cherish you.\n\nCelebrating eight amazing decades of you!\n\nWith profound respect and love,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Nonagenarian - 90th Birthday',
        type: 'landmark',
        subject: 'Extraordinary 90th Birthday, {name}! 🎊',
        message: 'Dear {name},\n\nHappy 90th Birthday! Nine decades of life - what an extraordinary achievement! As a nonagenarian, you are a living treasure, a keeper of history, and an inspiration to all who know you.\n\nNinety years of witnessing the world change, of loving and being loved, of creating memories that will last for generations. You are truly remarkable, and this milestone deserves the grandest of celebrations.\n\nCelebrating nine incredible decades of your amazing life!\n\nWith awe, respect, and deepest love,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Centenarian - 100th Birthday',
        type: 'landmark',
        subject: 'Historic 100th Birthday, {name}! 🎉',
        message: 'Dear {name},\n\nHappy 100th Birthday! A full century of life - what a monumental, historic achievement! As a centenarian, you have reached a milestone that few ever experience. You are a living piece of history, having witnessed an entire century of human progress.\n\nOne hundred years of love, wisdom, joy, and the incredible impact you\'ve had on countless lives. This is not just a birthday - it\'s a celebration of a legendary life that will be remembered for generations to come.\n\nCelebrating one hundred extraordinary years of your remarkable life!\n\nWith the deepest honor, respect, and love,\n{senderName}',
        variables: ['name', 'senderName'],
        isActive: true,
      },
      {
        name: 'Weekly Admin Notification',
        type: 'weekly_admin',
        subject: 'Weekly Birthday & Anniversary Report - {weekStart} to {weekEnd}',
        message: 'Weekly Summary:\n\nUpcoming Birthdays & Anniversaries ({upcomingCount} total):\n{upcomingList}\n\nLandmark Celebrations:\n{landmarkList}\n\nGenerated on {generatedDate}',
        variables: ['weekStart', 'weekEnd', 'upcomingCount', 'upcomingList', 'landmarkList', 'generatedDate'],
        isActive: true,
      },
    ];
  }

  private async simulateDelay(ms: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  // Template Management
  async getTemplates(): Promise<MessageTemplate[]> {
    await this.simulateDelay();

    // Get user templates and default templates
    const userTemplates = persistenceService.getAll<MessageTemplate>('message_templates');
    const defaultTemplates = persistenceService.getAllGlobal<MessageTemplate>('default_templates');

    // Combine and return all templates
    return [...defaultTemplates, ...userTemplates];
  }

  async getTemplateById(id: string): Promise<MessageTemplate | null> {
    await this.simulateDelay();

    // Check user templates first
    let template = await persistenceService.getById<MessageTemplate>('message_templates', id);

    // If not found, check default templates
    if (!template) {
      template = await persistenceService.getByIdGlobal<MessageTemplate>('default_templates', id);
    }

    return template;
  }

  async createTemplate(templateData: Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<MessageTemplate> {
    await this.simulateDelay();
    return await persistenceService.create<MessageTemplate>('message_templates', templateData);
  }

  async updateTemplate(id: string, updates: Partial<MessageTemplate>): Promise<MessageTemplate> {
    await this.simulateDelay();

    const updatedTemplate = await persistenceService.update<MessageTemplate>('message_templates', id, updates);

    if (!updatedTemplate) {
      throw new Error('Template not found');
    }

    return updatedTemplate;
  }

  async deleteTemplate(id: string): Promise<void> {
    await this.simulateDelay();
    const success = await persistenceService.delete<MessageTemplate>('message_templates', id);
    if (!success) {
      throw new Error('Template not found');
    }
  }

  // Message History
  async getMessageHistory(limit: number = 50): Promise<MessageHistory[]> {
    await this.simulateDelay();
    const allHistory = persistenceService.getAll<MessageHistory>('message_history');

    // Sort by sentAt descending and limit
    return allHistory
      .sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())
      .slice(0, limit);
  }

  async addMessageToHistory(messageData: Omit<MessageHistory, 'id' | 'sentAt'>): Promise<MessageHistory> {
    await this.simulateDelay();

    const messageWithTimestamp = {
      ...messageData,
      sentAt: new Date().toISOString(),
    };

    return await persistenceService.create<MessageHistory>('message_history', messageWithTimestamp);
  }

  // Scheduled Messages
  async getScheduledMessages(): Promise<ScheduledMessage[]> {
    await this.simulateDelay();
    return persistenceService.getAll<ScheduledMessage>('scheduled_messages');
  }

  async scheduleMessage(messageData: Omit<ScheduledMessage, 'id' | 'createdAt'>): Promise<ScheduledMessage> {
    await this.simulateDelay();
    return await persistenceService.create<ScheduledMessage>('scheduled_messages', messageData);
  }

  async updateScheduledMessage(id: string, updates: Partial<ScheduledMessage>): Promise<ScheduledMessage> {
    await this.simulateDelay();

    const updatedMessage = await persistenceService.update<ScheduledMessage>('scheduled_messages', id, updates);

    if (!updatedMessage) {
      throw new Error('Scheduled message not found');
    }

    return updatedMessage;
  }

  async deleteScheduledMessage(id: string): Promise<void> {
    await this.simulateDelay();
    const success = await persistenceService.delete<ScheduledMessage>('scheduled_messages', id);
    if (!success) {
      throw new Error('Scheduled message not found');
    }
  }

  // Settings Management
  async getSettings(): Promise<NotificationSettings> {
    await this.simulateDelay();
    return { ...this.settings };
  }

  async updateSettings(updates: Partial<NotificationSettings>): Promise<NotificationSettings> {
    await this.simulateDelay();
    this.settings = {
      ...this.settings,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    return { ...this.settings };
  }

  // Admin method to reset templates to defaults
  async resetToDefaultTemplates(): Promise<void> {
    await this.simulateDelay();
    await this.seedDefaultTemplatesIfNeeded();
  }

  // Admin method to clean up storage
  async cleanupStorage(): Promise<{ removedDuplicates: number; totalTemplates: number; }> {
    await this.simulateDelay();
    const templates = persistenceService.getAll<MessageTemplate>('message_templates');

    // For now, just return current count (actual deduplication would be more complex)
    return {
      removedDuplicates: 0,
      totalTemplates: templates.length
    };
  }

  // Utility methods for birthday/anniversary processing
  getLandmarkBirthdays(): LandmarkBirthday[] {
    return [...LANDMARK_BIRTHDAYS];
  }

  getLandmarkBirthday(age: number): LandmarkBirthday | null {
    return LANDMARK_BIRTHDAYS.find(landmark => landmark.age === age) || null;
  }

  isLandmarkBirthday(age: number): LandmarkBirthday | null {
    return LANDMARK_BIRTHDAYS.find(landmark => landmark.age === age) || null;
  }

  calculateAge(birthDate: string): number {
    return differenceInYears(new Date(), parseISO(birthDate));
  }

  // Get specific landmark template for age
  async getLandmarkTemplateForAge(age: number): Promise<MessageTemplate | null> {
    const templates = await this.getTemplates();

    // Map ages to specific template names
    const ageTemplateMap: Record<number, string> = {
      18: 'Coming of Age - 18th Birthday',
      21: 'Legal Drinking Age - 21st Birthday',
      30: 'Thirty and Thriving - 30th Birthday',
      40: 'Life Begins at Forty - 40th Birthday',
      50: 'Golden Jubilee - 50th Birthday',
      60: 'Diamond Years - 60th Birthday',
      65: 'Retirement Milestone - 65th Birthday',
      70: 'Platinum Years - 70th Birthday',
      75: 'Diamond Anniversary - 75th Birthday',
      80: 'Octogenarian - 80th Birthday',
      90: 'Nonagenarian - 90th Birthday',
      100: 'Centenarian - 100th Birthday',
    };

    const templateName = ageTemplateMap[age];
    if (templateName) {
      const specificTemplate = templates.find(t => t.name === templateName && t.isActive);
      if (specificTemplate) {
        return specificTemplate;
      }
    }

    // Fallback to general landmark template
    return templates.find(t => t.name === 'Landmark Birthday - General' && t.isActive) || null;
  }

  // Message sending simulation
  async sendMessage(
    contactId: string,
    templateId: string,
    variables: Record<string, string> = {},
    deliveryMethod: 'email' | 'sms' = 'email'
  ): Promise<void> {
    await this.simulateDelay(1000); // Simulate network delay

    const template = await this.getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Replace variables in template
    let subject = template.subject;
    let message = template.message;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      subject = subject.replace(new RegExp(placeholder, 'g'), value);
      message = message.replace(new RegExp(placeholder, 'g'), value);
    });

    // Add to message history
    await this.addMessageToHistory({
      contactId,
      templateId,
      type: template.type,
      subject,
      message,
      status: 'sent',
      deliveryMethod,
    });

    // Simulate sending (in real app, integrate with email/SMS service)
    console.log(`📧 Message sent via ${deliveryMethod}:`, { subject, message });
  }

  // Statistics
  async getStats(): Promise<MessagingStats> {
    await this.simulateDelay();
    const messageHistory = persistenceService.getAll<MessageHistory>('message_history');

    const totalSent = messageHistory.filter(m => m.status === 'sent').length;
    const totalFailed = messageHistory.filter(m => m.status === 'failed').length;
    const birthdayMessages = messageHistory.filter(m => m.type === 'birthday').length;
    const landmarkMessages = messageHistory.filter(m => m.type === 'landmark').length;
    const weeklyMessages = messageHistory.filter(m => m.type === 'weekly_admin').length;

    return {
      totalMessagesSent: totalSent,
      birthdayMessagesSent: birthdayMessages,
      landmarkMessagesSent: landmarkMessages,
      weeklyNotificationsSent: weeklyMessages,
      failedMessages: totalFailed,
      successRate: totalSent + totalFailed > 0 ? (totalSent / (totalSent + totalFailed)) * 100 : 0,
      lastWeekStats: {
        sent: totalSent, // Would need date filtering for actual last week
        failed: totalFailed,
      },
    };
  }

  // Birthday Message Processing
  async processBirthdayMessages(contacts: any[]): Promise<void> {
    await this.simulateDelay();

    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    for (const contact of contacts) {
      if (!contact.birthday) continue;

      // Check if today is their birthday
      const birthdayDate = new Date(contact.birthday);
      const birthdayThisYear = new Date(today.getFullYear(), birthdayDate.getMonth(), birthdayDate.getDate());

      if (birthdayThisYear.toISOString().split('T')[0] === todayString) {
        // Calculate age
        const age = today.getFullYear() - birthdayDate.getFullYear();

        // Check if it's a landmark birthday
        const landmark = this.isLandmarkBirthday(age);

        // Get appropriate template
        let template: MessageTemplate | null = null;
        if (landmark) {
          // Get specific landmark template for this age
          template = await this.getLandmarkTemplateForAge(age);
        } else {
          // Get regular birthday template
          const templates = await this.getTemplates();
          template = templates.find(t => t.type === 'birthday' && t.isActive) || null;
        }

        if (template) {
          // Schedule the message
          const scheduledFor = new Date();
          scheduledFor.setHours(6, 0, 0, 0); // Default to 6 AM

          const variables = {
            name: contact.name,
            age: age.toString(),
            years: age.toString(),
          };

          await this.scheduleMessage({
            contactId: contact.id,
            templateId: template.id,
            scheduledFor,
            type: landmark ? 'landmark' : 'birthday',
            variables,
            status: 'pending',
            retryCount: 0,
          });
        }
      }
    }
  }

  // Weekly Admin Notification
  async generateWeeklyAdminNotification(contacts: any[]): Promise<WeeklyAdminNotification> {
    await this.simulateDelay();

    const today = new Date();
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)

    // Find upcoming birthdays in the next 7 days
    const upcomingBirthdays = contacts
      .filter(contact => contact.birthday)
      .map(contact => {
        const birthdayDate = new Date(contact.birthday);
        const birthdayThisYear = new Date(today.getFullYear(), birthdayDate.getMonth(), birthdayDate.getDate());

        // If birthday already passed this year, check next year
        if (birthdayThisYear < today) {
          birthdayThisYear.setFullYear(today.getFullYear() + 1);
        }

        const daysUntil = Math.ceil((birthdayThisYear.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntil <= 7) {
          const age = today.getFullYear() - birthdayDate.getFullYear();
          const landmark = this.isLandmarkBirthday(age);

          return {
            contactId: contact.id,
            name: contact.name,
            birthday: birthdayThisYear.toISOString(),
            age,
            daysUntil,
            isLandmark: landmark !== null,
            landmarkType: landmark?.name,
          };
        }
        return null;
      })
      .filter(Boolean)
      .sort((a, b) => a!.daysUntil - b!.daysUntil);

    const notification: WeeklyAdminNotification = {
      id: `weekly_${Date.now()}`,
      weekStartDate: weekStart.toISOString(),
      weekEndDate: weekEnd.toISOString(),
      upcomingBirthdays: upcomingBirthdays as any[],
      status: 'sent',
      sentAt: new Date().toISOString(),
    };

    // Log the notification (in real app, would send email)
    const historyEntry: MessageHistory = {
      id: `history_${Date.now()}`,
      contactId: 'admin',
      templateId: 'weekly_admin',
      type: 'weekly_admin',
      subject: 'Weekly Birthday Notification',
      content: `Upcoming birthdays this week: ${upcomingBirthdays.length} contacts`,
      status: 'sent',
      sentAt: new Date().toISOString(),
      recipient: '<EMAIL>',
    };

    await persistenceService.create<MessageHistory>('message_history', historyEntry);

    return notification;
  }
}

// Export singleton instance
export const messagingService = new MessagingService();
export default messagingService;
