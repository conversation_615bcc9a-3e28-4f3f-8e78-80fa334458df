import type { Contact } from '../types';
import { databaseService } from './databaseService';
import { differenceInYears, parseISO, format, addYears } from 'date-fns';

// Database row interface for contacts
interface ContactRow {
  id: string;
  user_id: string;
  name: string;
  birthday?: string;
  category?: string;
  confirmed: number; // SQLite stores booleans as integers (0/1)
  anniversary_date?: string;
  anniversary_type?: string;
  partner_name?: string;
  anniversary_notes?: string;
  email?: string;
  phone?: string;
  notes?: string;
  tags?: string; // JSON string
  created_at: string;
  updated_at: string;
}

class ContactsService {
  private currentUserId: string | null = null;

  async initialize(): Promise<void> {
    await databaseService.initialize();
  }

  // Set current user for database operations
  setCurrentUser(userId: string) {
    this.currentUserId = userId;
    databaseService.setCurrentUser(userId);
  }

  // Helper function to convert database row to Contact interface
  private contactRowToContact(row: ContactRow): Contact {
    return {
      id: row.id,
      name: row.name,
      birthday: row.birthday || '',
      category: row.category as Contact['category'] || 'Friends',
      confirmed: Bo<PERSON><PERSON>(row.confirmed), // Convert SQLite integer (0/1) to boolean
      anniversaryDate: row.anniversary_date,
      anniversaryType: row.anniversary_type as Contact['anniversaryType'],
      partnerName: row.partner_name,
      anniversaryNotes: row.anniversary_notes,
      email: row.email,
      phone: row.phone,
      notes: row.notes,
      tags: row.tags ? JSON.parse(row.tags) : undefined
    };
  }

  private async simulateDelay(ms: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  // No conversion needed since we're using the same format

  // Get all contacts for current user
  async getContacts(): Promise<Contact[]> {
    await this.simulateDelay();
    const userId = this.getCurrentUserId();
    const rows = await databaseService.query<ContactRow>('SELECT * FROM contacts WHERE user_id = ? ORDER BY name ASC', [userId]);
    return rows.map(row => this.contactRowToContact(row));
  }

  // Get contact by ID
  async getContactById(id: string): Promise<Contact | null> {
    await this.simulateDelay();
    const userId = this.getCurrentUserId();
    const rows = await databaseService.query<ContactRow>('SELECT * FROM contacts WHERE id = ? AND user_id = ?', [id, userId]);
    return rows[0] ? this.contactRowToContact(rows[0]) : null;
  }

  // Create new contact
  async createContact(contactData: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contact> {
    await this.simulateDelay();
    const userId = this.getCurrentUserId();

    const contactRow = await databaseService.create<ContactRow>('contacts', {
      user_id: userId,
      name: contactData.name,
      birthday: contactData.birthday,
      category: contactData.category,
      confirmed: contactData.confirmed ? 1 : 0, // Convert boolean to SQLite integer
      anniversary_date: contactData.anniversaryDate,
      anniversary_type: contactData.anniversaryType,
      partner_name: contactData.partnerName,
      anniversary_notes: contactData.anniversaryNotes,
      email: (contactData as any).email,
      phone: (contactData as any).phone,
      notes: (contactData as any).notes,
      tags: (contactData as any).tags ? JSON.stringify((contactData as any).tags) : undefined
    });

    return this.contactRowToContact(contactRow);
  }

  // Update existing contact
  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact> {
    await this.simulateDelay();
    const userId = this.getCurrentUserId();

    // Convert Contact interface updates to database field names
    const dbUpdates: Record<string, any> = {};
    if (updates.name !== undefined) dbUpdates.name = updates.name;
    if (updates.birthday !== undefined) dbUpdates.birthday = updates.birthday;
    if (updates.category !== undefined) dbUpdates.category = updates.category;
    if (updates.confirmed !== undefined) dbUpdates.confirmed = updates.confirmed ? 1 : 0; // Convert boolean to SQLite integer
    if (updates.anniversaryDate !== undefined) dbUpdates.anniversary_date = updates.anniversaryDate;
    if (updates.anniversaryType !== undefined) dbUpdates.anniversary_type = updates.anniversaryType;
    if (updates.partnerName !== undefined) dbUpdates.partner_name = updates.partnerName;
    if (updates.anniversaryNotes !== undefined) dbUpdates.anniversary_notes = updates.anniversaryNotes;
    if ((updates as any).email !== undefined) dbUpdates.email = (updates as any).email;
    if ((updates as any).phone !== undefined) dbUpdates.phone = (updates as any).phone;
    if ((updates as any).notes !== undefined) dbUpdates.notes = (updates as any).notes;
    if ((updates as any).tags !== undefined) dbUpdates.tags = JSON.stringify((updates as any).tags);

    if (Object.keys(dbUpdates).length > 0) {
      await databaseService.query(
        'UPDATE contacts SET ' + Object.keys(dbUpdates).map(key => `${key} = ?`).join(', ') + ', updated_at = ? WHERE id = ? AND user_id = ?',
        [...Object.values(dbUpdates), new Date().toISOString(), id, userId]
      );
    }

    const updatedContact = await this.getContactById(id);
    if (!updatedContact) {
      throw new Error('Contact not found');
    }

    return updatedContact;
  }

  // Delete contact
  async deleteContact(id: string): Promise<void> {
    await this.simulateDelay();
    const userId = this.getCurrentUserId();

    const success = await databaseService.delete('contacts', id);
    if (!success) {
      throw new Error('Contact not found');
    }
  }

  // Bulk create contacts
  async createContacts(contactsData: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<Contact[]> {
    await this.simulateDelay(contactsData.length * 50); // Simulate processing time

    const contacts: Contact[] = [];
    for (const contactData of contactsData) {
      const contact = await this.createContact(contactData);
      contacts.push(contact);
    }

    return contacts;
  }

  // Get upcoming birthdays
  async getUpcomingBirthdays(days: number = 30): Promise<Contact[]> {
    await this.simulateDelay();
    const contacts = await this.getContacts();
    const today = new Date();

    return contacts.filter(contact => {
      const birthday = parseISO(contact.birthday);
      const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());

      // If birthday already passed this year, check next year
      if (thisYearBirthday < today) {
        thisYearBirthday.setFullYear(today.getFullYear() + 1);
      }

      const diffTime = thisYearBirthday.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays >= 0 && diffDays <= days;
    }).sort((a, b) => {
      const aBirthday = parseISO(a.birthday);
      const bBirthday = parseISO(b.birthday);
      const aThisYear = new Date(today.getFullYear(), aBirthday.getMonth(), aBirthday.getDate());
      const bThisYear = new Date(today.getFullYear(), bBirthday.getMonth(), bBirthday.getDate());

      if (aThisYear < today) aThisYear.setFullYear(today.getFullYear() + 1);
      if (bThisYear < today) bThisYear.setFullYear(today.getFullYear() + 1);

      return aThisYear.getTime() - bThisYear.getTime();
    });
  }

  // Get upcoming anniversaries
  async getUpcomingAnniversaries(days: number = 30): Promise<Contact[]> {
    await this.simulateDelay();
    const contacts = await this.getContacts();
    const today = new Date();

    return contacts.filter(contact => {
      if (!contact.anniversaryDate) return false;

      const anniversary = parseISO(contact.anniversaryDate);
      const thisYearAnniversary = new Date(today.getFullYear(), anniversary.getMonth(), anniversary.getDate());

      // If anniversary already passed this year, check next year
      if (thisYearAnniversary < today) {
        thisYearAnniversary.setFullYear(today.getFullYear() + 1);
      }

      const diffTime = thisYearAnniversary.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays >= 0 && diffDays <= days;
    }).sort((a, b) => {
      const aAnniversary = parseISO(a.anniversaryDate!);
      const bAnniversary = parseISO(b.anniversaryDate!);
      const aThisYear = new Date(today.getFullYear(), aAnniversary.getMonth(), aAnniversary.getDate());
      const bThisYear = new Date(today.getFullYear(), bAnniversary.getMonth(), bAnniversary.getDate());

      if (aThisYear < today) aThisYear.setFullYear(today.getFullYear() + 1);
      if (bThisYear < today) bThisYear.setFullYear(today.getFullYear() + 1);

      return aThisYear.getTime() - bThisYear.getTime();
    });
  }

  // Get contacts by category
  async getContactsByCategory(category: string): Promise<Contact[]> {
    await this.simulateDelay();
    const contacts = await this.getContacts();

    return contacts.filter(contact => contact.category === category);
  }

  // Search contacts
  async searchContacts(query: string): Promise<Contact[]> {
    await this.simulateDelay();
    const contacts = await this.getContacts();
    const lowerQuery = query.toLowerCase();

    return contacts.filter(contact =>
      contact.name.toLowerCase().includes(lowerQuery) ||
      contact.email?.toLowerCase().includes(lowerQuery) ||
      contact.notes?.toLowerCase().includes(lowerQuery) ||
      contact.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // Get contact statistics
  async getContactStats() {
    await this.simulateDelay();
    const contacts = await this.getContacts();

    const stats = {
      total: contacts.length,
      withBirthdays: contacts.filter(c => c.birthday).length,
      withAnniversaries: contacts.filter(c => c.anniversaryDate).length,
      byCategory: {} as Record<string, number>,
      upcomingBirthdays: (await this.getUpcomingBirthdays(30)).length,
      upcomingAnniversaries: (await this.getUpcomingAnniversaries(30)).length,
    };

    // Count by category
    contacts.forEach(contact => {
      const category = contact.category || 'uncategorized';
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
    });

    return stats;
  }

  // Calculate age for a contact
  calculateAge(contact: Contact): number {
    return differenceInYears(new Date(), parseISO(contact.birthday));
  }

  // Calculate years for anniversary
  calculateAnniversaryYears(contact: Contact): number | null {
    if (!contact.anniversaryDate) return null;
    return differenceInYears(new Date(), parseISO(contact.anniversaryDate));
  }

  // Get next birthday date
  getNextBirthday(contact: Contact): Date {
    const birthday = parseISO(contact.birthday);
    const today = new Date();
    const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());

    if (thisYearBirthday >= today) {
      return thisYearBirthday;
    } else {
      return addYears(thisYearBirthday, 1);
    }
  }

  // Get next anniversary date
  getNextAnniversary(contact: Contact): Date | null {
    if (!contact.anniversaryDate) return null;

    const anniversary = parseISO(contact.anniversaryDate);
    const today = new Date();
    const thisYearAnniversary = new Date(today.getFullYear(), anniversary.getMonth(), anniversary.getDate());

    if (thisYearAnniversary >= today) {
      return thisYearAnniversary;
    } else {
      return addYears(thisYearAnniversary, 1);
    }
  }
}

// Export singleton instance
export const contactsService = new ContactsService();
export default contactsService;
