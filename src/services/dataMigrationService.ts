import { databaseService } from './databaseService';
import { migrationService } from './migrationService';
import { v4 as uuidv4 } from 'uuid';

interface LocalStorageData {
  users: any[];
  organizations: any[];
  contacts: Record<string, any[]>;
  giftHistory: Record<string, any[]>;
  giftReminders: Record<string, any[]>;
  notificationSettings: Record<string, any>;
  userAuth: Record<string, string>;
}

export class DataMigrationService {
  async migrateFromLocalStorage(): Promise<void> {
    console.log('🔄 Starting data migration from localStorage to SQLite...');

    try {
      // Ensure database is initialized and migrated
      await migrationService.runMigrations();

      // Extract all localStorage data
      const localData = this.extractLocalStorageData();

      // Migrate data in order (respecting foreign key constraints)
      await this.migrateUserAuth(localData.userAuth);
      await this.migrateOrganizations(localData.organizations);
      await this.migrateUsers(localData.users);
      await this.migrateContacts(localData.contacts);
      await this.migrateGiftHistory(localData.giftHistory);
      await this.migrateGiftReminders(localData.giftReminders);
      await this.migrateNotificationSettings(localData.notificationSettings);

      console.log('✅ Data migration completed successfully');
      
      // Optionally backup localStorage data before clearing
      await this.backupLocalStorageData(localData);
      
    } catch (error) {
      console.error('❌ Data migration failed:', error);
      throw error;
    }
  }

  private extractLocalStorageData(): LocalStorageData {
    console.log('📦 Extracting data from localStorage...');

    const data: LocalStorageData = {
      users: [],
      organizations: [],
      contacts: {},
      giftHistory: {},
      giftReminders: {},
      notificationSettings: {},
      userAuth: {}
    };

    // Extract global users and organizations
    try {
      const usersData = localStorage.getItem('wewish_global_users');
      if (usersData) {
        data.users = JSON.parse(usersData);
        console.log(`📦 Found ${data.users.length} users`);
      }

      const orgsData = localStorage.getItem('wewish_global_organizations');
      if (orgsData) {
        data.organizations = JSON.parse(orgsData);
        console.log(`📦 Found ${data.organizations.length} organizations`);
      }
    } catch (error) {
      console.log('ℹ️ No global user/organization data found');
    }

    // Extract user authentication data
    try {
      const authData = localStorage.getItem('birthdaySaaSUsers');
      if (authData) {
        data.userAuth = JSON.parse(authData);
        console.log(`📦 Found authentication data for ${Object.keys(data.userAuth).length} users`);
      }
    } catch (error) {
      console.log('ℹ️ No authentication data found');
    }

    // Extract user-specific data
    const allKeys = Object.keys(localStorage);
    
    // Extract contacts
    const contactKeys = allKeys.filter(key => key.startsWith('birthdaySaaSContacts_'));
    contactKeys.forEach(key => {
      const userEmail = key.replace('birthdaySaaSContacts_', '');
      try {
        const contacts = JSON.parse(localStorage.getItem(key) || '[]');
        if (contacts.length > 0) {
          data.contacts[userEmail] = contacts;
          console.log(`📦 Found ${contacts.length} contacts for ${userEmail}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to parse contacts for ${userEmail}`);
      }
    });

    // Extract gift history
    const giftHistoryKeys = allKeys.filter(key => key.startsWith('birthdaySaaSGiftHistory_'));
    giftHistoryKeys.forEach(key => {
      const userEmail = key.replace('birthdaySaaSGiftHistory_', '');
      try {
        const giftHistory = JSON.parse(localStorage.getItem(key) || '[]');
        if (giftHistory.length > 0) {
          data.giftHistory[userEmail] = giftHistory;
          console.log(`📦 Found ${giftHistory.length} gift history items for ${userEmail}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to parse gift history for ${userEmail}`);
      }
    });

    // Extract gift reminders
    const giftReminderKeys = allKeys.filter(key => key.startsWith('birthdaySaaSGiftReminders_'));
    giftReminderKeys.forEach(key => {
      const userEmail = key.replace('birthdaySaaSGiftReminders_', '');
      try {
        const giftReminders = JSON.parse(localStorage.getItem(key) || '[]');
        if (giftReminders.length > 0) {
          data.giftReminders[userEmail] = giftReminders;
          console.log(`📦 Found ${giftReminders.length} gift reminders for ${userEmail}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to parse gift reminders for ${userEmail}`);
      }
    });

    // Extract notification settings
    const notificationKeys = allKeys.filter(key => key.startsWith('birthdaySaaSNotificationSettings_'));
    notificationKeys.forEach(key => {
      const userEmail = key.replace('birthdaySaaSNotificationSettings_', '');
      try {
        const setting = localStorage.getItem(key);
        if (setting) {
          data.notificationSettings[userEmail] = setting === 'true';
          console.log(`📦 Found notification settings for ${userEmail}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to parse notification settings for ${userEmail}`);
      }
    });

    return data;
  }

  private async migrateUserAuth(userAuth: Record<string, string>): Promise<void> {
    console.log('🔄 Migrating user authentication data...');

    for (const [email, passwordHash] of Object.entries(userAuth)) {
      try {
        await databaseService.create('user_auth', {
          email,
          password_hash: passwordHash
        });
      } catch (error) {
        console.warn(`⚠️ Failed to migrate auth for ${email}:`, error);
      }
    }

    console.log(`✅ Migrated ${Object.keys(userAuth).length} user authentication records`);
  }

  private async migrateOrganizations(organizations: any[]): Promise<void> {
    console.log('🔄 Migrating organizations...');

    for (const org of organizations) {
      try {
        await databaseService.create('organizations', {
          id: org.id || uuidv4(),
          name: org.name,
          type: org.type || 'other',
          description: org.description,
          admin_email: org.adminEmail,
          settings: JSON.stringify(org.settings || {}),
          is_active: org.isActive !== false,
          created_at: org.createdAt || new Date().toISOString(),
          updated_at: org.updatedAt || new Date().toISOString()
        });
      } catch (error) {
        console.warn(`⚠️ Failed to migrate organization ${org.name}:`, error);
      }
    }

    console.log(`✅ Migrated ${organizations.length} organizations`);
  }

  private async migrateUsers(users: any[]): Promise<void> {
    console.log('🔄 Migrating users...');

    for (const user of users) {
      try {
        await databaseService.create('users', {
          id: uuidv4(),
          email: user.email,
          name: user.name,
          role: user.role || 'user',
          organization_id: user.organizationId,
          is_active: user.isActive !== false,
          last_login: user.lastLogin,
          created_at: user.createdAt || new Date().toISOString(),
          updated_at: user.updatedAt || new Date().toISOString()
        });
      } catch (error) {
        console.warn(`⚠️ Failed to migrate user ${user.email}:`, error);
      }
    }

    console.log(`✅ Migrated ${users.length} users`);
  }

  private async migrateContacts(contactsByUser: Record<string, any[]>): Promise<void> {
    console.log('🔄 Migrating contacts...');

    let totalContacts = 0;

    for (const [userEmail, contacts] of Object.entries(contactsByUser)) {
      // Get user ID from database
      const users = await databaseService.query<{id: string}>('SELECT id FROM users WHERE email = ?', [userEmail]);
      const userId = users[0]?.id;

      if (!userId) {
        console.warn(`⚠️ User not found for email ${userEmail}, skipping contacts`);
        continue;
      }

      for (const contact of contacts) {
        try {
          await databaseService.create('contacts', {
            id: contact.id || uuidv4(),
            user_id: userId,
            name: contact.name,
            birthday: contact.birthday,
            category: contact.category,
            confirmed: contact.confirmed || false,
            anniversary_date: contact.anniversaryDate,
            anniversary_type: contact.anniversaryType,
            partner_name: contact.partnerName,
            anniversary_notes: contact.anniversaryNotes,
            email: contact.email,
            phone: contact.phone,
            notes: contact.notes,
            tags: contact.tags ? JSON.stringify(contact.tags) : null,
            created_at: contact.createdAt || new Date().toISOString(),
            updated_at: contact.updatedAt || new Date().toISOString()
          });
          totalContacts++;
        } catch (error) {
          console.warn(`⚠️ Failed to migrate contact ${contact.name} for ${userEmail}:`, error);
        }
      }
    }

    console.log(`✅ Migrated ${totalContacts} contacts`);
  }

  private async migrateGiftHistory(giftHistoryByUser: Record<string, any[]>): Promise<void> {
    console.log('🔄 Migrating gift history...');

    let totalGifts = 0;

    for (const [userEmail, giftHistory] of Object.entries(giftHistoryByUser)) {
      const users = await databaseService.query<{id: string}>('SELECT id FROM users WHERE email = ?', [userEmail]);
      const userId = users[0]?.id;

      if (!userId) {
        console.warn(`⚠️ User not found for email ${userEmail}, skipping gift history`);
        continue;
      }

      for (const gift of giftHistory) {
        try {
          await databaseService.create('gift_history', {
            id: gift.id || uuidv4(),
            user_id: userId,
            gift_name: gift.giftName,
            recipient: gift.recipient,
            recipient_id: gift.recipientId,
            date: gift.date,
            amount: gift.amount,
            category: gift.category,
            notes: gift.notes,
            created_at: gift.createdAt || new Date().toISOString(),
            updated_at: gift.updatedAt || new Date().toISOString()
          });
          totalGifts++;
        } catch (error) {
          console.warn(`⚠️ Failed to migrate gift ${gift.giftName} for ${userEmail}:`, error);
        }
      }
    }

    console.log(`✅ Migrated ${totalGifts} gift history items`);
  }

  private async migrateGiftReminders(giftRemindersByUser: Record<string, any[]>): Promise<void> {
    console.log('🔄 Migrating gift reminders...');

    let totalReminders = 0;

    for (const [userEmail, giftReminders] of Object.entries(giftRemindersByUser)) {
      const users = await databaseService.query<{id: string}>('SELECT id FROM users WHERE email = ?', [userEmail]);
      const userId = users[0]?.id;

      if (!userId) {
        console.warn(`⚠️ User not found for email ${userEmail}, skipping gift reminders`);
        continue;
      }

      for (const reminder of giftReminders) {
        try {
          await databaseService.create('gift_reminders', {
            id: reminder.id || uuidv4(),
            user_id: userId,
            contact_id: reminder.contactId,
            contact_name: reminder.contactName,
            reminder_date: reminder.reminderDate,
            event_date: reminder.eventDate,
            gift_ideas: JSON.stringify(reminder.giftIdeas || []),
            budget: reminder.budget,
            notes: reminder.notes,
            is_active: reminder.isActive !== false,
            created_at: reminder.createdAt || new Date().toISOString(),
            updated_at: reminder.updatedAt || new Date().toISOString()
          });
          totalReminders++;
        } catch (error) {
          console.warn(`⚠️ Failed to migrate gift reminder for ${userEmail}:`, error);
        }
      }
    }

    console.log(`✅ Migrated ${totalReminders} gift reminders`);
  }

  private async migrateNotificationSettings(notificationsByUser: Record<string, any>): Promise<void> {
    console.log('🔄 Migrating notification settings...');

    let totalSettings = 0;

    for (const [userEmail, reminderEnabled] of Object.entries(notificationsByUser)) {
      const users = await databaseService.query<{id: string}>('SELECT id FROM users WHERE email = ?', [userEmail]);
      const userId = users[0]?.id;

      if (!userId) {
        console.warn(`⚠️ User not found for email ${userEmail}, skipping notification settings`);
        continue;
      }

      try {
        await databaseService.create('notification_settings', {
          user_id: userId,
          reminder_enabled: reminderEnabled,
          email_notifications: true,
          push_notifications: false,
          reminder_days_before: 7,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        totalSettings++;
      } catch (error) {
        console.warn(`⚠️ Failed to migrate notification settings for ${userEmail}:`, error);
      }
    }

    console.log(`✅ Migrated ${totalSettings} notification settings`);
  }

  private async backupLocalStorageData(data: LocalStorageData): Promise<void> {
    console.log('💾 Creating backup of localStorage data...');

    const backup = {
      timestamp: new Date().toISOString(),
      data: data,
      localStorage: {} as Record<string, string>
    };

    // Backup all localStorage keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('birthdaySaaS') || key?.startsWith('wewish_')) {
        backup.localStorage[key] = localStorage.getItem(key) || '';
      }
    }

    // Save backup to localStorage with a special key
    localStorage.setItem('wewish_migration_backup', JSON.stringify(backup));
    console.log('✅ Backup created successfully');
  }

  async clearLocalStorageData(): Promise<void> {
    console.log('🧹 Clearing old localStorage data...');

    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('birthdaySaaS') || key.startsWith('wewish_global'))) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`✅ Removed ${keysToRemove.length} localStorage keys`);
  }

  async restoreFromBackup(): Promise<void> {
    console.log('🔄 Restoring from backup...');

    const backupData = localStorage.getItem('wewish_migration_backup');
    if (!backupData) {
      throw new Error('No backup found');
    }

    const backup = JSON.parse(backupData);
    
    // Restore localStorage data
    Object.entries(backup.localStorage).forEach(([key, value]) => {
      localStorage.setItem(key, value as string);
    });

    console.log('✅ Backup restored successfully');
  }

  async getMigrationStatus(): Promise<{
    hasLocalStorageData: boolean;
    hasSQLiteData: boolean;
    localStorageKeys: string[];
    sqliteStats: Record<string, number>;
  }> {
    const localStorageKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('birthdaySaaS') || key.startsWith('wewish_global'))) {
        localStorageKeys.push(key);
      }
    }

    let sqliteStats = {};
    try {
      await databaseService.initialize();
      sqliteStats = await databaseService.getStats();
    } catch (error) {
      console.log('SQLite not initialized yet');
    }

    return {
      hasLocalStorageData: localStorageKeys.length > 0,
      hasSQLiteData: Object.values(sqliteStats).some(count => count > 0),
      localStorageKeys,
      sqliteStats
    };
  }
}

// Export singleton instance
export const dataMigrationService = new DataMigrationService();
export default dataMigrationService;
