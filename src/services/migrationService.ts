import { databaseService } from './databaseService';

interface Migration {
  version: number;
  name: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

interface MigrationRecord {
  id: string;
  version: number;
  name: string;
  executed_at: string;
}

export class MigrationService {
  private migrations: Migration[] = [];

  constructor() {
    this.registerMigrations();
  }

  private registerMigrations(): void {
    // Migration 1: Initial schema (already handled in databaseService.createTables)
    this.migrations.push({
      version: 1,
      name: 'initial_schema',
      up: async () => {
        // Tables are created in databaseService.createTables()
        console.log('✅ Initial schema migration completed');
      },
      down: async () => {
        // Drop all tables
        const tables = [
          'user_auth', 'notification_settings', 'message_templates',
          'reminders', 'gift_reminders', 'gift_history', 'contacts',
          'organizations', 'users', 'migrations'
        ];

        for (const table of tables) {
          await databaseService.query(`DROP TABLE IF EXISTS ${table}`);
        }
        console.log('✅ Initial schema rollback completed');
      }
    });

    // Migration 2: Add indexes for performance
    this.migrations.push({
      version: 2,
      name: 'add_performance_indexes',
      up: async () => {
        const indexes = [
          'CREATE INDEX IF NOT EXISTS idx_contacts_category ON contacts (category)',
          'CREATE INDEX IF NOT EXISTS idx_contacts_confirmed ON contacts (confirmed)',
          'CREATE INDEX IF NOT EXISTS idx_gift_history_date ON gift_history (date)',
          'CREATE INDEX IF NOT EXISTS idx_gift_reminders_reminder_date ON gift_reminders (reminder_date)',
          'CREATE INDEX IF NOT EXISTS idx_reminders_reminder_date ON reminders (reminder_date)',
          'CREATE INDEX IF NOT EXISTS idx_reminders_is_sent ON reminders (is_sent)',
          'CREATE INDEX IF NOT EXISTS idx_message_templates_type ON message_templates (type)',
          'CREATE INDEX IF NOT EXISTS idx_message_templates_is_active ON message_templates (is_active)'
        ];

        for (const indexSQL of indexes) {
          await databaseService.query(indexSQL);
        }
        console.log('✅ Performance indexes migration completed');
      },
      down: async () => {
        const indexes = [
          'DROP INDEX IF EXISTS idx_contacts_category',
          'DROP INDEX IF EXISTS idx_contacts_confirmed',
          'DROP INDEX IF EXISTS idx_gift_history_date',
          'DROP INDEX IF EXISTS idx_gift_reminders_reminder_date',
          'DROP INDEX IF EXISTS idx_reminders_reminder_date',
          'DROP INDEX IF EXISTS idx_reminders_is_sent',
          'DROP INDEX IF EXISTS idx_message_templates_type',
          'DROP INDEX IF EXISTS idx_message_templates_is_active'
        ];

        for (const indexSQL of indexes) {
          await databaseService.query(indexSQL);
        }
        console.log('✅ Performance indexes rollback completed');
      }
    });

    // Migration 3: Add additional contact fields
    this.migrations.push({
      version: 3,
      name: 'add_contact_fields',
      up: async () => {
        const alterStatements = [
          'ALTER TABLE contacts ADD COLUMN address TEXT',
          'ALTER TABLE contacts ADD COLUMN company TEXT',
          'ALTER TABLE contacts ADD COLUMN job_title TEXT',
          'ALTER TABLE contacts ADD COLUMN social_media TEXT', // JSON object
          'ALTER TABLE contacts ADD COLUMN preferences TEXT'   // JSON object
        ];

        for (const sql of alterStatements) {
          try {
            await databaseService.query(sql);
          } catch (error) {
            // Column might already exist, ignore error
            console.log(`Column already exists or error adding: ${sql}`);
          }
        }
        console.log('✅ Additional contact fields migration completed');
      },
      down: async () => {
        // SQLite doesn't support DROP COLUMN easily, so we'd need to recreate the table
        console.log('⚠️ Rollback for contact fields not implemented (SQLite limitation)');
      }
    });
  }

  async createMigrationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id TEXT PRIMARY KEY,
        version INTEGER UNIQUE NOT NULL,
        name TEXT NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    await databaseService.query(sql);
  }

  async getExecutedMigrations(): Promise<MigrationRecord[]> {
    try {
      return await databaseService.query<MigrationRecord>(
        'SELECT * FROM migrations ORDER BY version ASC'
      );
    } catch (error) {
      // Table might not exist yet
      return [];
    }
  }

  async markMigrationExecuted(migration: Migration): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO migrations (id, version, name, executed_at)
      VALUES (?, ?, ?, ?)
    `;
    const id = `migration_${migration.version}`;
    const now = new Date().toISOString();

    try {
      await databaseService.query(sql, [id, migration.version, migration.name, now]);
    } catch (error) {
      console.error(`Failed to mark migration ${migration.version} as executed:`, error);
      // Don't throw - this is not critical if it fails
    }
  }

  async markMigrationRolledBack(version: number): Promise<void> {
    await databaseService.query('DELETE FROM migrations WHERE version = ?', [version]);
  }

  async runMigrations(): Promise<void> {
    console.log('🔄 Starting database migrations...');

    try {
      // Ensure database is initialized
      await databaseService.initialize();

      // Create migrations table
      await this.createMigrationsTable();

      // Get executed migrations
      const executedMigrations = await this.getExecutedMigrations();
      const executedVersions = new Set(executedMigrations.map(m => m.version));

      console.log(`📊 Found ${executedMigrations.length} previously executed migrations`);
      console.log(`📋 Total migrations available: ${this.migrations.length}`);

      // Run pending migrations
      let migrationsRun = 0;
      for (const migration of this.migrations) {
        if (!executedVersions.has(migration.version)) {
          console.log(`🔄 Running migration ${migration.version}: ${migration.name}`);

          try {
            await migration.up();
            await this.markMigrationExecuted(migration);
            console.log(`✅ Migration ${migration.version} completed successfully`);
            migrationsRun++;
          } catch (error) {
            console.error(`❌ Migration ${migration.version} failed:`, error);
            // Don't throw immediately - try to continue with other migrations
            console.warn(`⚠️ Continuing with remaining migrations...`);
          }
        } else {
          console.log(`⏭️ Migration ${migration.version} already executed`);
        }
      }

      console.log(`✅ Migration process completed. ${migrationsRun} new migrations executed.`);
    } catch (error) {
      console.error('❌ Migration process failed:', error);
      // Don't throw - allow the app to continue even if migrations fail
      console.warn('⚠️ App will continue with existing database state');
    }
  }

  async rollbackMigration(targetVersion: number): Promise<void> {
    console.log(`🔄 Rolling back to migration version ${targetVersion}...`);

    const executedMigrations = await this.getExecutedMigrations();
    const migrationsToRollback = executedMigrations
      .filter(m => m.version > targetVersion)
      .sort((a, b) => b.version - a.version); // Rollback in reverse order

    for (const migrationRecord of migrationsToRollback) {
      const migration = this.migrations.find(m => m.version === migrationRecord.version);

      if (migration) {
        console.log(`🔄 Rolling back migration ${migration.version}: ${migration.name}`);

        try {
          await migration.down();
          await this.markMigrationRolledBack(migration.version);
          console.log(`✅ Migration ${migration.version} rolled back successfully`);
        } catch (error) {
          console.error(`❌ Rollback of migration ${migration.version} failed:`, error);
          throw error;
        }
      }
    }

    console.log(`✅ Rollback to version ${targetVersion} completed`);
  }

  async getMigrationStatus(): Promise<{
    current: number;
    available: number;
    pending: Migration[];
    executed: MigrationRecord[];
  }> {
    const executedMigrations = await this.getExecutedMigrations();
    const executedVersions = new Set(executedMigrations.map(m => m.version));

    const pendingMigrations = this.migrations.filter(m => !executedVersions.has(m.version));
    const currentVersion = executedMigrations.length > 0
      ? Math.max(...executedMigrations.map(m => m.version))
      : 0;
    const availableVersion = this.migrations.length > 0
      ? Math.max(...this.migrations.map(m => m.version))
      : 0;

    return {
      current: currentVersion,
      available: availableVersion,
      pending: pendingMigrations,
      executed: executedMigrations
    };
  }

  async clearMigrationHistory(): Promise<void> {
    console.log('🔄 Clearing migration history...');

    try {
      await databaseService.query('DELETE FROM migrations');
      console.log('✅ Migration history cleared');
    } catch (error) {
      console.error('❌ Failed to clear migration history:', error);
      // Try to drop and recreate the migrations table
      try {
        await databaseService.query('DROP TABLE IF EXISTS migrations');
        await this.createMigrationsTable();
        console.log('✅ Migration table recreated');
      } catch (recreateError) {
        console.error('❌ Failed to recreate migration table:', recreateError);
      }
    }
  }

  async resetDatabase(): Promise<void> {
    console.log('🔄 Resetting database...');

    // Clear localStorage database
    localStorage.removeItem('wewish_sqlite_db');

    // Reinitialize database
    await databaseService.initialize();

    // Run all migrations
    await this.runMigrations();

    console.log('✅ Database reset completed');
  }
}

// Export singleton instance
export const migrationService = new MigrationService();
export default migrationService;
