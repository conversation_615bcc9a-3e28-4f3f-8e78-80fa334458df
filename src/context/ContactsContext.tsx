import { createContext, useEffect, useState, useContext, type ReactNode } from "react";
import { type Contact } from "../types";
import { useAuth } from "./AuthContext";
import { useSubscription } from "./SubscriptionContext";
import { useErrorNotification } from "./ErrorContext";
import { contactsService } from "../services/contactsService";



interface ContactsContextType {
    contacts: Contact[];
    isLoading: boolean;
    addContact: (contact: Omit<Contact, 'id'>) => void;
    updateContact: (id: string, contact: Partial<Contact>) => void;
    deleteContact: (id: string) => void;
    addContacts: (contacts: Omit<Contact, 'id'>[]) => void;
}

const ContactsContext = createContext<ContactsContextType | undefined>(undefined);




export const ContactsProvider = ({ children }: { children: ReactNode; }) => {
    const [contacts, setContacts] = useState<Contact[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const { user } = useAuth();
    const { plan } = useSubscription();
    const { showError, showWarning, showSuccess } = useErrorNotification();

    // Initialize contacts service and load contacts when user changes
    useEffect(() => {
        const loadContacts = async () => {
            if (user) {
                try {
                    setIsLoading(true);
                    await contactsService.initialize();
                    contactsService.setCurrentUser(user.email);
                    const loadedContacts = await contactsService.getContacts();
                    setContacts(loadedContacts);
                } catch (error) {
                    console.error("Error loading contacts:", error);
                    showError("Failed to load contacts from database");
                }
                finally {
                    setIsLoading(false);
                }
            } else {
                setContacts([]);
            }
        };

        loadContacts();
    }, [user, showError]);

    const addContact = async (contactData: Omit<Contact, 'id'>) => {
        if (plan === "Free" && contacts.length >= 25) {
            showWarning("Free plan limit reached. Upgrade to Premium for unlimited contacts.");
            return;
        }

        try {
            const newContact = await contactsService.createContact(contactData);
            setContacts((prev) => [...prev, newContact]);
            showSuccess(`Contact ${newContact.name} added successfully!`);
        } catch (error) {
            console.error("Error adding contact:", error);
            showError("Failed to add contact");
        }
    };

    const updateContact = async (id: string, updates: Partial<Contact>) => {
        try {
            const updatedContact = await contactsService.updateContact(id, updates);
            setContacts((prev) => prev.map(c => c.id === id ? updatedContact : c));
            showSuccess("Contact updated successfully!");
        } catch (error) {
            console.error("Error updating contact:", error);
            showError("Failed to update contact");
        }
    };

    const deleteContact = async (id: string) => {
        try {
            await contactsService.deleteContact(id);
            setContacts((prev) => prev.filter((c) => c.id !== id));
            showSuccess("Contact deleted successfully!");
        } catch (error) {
            console.error("Error deleting contact:", error);
            showError("Failed to delete contact");
        }
    };

    const addContacts = async (contactsData: Omit<Contact, 'id'>[]) => {
        if (plan === "Free" && contacts.length + contactsData.length > 25) {
            showWarning("Free plan limit reached. Upgrade to Premium for unlimited contacts.");
            return;
        }

        try {
            const newContacts = await contactsService.createContacts(contactsData);
            setContacts((prev) => [...prev, ...newContacts]);
            showSuccess(`${newContacts.length} contacts added successfully!`);
        } catch (error) {
            console.error("Error adding contacts:", error);
            showError("Failed to add contacts");
        }
    };

    return (
        <ContactsContext.Provider value={{
            contacts,
            isLoading,
            addContact,
            updateContact,
            deleteContact,
            addContacts
        }}>
            {children}
        </ContactsContext.Provider>
    );
};

export const useContacts = () => {
    const ctx = useContext(ContactsContext);
    if (!ctx) throw new Error("useContacts must be used within ContactsProvider");
    return ctx;
};
