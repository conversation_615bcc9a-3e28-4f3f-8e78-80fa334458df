import { createContext, useEffect, useState, useContext, type ReactNode } from "react";
import { type Contact } from "../types";
import { useAuth } from "./AuthContext";
import { useSubscription } from "./SubscriptionContext";
import { useErrorNotification } from "./ErrorContext";



interface ContactsContextType {
    contacts: Contact[];
    addContact: (contact: Contact) => void;
    removeContact: (id: string) => void;
}

const ContactsContext = createContext<ContactsContextType | undefined>(undefined);

const initialContacts: Contact[] = [
    { id: "1", name: "<PERSON>", birthday: "1990-07-01", category: "Friends" },
    { id: "2", name: "<PERSON>", birthday: "1985-06-30", category: "Family" },
    { id: "3", name: "<PERSON>", birthday: "1975-06-30", category: "Colleagues" },
    { id: "4", name: "<PERSON>", birthday: "1988-03-05", category: "<PERSON>" },
    { id: "5", name: "<PERSON>", birthday: "1992-01-15", category: "Family" },
    { id: "6", name: "<PERSON>", birthday: "1987-09-22", category: "Colleagues" },
    { id: "7", name: "<PERSON> <PERSON>", birthday: "1995-11-08", category: "Friends" },
    { id: "8", name: "<PERSON> <PERSON>", birthday: "1983-04-12", category: "Family" },
    { id: "9", name: "<PERSON> <PERSON>", birthday: "1991-08-30", category: "Colleagues" },
    { id: "10", name: "<PERSON> <PERSON>", birthday: "1989-12-03", category: "Friends" },
    { id: "11", name: "Kevin Lee", birthday: "1986-02-18", category: "Family" },
    { id: "12", name: "Laura White", birthday: "1993-05-25", category: "Colleagues" },
    { id: "13", name: "Michael Chen", birthday: "1984-10-14", category: "Friends" },
    { id: "14", name: "Nancy Rodriguez", birthday: "1990-07-07", category: "Family" },
    { id: "15", name: "Oliver Taylor", birthday: "1988-01-29", category: "Colleagues" },
    { id: "16", name: "Patricia Anderson", birthday: "1992-06-11", category: "Clients" },
    { id: "17", name: "Quinn Jackson", birthday: "1987-03-16", category: "Clients" },
    { id: "18", name: "Rachel Green", birthday: "1985-09-04", category: "Friends" },
    { id: "19", name: "Samuel Harris", birthday: "1991-11-21", category: "Family" },
    { id: "20", name: "Tina Clark", birthday: "1989-04-08", category: "Colleagues" },
    // Additional July birthdays to match the screenshot
    { id: "21", name: "Emma Watson", birthday: "1990-07-22", category: "Friends" },
    { id: "22", name: "James Wilson", birthday: "1985-07-22", category: "Family" },
    { id: "23", name: "Sarah Connor", birthday: "1992-07-15", category: "Colleagues" },
    { id: "24", name: "John Doe", birthday: "1988-07-03", category: "Friends" },
];


const STORAGE_KEY_PREFIX = "birthdaySaaSContacts_";


export const ContactsProvider = ({ children }: { children: ReactNode; }) => {
    const [contacts, setContacts] = useState<Contact[]>(initialContacts);
    const { user } = useAuth();
    const { plan } = useSubscription();
    const { showError, showWarning, showSuccess } = useErrorNotification();


    // Only load contacts from storage when user changes
    useEffect(() => {
        if (user) {
            try {
                const saved = localStorage.getItem(STORAGE_KEY_PREFIX + user.email);
                const loadedContacts = saved ? JSON.parse(saved) : [];
                setContacts(loadedContacts);
            } catch (error) {
                showError("Failed to load contacts from storage");
                console.error("Error loading contacts:", error);
            }
        }
    }, [user, showError]);

    useEffect(() => {
        if (user) {
            try {
                localStorage.setItem(STORAGE_KEY_PREFIX + user.email, JSON.stringify(contacts));
            } catch (error) {
                showError("Failed to save contacts to storage");
                console.error("Error saving contacts:", error);
            }
        }
    }, [contacts, user, showError]);

    const addContact = (contact: Contact) => {
        if (plan === "Free" && contacts.length >= 25) {
            showWarning("Free plan limit reached. Upgrade to Premium for unlimited contacts.");
            return;
        }
        setContacts((prev) => [...prev, contact]);
        showSuccess(`Contact ${contact.name} added successfully!`);
    };

    const removeContact = (id: string) => {
        setContacts((prev) => prev.filter((c) => c.id !== id));
    };



    return (
        <ContactsContext.Provider value={{ contacts, addContact, removeContact }}>
            {children}
        </ContactsContext.Provider>
    );
};

export const useContacts = () => {
    const ctx = useContext(ContactsContext);
    if (!ctx) throw new Error("useContacts must be used within ContactsProvider");
    return ctx;
};
