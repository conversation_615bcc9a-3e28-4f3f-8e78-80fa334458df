import { createContext, useContext, useState, useEffect } from "react";
import type { ReactNode } from "react";
import { useAuth } from "./AuthContext";
import { databaseService } from "../services/databaseService";

interface GiftHistoryItem {
    id: string;
    giftName: string;
    recipient: string;
    recipientId: string;
    date: string;
    amount: number;
    category: string;
    notes?: string;
}

interface GiftReminder {
    id: string;
    contactId: string;
    contactName: string;
    reminderDate: string;
    eventDate: string; // birthday date
    giftIdeas: string[];
    budget?: number;
    notes?: string;
    isActive: boolean;
}

interface GiftSuggestion {
    id: string;
    name: string;
    category: string;
    price: number;
    rating: number;
    description: string;
    ageGroup?: string;
    interests?: string[];
}

interface GiftContextType {
    giftHistory: GiftHistoryItem[];
    giftReminders: GiftReminder[];
    addToGiftHistory: (item: Omit<GiftHistoryItem, 'id'>) => void;
    createGiftReminder: (reminder: Omit<GiftReminder, 'id'>) => void;
    updateGiftReminder: (id: string, updates: Partial<GiftReminder>) => void;
    deleteGiftReminder: (id: string) => void;
    getGiftHistoryForContact: (contactId: string) => GiftHistoryItem[];
    getGiftReminderForContact: (contactId: string) => GiftReminder | undefined;
    getSuggestedGifts: (contactId: string, budget?: number) => GiftSuggestion[];
}

const GiftContext = createContext<GiftContextType | undefined>(undefined);

// Database row interfaces
interface GiftHistoryRow {
    id: string;
    user_id: string;
    gift_name: string;
    recipient: string;
    recipient_id: string;
    date: string;
    amount: number;
    category: string;
    notes?: string;
    created_at: string;
    updated_at: string;
}

interface GiftReminderRow {
    id: string;
    user_id: string;
    contact_id: string;
    contact_name: string;
    reminder_date: string;
    event_date: string;
    gift_ideas: string; // JSON string
    budget?: number;
    notes?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

// Sample gift suggestions data
const giftSuggestions: GiftSuggestion[] = [
    {
        id: '1',
        name: 'Wireless Bluetooth Headphones',
        category: 'Electronics',
        price: 199.99,
        rating: 4.5,
        description: 'High-quality noise-canceling headphones perfect for music lovers.',
        ageGroup: 'adult',
        interests: ['music', 'technology']
    },
    {
        id: '2',
        name: 'Artisan Coffee Subscription',
        category: 'Food & Beverage',
        price: 89.99,
        rating: 4.8,
        description: '3-month subscription to premium coffee beans from around the world.',
        ageGroup: 'adult',
        interests: ['coffee', 'food']
    },
    {
        id: '3',
        name: 'Leather Bound Journal',
        category: 'Stationery',
        price: 45.00,
        rating: 4.3,
        description: 'Handcrafted leather journal perfect for writing and note-taking.',
        ageGroup: 'adult',
        interests: ['writing', 'organization']
    },
    {
        id: '4',
        name: 'Smart Fitness Watch',
        category: 'Electronics',
        price: 299.99,
        rating: 4.6,
        description: 'Advanced fitness tracking with heart rate monitoring and GPS.',
        ageGroup: 'adult',
        interests: ['fitness', 'health', 'technology']
    },
    {
        id: '5',
        name: 'Gourmet Chocolate Box',
        category: 'Food & Beverage',
        price: 35.00,
        rating: 4.7,
        description: 'Premium assorted chocolates from renowned chocolatiers.',
        ageGroup: 'all',
        interests: ['food', 'sweets']
    },
    {
        id: '6',
        name: 'Silk Scarf',
        category: 'Clothing',
        price: 75.00,
        rating: 4.4,
        description: 'Elegant silk scarf with beautiful patterns.',
        ageGroup: 'adult',
        interests: ['fashion', 'accessories']
    },
    {
        id: '7',
        name: 'Board Game Collection',
        category: 'Entertainment',
        price: 120.00,
        rating: 4.5,
        description: 'Set of popular strategy and party board games.',
        ageGroup: 'all',
        interests: ['games', 'social']
    },
    {
        id: '8',
        name: 'Essential Oils Diffuser Set',
        category: 'Home & Garden',
        price: 65.00,
        rating: 4.3,
        description: 'Aromatherapy diffuser with starter essential oils collection.',
        ageGroup: 'adult',
        interests: ['wellness', 'home']
    }
];

export const GiftProvider = ({ children }: { children: ReactNode; }) => {
    const { user } = useAuth();
    const [giftHistory, setGiftHistory] = useState<GiftHistoryItem[]>([]);
    const [giftReminders, setGiftReminders] = useState<GiftReminder[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    // Helper functions to convert between database rows and interface types
    const giftHistoryRowToItem = (row: GiftHistoryRow): GiftHistoryItem => ({
        id: row.id,
        giftName: row.gift_name,
        recipient: row.recipient,
        recipientId: row.recipient_id,
        date: row.date,
        amount: row.amount,
        category: row.category,
        notes: row.notes
    });

    const giftReminderRowToItem = (row: GiftReminderRow): GiftReminder => ({
        id: row.id,
        contactId: row.contact_id,
        contactName: row.contact_name,
        reminderDate: row.reminder_date,
        eventDate: row.event_date,
        giftIdeas: JSON.parse(row.gift_ideas || '[]'),
        budget: row.budget,
        notes: row.notes,
        isActive: row.is_active
    });

    // Load data from database when user changes
    useEffect(() => {
        const loadGiftData = async () => {
            if (user) {
                try {
                    setIsLoading(true);
                    await databaseService.initialize();

                    // Load gift history
                    const historyRows = await databaseService.query<GiftHistoryRow>(
                        'SELECT * FROM gift_history WHERE user_id = ? ORDER BY date DESC',
                        [user.email]
                    );
                    setGiftHistory(historyRows.map(giftHistoryRowToItem));

                    // Load gift reminders
                    const reminderRows = await databaseService.query<GiftReminderRow>(
                        'SELECT * FROM gift_reminders WHERE user_id = ? ORDER BY reminder_date ASC',
                        [user.email]
                    );
                    setGiftReminders(reminderRows.map(giftReminderRowToItem));
                } catch (error) {
                    console.error("Error loading gift data:", error);
                } finally {
                    setIsLoading(false);
                }
            } else {
                setGiftHistory([]);
                setGiftReminders([]);
            }
        };

        loadGiftData();
    }, [user]);

    const addToGiftHistory = async (item: Omit<GiftHistoryItem, 'id'>) => {
        if (!user) return;

        try {
            const historyRow = await databaseService.create<GiftHistoryRow>('gift_history', {
                user_id: user.email,
                gift_name: item.giftName,
                recipient: item.recipient,
                recipient_id: item.recipientId,
                date: item.date,
                amount: item.amount,
                category: item.category,
                notes: item.notes
            });

            const newItem = giftHistoryRowToItem(historyRow);
            setGiftHistory(prev => [newItem, ...prev]);
        } catch (error) {
            console.error("Error adding gift history:", error);
        }
    };

    const createGiftReminder = async (reminder: Omit<GiftReminder, 'id'>) => {
        if (!user) return;

        try {
            const reminderRow = await databaseService.create<GiftReminderRow>('gift_reminders', {
                user_id: user.email,
                contact_id: reminder.contactId,
                contact_name: reminder.contactName,
                reminder_date: reminder.reminderDate,
                event_date: reminder.eventDate,
                gift_ideas: JSON.stringify(reminder.giftIdeas || []),
                budget: reminder.budget,
                notes: reminder.notes,
                is_active: reminder.isActive !== false
            });

            const newReminder = giftReminderRowToItem(reminderRow);
            setGiftReminders(prev => [newReminder, ...prev]);
        } catch (error) {
            console.error("Error creating gift reminder:", error);
        }
    };

    const updateGiftReminder = async (id: string, updates: Partial<GiftReminder>) => {
        if (!user) return;

        try {
            // Convert interface updates to database field names
            const dbUpdates: Record<string, any> = {};
            if (updates.contactId !== undefined) dbUpdates.contact_id = updates.contactId;
            if (updates.contactName !== undefined) dbUpdates.contact_name = updates.contactName;
            if (updates.reminderDate !== undefined) dbUpdates.reminder_date = updates.reminderDate;
            if (updates.eventDate !== undefined) dbUpdates.event_date = updates.eventDate;
            if (updates.giftIdeas !== undefined) dbUpdates.gift_ideas = JSON.stringify(updates.giftIdeas);
            if (updates.budget !== undefined) dbUpdates.budget = updates.budget;
            if (updates.notes !== undefined) dbUpdates.notes = updates.notes;
            if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;

            if (Object.keys(dbUpdates).length > 0) {
                await databaseService.query(
                    'UPDATE gift_reminders SET ' + Object.keys(dbUpdates).map(key => `${key} = ?`).join(', ') + ', updated_at = ? WHERE id = ? AND user_id = ?',
                    [...Object.values(dbUpdates), new Date().toISOString(), id, user.email]
                );
            }

            setGiftReminders(prev =>
                prev.map(reminder =>
                    reminder.id === id ? { ...reminder, ...updates } : reminder
                )
            );
        } catch (error) {
            console.error("Error updating gift reminder:", error);
        }
    };

    const deleteGiftReminder = async (id: string) => {
        if (!user) return;

        try {
            await databaseService.query('DELETE FROM gift_reminders WHERE id = ? AND user_id = ?', [id, user.email]);
            setGiftReminders(prev => prev.filter(reminder => reminder.id !== id));
        } catch (error) {
            console.error("Error deleting gift reminder:", error);
        }
    };

    const getGiftHistoryForContact = (contactId: string): GiftHistoryItem[] => {
        return giftHistory.filter(item => item.recipientId === contactId);
    };

    const getGiftReminderForContact = (contactId: string): GiftReminder | undefined => {
        return giftReminders.find(reminder => reminder.contactId === contactId && reminder.isActive);
    };

    const getSuggestedGifts = (_contactId: string, budget?: number): GiftSuggestion[] => {
        let suggestions = [...giftSuggestions];

        // Filter by budget if provided
        if (budget) {
            suggestions = suggestions.filter(gift => gift.price <= budget);
        }

        // Sort by rating and price
        suggestions.sort((a, b) => {
            if (b.rating !== a.rating) {
                return b.rating - a.rating; // Higher rating first
            }
            return a.price - b.price; // Lower price first for same rating
        });

        return suggestions.slice(0, 6); // Return top 6 suggestions
    };

    return (
        <GiftContext.Provider value={{
            giftHistory,
            giftReminders,
            addToGiftHistory,
            createGiftReminder,
            updateGiftReminder,
            deleteGiftReminder,
            getGiftHistoryForContact,
            getGiftReminderForContact,
            getSuggestedGifts
        }}>
            {children}
        </GiftContext.Provider>
    );
};

export const useGift = () => {
    const context = useContext(GiftContext);
    if (!context) {
        throw new Error("useGift must be used within a GiftProvider");
    }
    return context;
};
