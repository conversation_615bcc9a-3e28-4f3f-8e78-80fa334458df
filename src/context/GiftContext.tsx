import { createContext, useContext, useState, useEffect } from "react";
import type { ReactNode } from "react";
import { useAuth } from "./AuthContext";
import { databaseService } from "../services/databaseService";

interface GiftHistoryItem {
    id: string;
    giftName: string;
    recipient: string;
    recipientId: string;
    date: string;
    amount: number;
    category: string;
    notes?: string;
}

interface GiftReminder {
    id: string;
    contactId: string;
    contactName: string;
    reminderDate: string;
    eventDate: string; // birthday date
    giftIdeas: string[];
    budget?: number;
    notes?: string;
    isActive: boolean;
}

interface GiftSuggestion {
    id: string;
    name: string;
    category: string;
    price: number;
    rating: number;
    description: string;
    ageGroup?: string;
    interests?: string[];
}

interface GiftContextType {
    giftHistory: GiftHistoryItem[];
    giftReminders: GiftReminder[];
    addToGiftHistory: (item: Omit<GiftHistoryItem, 'id'>) => void;
    createGiftReminder: (reminder: Omit<GiftReminder, 'id'>) => void;
    updateGiftReminder: (id: string, updates: Partial<GiftReminder>) => void;
    deleteGiftReminder: (id: string) => void;
    getGiftHistoryForContact: (contactId: string) => GiftHistoryItem[];
    getGiftReminderForContact: (contactId: string) => GiftReminder | undefined;
    getSuggestedGifts: (contactId: string, budget?: number) => GiftSuggestion[];
}

const GiftContext = createContext<GiftContextType | undefined>(undefined);

const GIFT_HISTORY_STORAGE_KEY = "birthdaySaaSGiftHistory_";
const GIFT_REMINDERS_STORAGE_KEY = "birthdaySaaSGiftReminders_";

// Sample gift suggestions data
const giftSuggestions: GiftSuggestion[] = [
    {
        id: '1',
        name: 'Wireless Bluetooth Headphones',
        category: 'Electronics',
        price: 199.99,
        rating: 4.5,
        description: 'High-quality noise-canceling headphones perfect for music lovers.',
        ageGroup: 'adult',
        interests: ['music', 'technology']
    },
    {
        id: '2',
        name: 'Artisan Coffee Subscription',
        category: 'Food & Beverage',
        price: 89.99,
        rating: 4.8,
        description: '3-month subscription to premium coffee beans from around the world.',
        ageGroup: 'adult',
        interests: ['coffee', 'food']
    },
    {
        id: '3',
        name: 'Leather Bound Journal',
        category: 'Stationery',
        price: 45.00,
        rating: 4.3,
        description: 'Handcrafted leather journal perfect for writing and note-taking.',
        ageGroup: 'adult',
        interests: ['writing', 'organization']
    },
    {
        id: '4',
        name: 'Smart Fitness Watch',
        category: 'Electronics',
        price: 299.99,
        rating: 4.6,
        description: 'Advanced fitness tracking with heart rate monitoring and GPS.',
        ageGroup: 'adult',
        interests: ['fitness', 'health', 'technology']
    },
    {
        id: '5',
        name: 'Gourmet Chocolate Box',
        category: 'Food & Beverage',
        price: 35.00,
        rating: 4.7,
        description: 'Premium assorted chocolates from renowned chocolatiers.',
        ageGroup: 'all',
        interests: ['food', 'sweets']
    },
    {
        id: '6',
        name: 'Silk Scarf',
        category: 'Clothing',
        price: 75.00,
        rating: 4.4,
        description: 'Elegant silk scarf with beautiful patterns.',
        ageGroup: 'adult',
        interests: ['fashion', 'accessories']
    },
    {
        id: '7',
        name: 'Board Game Collection',
        category: 'Entertainment',
        price: 120.00,
        rating: 4.5,
        description: 'Set of popular strategy and party board games.',
        ageGroup: 'all',
        interests: ['games', 'social']
    },
    {
        id: '8',
        name: 'Essential Oils Diffuser Set',
        category: 'Home & Garden',
        price: 65.00,
        rating: 4.3,
        description: 'Aromatherapy diffuser with starter essential oils collection.',
        ageGroup: 'adult',
        interests: ['wellness', 'home']
    }
];

export const GiftProvider = ({ children }: { children: ReactNode; }) => {
    const { user } = useAuth();
    const [giftHistory, setGiftHistory] = useState<GiftHistoryItem[]>([]);
    const [giftReminders, setGiftReminders] = useState<GiftReminder[]>([]);

    // Load data from localStorage when user changes
    useEffect(() => {
        if (user) {
            try {
                const savedHistory = localStorage.getItem(GIFT_HISTORY_STORAGE_KEY + user.email);
                const savedReminders = localStorage.getItem(GIFT_REMINDERS_STORAGE_KEY + user.email);

                if (savedHistory) {
                    setGiftHistory(JSON.parse(savedHistory));
                }

                if (savedReminders) {
                    setGiftReminders(JSON.parse(savedReminders));
                }
            } catch (error) {
                console.error("Error loading gift data:", error);
            }
        }
    }, [user]);

    // Save to localStorage when data changes
    useEffect(() => {
        if (user) {
            localStorage.setItem(GIFT_HISTORY_STORAGE_KEY + user.email, JSON.stringify(giftHistory));
        }
    }, [giftHistory, user]);

    useEffect(() => {
        if (user) {
            localStorage.setItem(GIFT_REMINDERS_STORAGE_KEY + user.email, JSON.stringify(giftReminders));
        }
    }, [giftReminders, user]);

    const addToGiftHistory = (item: Omit<GiftHistoryItem, 'id'>) => {
        const newItem: GiftHistoryItem = {
            ...item,
            id: Date.now().toString()
        };
        setGiftHistory(prev => [newItem, ...prev]);
    };

    const createGiftReminder = (reminder: Omit<GiftReminder, 'id'>) => {
        const newReminder: GiftReminder = {
            ...reminder,
            id: Date.now().toString()
        };
        setGiftReminders(prev => [newReminder, ...prev]);
    };

    const updateGiftReminder = (id: string, updates: Partial<GiftReminder>) => {
        setGiftReminders(prev =>
            prev.map(reminder =>
                reminder.id === id ? { ...reminder, ...updates } : reminder
            )
        );
    };

    const deleteGiftReminder = (id: string) => {
        setGiftReminders(prev => prev.filter(reminder => reminder.id !== id));
    };

    const getGiftHistoryForContact = (contactId: string): GiftHistoryItem[] => {
        return giftHistory.filter(item => item.recipientId === contactId);
    };

    const getGiftReminderForContact = (contactId: string): GiftReminder | undefined => {
        return giftReminders.find(reminder => reminder.contactId === contactId && reminder.isActive);
    };

    const getSuggestedGifts = (_contactId: string, budget?: number): GiftSuggestion[] => {
        let suggestions = [...giftSuggestions];

        // Filter by budget if provided
        if (budget) {
            suggestions = suggestions.filter(gift => gift.price <= budget);
        }

        // Sort by rating and price
        suggestions.sort((a, b) => {
            if (b.rating !== a.rating) {
                return b.rating - a.rating; // Higher rating first
            }
            return a.price - b.price; // Lower price first for same rating
        });

        return suggestions.slice(0, 6); // Return top 6 suggestions
    };

    return (
        <GiftContext.Provider value={{
            giftHistory,
            giftReminders,
            addToGiftHistory,
            createGiftReminder,
            updateGiftReminder,
            deleteGiftReminder,
            getGiftHistoryForContact,
            getGiftReminderForContact,
            getSuggestedGifts
        }}>
            {children}
        </GiftContext.Provider>
    );
};

export const useGift = () => {
    const context = useContext(GiftContext);
    if (!context) {
        throw new Error("useGift must be used within a GiftProvider");
    }
    return context;
};
