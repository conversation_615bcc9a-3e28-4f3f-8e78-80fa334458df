import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '../AuthContext';

// Mock Firebase
jest.mock('../../firebase', () => ({
    auth: {
        currentUser: null,
    },
    googleProvider: {},
}));

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
    signInWithPopup: jest.fn(),
    signOut: jest.fn(),
}));

// Test component to access auth context
const TestComponent = () => {
    const { user, login, signup, logout, loginWithGoogle, isAuthLoading } = useAuth();
    
    return (
        <div>
            <div data-testid="user">{user?.email || 'No user'}</div>
            <div data-testid="loading">{isAuthLoading ? 'Loading' : 'Not loading'}</div>
            <button onClick={() => login('<EMAIL>', 'password123')}>
                Login
            </button>
            <button onClick={() => signup('<EMAIL>', 'password123')}>
                Signup
            </button>
            <button onClick={() => logout()}>Logout</button>
            <button onClick={() => loginWithGoogle()}>Google Login</button>
        </div>
    );
};

const renderWithAuthProvider = (component: React.ReactElement) => {
    return render(
        <AuthProvider>
            {component}
        </AuthProvider>
    );
};

describe('AuthContext', () => {
    beforeEach(() => {
        localStorage.clear();
        sessionStorage.clear();
        jest.clearAllMocks();
    });

    it('should provide initial auth state', () => {
        renderWithAuthProvider(<TestComponent />);
        
        expect(screen.getByTestId('user')).toHaveTextContent('No user');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    it('should handle user signup successfully', async () => {
        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        const signupButton = screen.getByText('Signup');
        
        await act(async () => {
            await user.click(signupButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        });
    });

    it('should handle user login successfully', async () => {
        // First signup a user
        localStorage.setItem('birthdaySaaSUserDB', JSON.stringify({
            '<EMAIL>': 'hashedpassword'
        }));

        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        const loginButton = screen.getByText('Login');
        
        await act(async () => {
            await user.click(loginButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        });
    });

    it('should handle login failure for non-existent user', async () => {
        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        const loginButton = screen.getByText('Login');
        
        await act(async () => {
            await user.click(loginButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('No user');
        });
    });

    it('should handle logout', async () => {
        // Setup logged in user
        sessionStorage.setItem('birthdaySaaSCurrentUser', '<EMAIL>');
        
        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        // Wait for initial user to be set
        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        });
        
        const logoutButton = screen.getByText('Logout');
        
        await act(async () => {
            await user.click(logoutButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('No user');
        });
    });

    it('should show loading state during authentication', async () => {
        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        const loginButton = screen.getByText('Login');
        
        await act(async () => {
            await user.click(loginButton);
        });

        // Should show loading state briefly
        expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
        
        await waitFor(() => {
            expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
        });
    });

    it('should restore user from session storage on mount', async () => {
        sessionStorage.setItem('birthdaySaaSCurrentUser', '<EMAIL>');
        
        renderWithAuthProvider(<TestComponent />);
        
        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        });
    });

    it('should prevent signup for existing user', async () => {
        // Setup existing user
        localStorage.setItem('birthdaySaaSUserDB', JSON.stringify({
            '<EMAIL>': 'hashedpassword'
        }));

        const user = userEvent.setup();
        renderWithAuthProvider(<TestComponent />);
        
        const signupButton = screen.getByText('Signup');
        
        await act(async () => {
            await user.click(signupButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('user')).toHaveTextContent('No user');
        });
    });
});
