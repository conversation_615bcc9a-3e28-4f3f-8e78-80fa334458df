import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ContactsProvider, useContacts } from '../ContactsContext';
import { AuthProvider } from '../AuthContext';
import { SubscriptionProvider } from '../SubscriptionContext';
import { ErrorProvider } from '../ErrorContext';
import { Contact } from '../../types';

// Test component to access contacts context
const TestComponent = () => {
    const { contacts, addContact, removeContact } = useContacts();
    
    const handleAddContact = () => {
        const newContact: Contact = {
            id: 'test-id',
            name: 'Test User',
            birthday: '1990-01-01',
            category: 'Friends'
        };
        addContact(newContact);
    };
    
    const handleRemoveContact = () => {
        if (contacts.length > 0) {
            removeContact(contacts[0].id);
        }
    };
    
    return (
        <div>
            <div data-testid="contact-count">{contacts.length}</div>
            <div data-testid="contacts">
                {contacts.map(contact => (
                    <div key={contact.id} data-testid={`contact-${contact.id}`}>
                        {contact.name} - {contact.birthday} - {contact.category}
                    </div>
                ))}
            </div>
            <button onClick={handleAddContact}>Add Contact</button>
            <button onClick={handleRemoveContact}>Remove Contact</button>
        </div>
    );
};

// Mock user for auth context
const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
    const mockAuthValue = {
        user: { email: '<EMAIL>' },
        login: jest.fn(),
        signup: jest.fn(),
        logout: jest.fn(),
        loginWithGoogle: jest.fn(),
        isAuthLoading: false,
    };
    
    return (
        <AuthProvider>
            {children}
        </AuthProvider>
    );
};

const renderWithProviders = (component: React.ReactElement) => {
    return render(
        <ErrorProvider>
            <MockAuthProvider>
                <SubscriptionProvider>
                    <ContactsProvider>
                        {component}
                    </ContactsProvider>
                </SubscriptionProvider>
            </MockAuthProvider>
        </ErrorProvider>
    );
};

describe('ContactsContext', () => {
    beforeEach(() => {
        localStorage.clear();
        sessionStorage.clear();
        jest.clearAllMocks();
        
        // Mock user session
        sessionStorage.setItem('birthdaySaaSCurrentUser', '<EMAIL>');
    });

    it('should provide initial contacts state', () => {
        renderWithProviders(<TestComponent />);
        
        // Should start with empty contacts (since no saved data)
        expect(screen.getByTestId('contact-count')).toHaveTextContent('0');
    });

    it('should add a new contact', async () => {
        const user = userEvent.setup();
        renderWithProviders(<TestComponent />);
        
        const addButton = screen.getByText('Add Contact');
        
        await act(async () => {
            await user.click(addButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('contact-count')).toHaveTextContent('1');
            expect(screen.getByTestId('contact-test-id')).toHaveTextContent('Test User - 1990-01-01 - Friends');
        });
    });

    it('should remove a contact', async () => {
        const user = userEvent.setup();
        renderWithProviders(<TestComponent />);
        
        // First add a contact
        const addButton = screen.getByText('Add Contact');
        await act(async () => {
            await user.click(addButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('contact-count')).toHaveTextContent('1');
        });

        // Then remove it
        const removeButton = screen.getByText('Remove Contact');
        await act(async () => {
            await user.click(removeButton);
        });

        await waitFor(() => {
            expect(screen.getByTestId('contact-count')).toHaveTextContent('0');
        });
    });

    it('should persist contacts to localStorage', async () => {
        const user = userEvent.setup();
        renderWithProviders(<TestComponent />);
        
        const addButton = screen.getByText('Add Contact');
        
        await act(async () => {
            await user.click(addButton);
        });

        await waitFor(() => {
            const savedContacts = localStorage.getItem('<EMAIL>');
            expect(savedContacts).toBeTruthy();
            
            const parsedContacts = JSON.parse(savedContacts!);
            expect(parsedContacts).toHaveLength(1);
            expect(parsedContacts[0].name).toBe('Test User');
        });
    });

    it('should load contacts from localStorage', () => {
        // Pre-populate localStorage
        const existingContacts = [
            {
                id: 'existing-1',
                name: 'Existing User',
                birthday: '1985-05-15',
                category: 'Family'
            }
        ];
        localStorage.setItem(
            '<EMAIL>',
            JSON.stringify(existingContacts)
        );

        renderWithProviders(<TestComponent />);
        
        expect(screen.getByTestId('contact-count')).toHaveTextContent('1');
        expect(screen.getByTestId('contact-existing-1')).toHaveTextContent('Existing User - 1985-05-15 - Family');
    });

    it('should handle localStorage errors gracefully', () => {
        // Mock localStorage to throw an error
        const originalGetItem = localStorage.getItem;
        localStorage.getItem = jest.fn().mockImplementation(() => {
            throw new Error('Storage error');
        });

        // Should not crash
        expect(() => {
            renderWithProviders(<TestComponent />);
        }).not.toThrow();

        // Restore original implementation
        localStorage.getItem = originalGetItem;
    });
});
