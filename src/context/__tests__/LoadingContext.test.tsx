import { render } from '@testing-library/react';
import { LoadingProvider, useLoading, useComponentLoading, useAsyncWithLoading } from '../LoadingContext';

// Test component to access loading context
const TestComponent = () => {
    const { loadingStates, isLoading, setLoading, isAnyLoading, clearAllLoading } = useLoading();
    
    const handleSetLoading = () => {
        setLoading('test-operation', true);
    };
    
    const handleClearLoading = () => {
        setLoading('test-operation', false);
    };
    
    return (
        <div>
            <div data-testid="is-any-loading">{isAnyLoading ? 'true' : 'false'}</div>
            <div data-testid="test-loading">{isLoading('test-operation') ? 'true' : 'false'}</div>
            <div data-testid="loading-states">{JSON.stringify(loadingStates)}</div>
            <button onClick={handleSetLoading}>Set Loading</button>
            <button onClick={handleClearLoading}>Clear Loading</button>
            <button onClick={clearAllLoading}>Clear All Loading</button>
        </div>
    );
};

// Test component for component loading hook
const ComponentLoadingTestComponent = () => {
    const { isLoading, setLoading } = useComponentLoading('test-component');
    
    return (
        <div>
            <div data-testid="component-loading">{isLoading ? 'true' : 'false'}</div>
            <button onClick={() => setLoading(true)}>Set Component Loading</button>
            <button onClick={() => setLoading(false)}>Clear Component Loading</button>
        </div>
    );
};

// Test component for async with loading hook
const AsyncLoadingTestComponent = () => {
    const { executeWithLoading } = useAsyncWithLoading();
    
    const handleAsyncOperation = async () => {
        await executeWithLoading(
            'async-test',
            () => new Promise(resolve => setTimeout(resolve, 100)),
            () => console.log('Success'),
            (error) => console.error('Error:', error)
        );
    };
    
    return (
        <div>
            <button onClick={handleAsyncOperation}>Execute Async</button>
        </div>
    );
};

const renderWithLoadingProvider = (component: React.ReactElement) => {
    return render(
        <LoadingProvider>
            {component}
        </LoadingProvider>
    );
};

describe('LoadingContext', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should provide initial loading state', () => {
        const { getByTestId } = renderWithLoadingProvider(<TestComponent />);
        
        expect(getByTestId('is-any-loading')).toHaveTextContent('false');
        expect(getByTestId('test-loading')).toHaveTextContent('false');
        expect(getByTestId('loading-states')).toHaveTextContent('{}');
    });

    it('should set loading state', () => {
        const { getByTestId, getByText } = renderWithLoadingProvider(<TestComponent />);
        
        const setButton = getByText('Set Loading');
        setButton.click();
        
        expect(getByTestId('is-any-loading')).toHaveTextContent('true');
        expect(getByTestId('test-loading')).toHaveTextContent('true');
        expect(getByTestId('loading-states')).toHaveTextContent('{"test-operation":true}');
    });

    it('should clear loading state', () => {
        const { getByTestId, getByText } = renderWithLoadingProvider(<TestComponent />);
        
        // First set loading
        const setButton = getByText('Set Loading');
        setButton.click();
        
        expect(getByTestId('test-loading')).toHaveTextContent('true');
        
        // Then clear it
        const clearButton = getByText('Clear Loading');
        clearButton.click();
        
        expect(getByTestId('is-any-loading')).toHaveTextContent('false');
        expect(getByTestId('test-loading')).toHaveTextContent('false');
    });

    it('should clear all loading states', () => {
        const { getByTestId, getByText } = renderWithLoadingProvider(<TestComponent />);
        
        // Set multiple loading states
        const setButton = getByText('Set Loading');
        setButton.click();
        
        expect(getByTestId('is-any-loading')).toHaveTextContent('true');
        
        // Clear all
        const clearAllButton = getByText('Clear All Loading');
        clearAllButton.click();
        
        expect(getByTestId('is-any-loading')).toHaveTextContent('false');
        expect(getByTestId('loading-states')).toHaveTextContent('{}');
    });

    it('should handle multiple loading states', () => {
        const MultipleLoadingComponent = () => {
            const { setLoading, isLoading, isAnyLoading } = useLoading();
            
            return (
                <div>
                    <div data-testid="is-any-loading">{isAnyLoading ? 'true' : 'false'}</div>
                    <div data-testid="operation1-loading">{isLoading('operation1') ? 'true' : 'false'}</div>
                    <div data-testid="operation2-loading">{isLoading('operation2') ? 'true' : 'false'}</div>
                    <button onClick={() => setLoading('operation1', true)}>Set Op1</button>
                    <button onClick={() => setLoading('operation2', true)}>Set Op2</button>
                    <button onClick={() => setLoading('operation1', false)}>Clear Op1</button>
                </div>
            );
        };
        
        const { getByTestId, getByText } = renderWithLoadingProvider(<MultipleLoadingComponent />);
        
        // Set first operation
        getByText('Set Op1').click();
        expect(getByTestId('is-any-loading')).toHaveTextContent('true');
        expect(getByTestId('operation1-loading')).toHaveTextContent('true');
        expect(getByTestId('operation2-loading')).toHaveTextContent('false');
        
        // Set second operation
        getByText('Set Op2').click();
        expect(getByTestId('is-any-loading')).toHaveTextContent('true');
        expect(getByTestId('operation1-loading')).toHaveTextContent('true');
        expect(getByTestId('operation2-loading')).toHaveTextContent('true');
        
        // Clear first operation
        getByText('Clear Op1').click();
        expect(getByTestId('is-any-loading')).toHaveTextContent('true'); // Still true because op2 is loading
        expect(getByTestId('operation1-loading')).toHaveTextContent('false');
        expect(getByTestId('operation2-loading')).toHaveTextContent('true');
    });

    it('should work with component loading hook', () => {
        const { getByTestId, getByText } = renderWithLoadingProvider(<ComponentLoadingTestComponent />);
        
        expect(getByTestId('component-loading')).toHaveTextContent('false');
        
        // Set component loading
        getByText('Set Component Loading').click();
        expect(getByTestId('component-loading')).toHaveTextContent('true');
        
        // Clear component loading
        getByText('Clear Component Loading').click();
        expect(getByTestId('component-loading')).toHaveTextContent('false');
    });

    it('should work with async loading hook', async () => {
        jest.useFakeTimers();
        
        const TestWithAsync = () => (
            <LoadingProvider>
                <TestComponent />
                <AsyncLoadingTestComponent />
            </LoadingProvider>
        );
        
        const { getByTestId, getByText } = render(<TestWithAsync />);
        
        // Execute async operation
        getByText('Execute Async').click();
        
        // Should be loading
        expect(getByTestId('is-any-loading')).toHaveTextContent('true');
        
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Should complete loading
        expect(getByTestId('is-any-loading')).toHaveTextContent('false');
        
        jest.useRealTimers();
    });

    it('should throw error when used outside provider', () => {
        // Mock console.error to avoid noise in test output
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        
        expect(() => {
            render(<TestComponent />);
        }).toThrow('useLoading must be used within a LoadingProvider');
        
        consoleSpy.mockRestore();
    });
});
