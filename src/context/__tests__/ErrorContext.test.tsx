import { render } from '@testing-library/react';
import { Error<PERSON>rovider, useError, useErrorNotification } from '../ErrorContext';

// Test component to access error context
const TestComponent = () => {
    const { errors, addError, removeError, clearAllErrors } = useError();
    
    const handleAddError = () => {
        addError('Test error message', 'error', false);
    };
    
    const handleAddWarning = () => {
        addError('Test warning message', 'warning', true);
    };
    
    const handleRemoveError = () => {
        if (errors.length > 0) {
            removeError(errors[0].id);
        }
    };
    
    return (
        <div>
            <div data-testid="error-count">{errors.length}</div>
            <div data-testid="errors">
                {errors.map(error => (
                    <div key={error.id} data-testid={`error-${error.id}`}>
                        {error.message} - {error.type}
                    </div>
                ))}
            </div>
            <button onClick={handleAddError}>Add Error</button>
            <button onClick={handleAddWarning}>Add Warning</button>
            <button onClick={handleRemoveError}>Remove Error</button>
            <button onClick={clearAllErrors}>Clear All</button>
        </div>
    );
};

// Test component for error notification hook
const NotificationTestComponent = () => {
    const { showError, showWarning, showInfo, showSuccess } = useErrorNotification();
    
    return (
        <div>
            <button onClick={() => showError('Error message')}>Show Error</button>
            <button onClick={() => showWarning('Warning message')}>Show Warning</button>
            <button onClick={() => showInfo('Info message')}>Show Info</button>
            <button onClick={() => showSuccess('Success message')}>Show Success</button>
        </div>
    );
};

const renderWithErrorProvider = (component: React.ReactElement) => {
    return render(
        <ErrorProvider>
            {component}
        </ErrorProvider>
    );
};

describe('ErrorContext', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Clear any existing timers
        jest.clearAllTimers();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.runOnlyPendingTimers();
        jest.useRealTimers();
    });

    it('should provide initial error state', () => {
        const { getByTestId } = renderWithErrorProvider(<TestComponent />);
        
        expect(getByTestId('error-count')).toHaveTextContent('0');
    });

    it('should add an error', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        const addButton = getByText('Add Error');
        addButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        expect(getByTestId('errors')).toHaveTextContent('Test error message - error');
    });

    it('should add a warning', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        const addButton = getByText('Add Warning');
        addButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        expect(getByTestId('errors')).toHaveTextContent('Test warning message - warning');
    });

    it('should remove an error', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        // First add an error
        const addButton = getByText('Add Error');
        addButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        
        // Then remove it
        const removeButton = getByText('Remove Error');
        removeButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('0');
    });

    it('should clear all errors', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        // Add multiple errors
        const addErrorButton = getByText('Add Error');
        const addWarningButton = getByText('Add Warning');
        
        addErrorButton.click();
        addWarningButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('2');
        
        // Clear all
        const clearButton = getByText('Clear All');
        clearButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('0');
    });

    it('should auto-hide warnings after timeout', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        const addButton = getByText('Add Warning');
        addButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        
        // Fast-forward time by 5 seconds
        jest.advanceTimersByTime(5000);
        
        expect(getByTestId('error-count')).toHaveTextContent('0');
    });

    it('should not auto-hide errors', () => {
        const { getByTestId, getByText } = renderWithErrorProvider(<TestComponent />);
        
        const addButton = getByText('Add Error');
        addButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        
        // Fast-forward time by 5 seconds
        jest.advanceTimersByTime(5000);
        
        // Error should still be there
        expect(getByTestId('error-count')).toHaveTextContent('1');
    });

    it('should work with error notification hook', () => {
        const TestWithNotifications = () => (
            <ErrorProvider>
                <TestComponent />
                <NotificationTestComponent />
            </ErrorProvider>
        );
        
        const { getByTestId, getByText } = render(<TestWithNotifications />);
        
        const showErrorButton = getByText('Show Error');
        showErrorButton.click();
        
        expect(getByTestId('error-count')).toHaveTextContent('1');
        expect(getByTestId('errors')).toHaveTextContent('Error message - error');
    });

    it('should handle different notification types', () => {
        const TestWithNotifications = () => (
            <ErrorProvider>
                <TestComponent />
                <NotificationTestComponent />
            </ErrorProvider>
        );
        
        const { getByTestId, getByText } = render(<TestWithNotifications />);
        
        // Test all notification types
        getByText('Show Error').click();
        getByText('Show Warning').click();
        getByText('Show Info').click();
        getByText('Show Success').click();
        
        expect(getByTestId('error-count')).toHaveTextContent('4');
        
        const errorsContainer = getByTestId('errors');
        expect(errorsContainer).toHaveTextContent('Error message - error');
        expect(errorsContainer).toHaveTextContent('Warning message - warning');
        expect(errorsContainer).toHaveTextContent('Info message - info');
        expect(errorsContainer).toHaveTextContent('Success message - success');
    });

    it('should throw error when used outside provider', () => {
        // Mock console.error to avoid noise in test output
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        
        expect(() => {
            render(<TestComponent />);
        }).toThrow('useError must be used within an ErrorProvider');
        
        consoleSpy.mockRestore();
    });
});
