import { createContext, useContext, useState, useCallback } from 'react';
import type { ReactNode } from 'react';

interface LoadingState {
    [key: string]: boolean;
}

interface LoadingContextType {
    loadingStates: LoadingState;
    isLoading: (key: string) => boolean;
    setLoading: (key: string, loading: boolean) => void;
    isAnyLoading: boolean;
    clearAllLoading: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider = ({ children }: { children: ReactNode; }) => {
    const [loadingStates, setLoadingStates] = useState<LoadingState>({});

    const isLoading = useCallback((key: string): boolean => {
        return loadingStates[key] || false;
    }, [loadingStates]);

    const setLoading = useCallback((key: string, loading: boolean) => {
        setLoadingStates(prev => ({
            ...prev,
            [key]: loading,
        }));
    }, []);

    const clearAllLoading = useCallback(() => {
        setLoadingStates({});
    }, []);

    const isAnyLoading = Object.values(loadingStates).some(Boolean);

    return (
        <LoadingContext.Provider
            value={{
                loadingStates,
                isLoading,
                setLoading,
                isAnyLoading,
                clearAllLoading,
            }}
        >
            {children}
        </LoadingContext.Provider>
    );
};

export const useLoading = () => {
    const context = useContext(LoadingContext);
    if (!context) {
        throw new Error('useLoading must be used within a LoadingProvider');
    }
    return context;
};

// Convenience hooks for common loading operations
export const useAsyncWithLoading = () => {
    const { setLoading } = useLoading();

    const executeWithLoading = useCallback(
        async <T,>(
            key: string,
            asyncOperation: () => Promise<T>,
            onSuccess?: (result: T) => void,
            onError?: (error: Error) => void
        ): Promise<T | null> => {
            setLoading(key, true);
            try {
                const result = await asyncOperation();
                onSuccess?.(result);
                return result;
            } catch (error) {
                const err = error instanceof Error ? error : new Error('Unknown error');
                onError?.(err);
                console.error(`Error in ${key}:`, err);
                return null;
            } finally {
                setLoading(key, false);
            }
        },
        [setLoading]
    );

    return { executeWithLoading };
};

// Hook for managing component-level loading states
export const useComponentLoading = (componentName: string) => {
    const { isLoading, setLoading } = useLoading();

    const isComponentLoading = isLoading(componentName);

    const setComponentLoading = useCallback((loading: boolean) => {
        setLoading(componentName, loading);
    }, [componentName, setLoading]);

    return {
        isLoading: isComponentLoading,
        setLoading: setComponentLoading,
    };
};
