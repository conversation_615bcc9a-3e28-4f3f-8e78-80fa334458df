import { createContext, useContext, useState, useCallback, useMemo } from 'react';
import type { ReactNode } from 'react';

interface ErrorInfo {
    id: string;
    message: string;
    type: 'error' | 'warning' | 'info' | 'success';
    timestamp: Date;
    autoHide?: boolean;
}

interface ErrorContextType {
    errors: ErrorInfo[];
    addError: (message: string, type?: ErrorInfo['type'], autoHide?: boolean) => string;
    removeError: (id: string) => void;
    clearAllErrors: () => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export const ErrorProvider = ({ children }: { children: ReactNode; }) => {
    const [errors, setErrors] = useState<ErrorInfo[]>([]);

    const addError = useCallback((
        message: string,
        type: ErrorInfo['type'] = 'error',
        autoHide: boolean = true
    ): string => {
        const id = Math.random().toString(36).substr(2, 9);
        const newError: ErrorInfo = {
            id,
            message,
            type,
            timestamp: new Date(),
            autoHide,
        };

        setErrors(prev => [...prev, newError]);

        // Auto-hide after 5 seconds for non-error types
        if (autoHide && type !== 'error') {
            setTimeout(() => {
                removeError(id);
            }, 5000);
        }

        return id;
    }, []);

    const removeError = useCallback((id: string) => {
        setErrors(prev => prev.filter(error => error.id !== id));
    }, []);

    const clearAllErrors = useCallback(() => {
        setErrors([]);
    }, []);

    return (
        <ErrorContext.Provider value={{ errors, addError, removeError, clearAllErrors }}>
            {children}
        </ErrorContext.Provider>
    );
};

export const useError = () => {
    const context = useContext(ErrorContext);
    if (!context) {
        throw new Error('useError must be used within an ErrorProvider');
    }
    return context;
};

// Convenience hooks for different error types
export const useErrorNotification = () => {
    const { addError } = useError();

    return useMemo(() => ({
        showError: (message: string) => addError(message, 'error', false),
        showWarning: (message: string) => addError(message, 'warning', true),
        showInfo: (message: string) => addError(message, 'info', true),
        showSuccess: (message: string) => addError(message, 'success', true),
    }), [addError]);
};
