import { createContext, useContext, useState, useEffect } from "react";
import type { ReactNode } from "react";
import { useAuth } from "./AuthContext";

type Plan = "Free" | "Trial" | "Standard" | "Elite" | "Premium" | "Enterprise";

interface PlanDetails {
    name: string;
    price: number;
    priceId: string;
    features: string[];
    popular?: boolean;
}

interface SubscriptionContextType {
    plan: Plan;
    trialDaysLeft: number;
    isTrialActive: boolean;
    planDetails: Record<Plan, PlanDetails>;
    upgradeToPlan: (plan: Plan) => Promise<void>;
    startTrial: () => void;
    cancelSubscription: () => void;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider = ({ children }: { children: ReactNode; }) => {
    const { user } = useAuth();
    const [plan, setPlan] = useState<Plan>("Free");
    const [trialStartDate, setTrialStartDate] = useState<Date | null>(null);

    // Plan details with Stripe price IDs (these would be your actual Stripe price IDs)
    const planDetails: Record<Plan, PlanDetails> = {
        Free: {
            name: "Free",
            price: 0,
            priceId: "",
            features: ["Up to 10 contacts", "Basic reminders", "Email support"]
        },
        Trial: {
            name: "14-Day Trial",
            price: 0,
            priceId: "",
            features: ["All Premium features", "Unlimited contacts", "Advanced analytics", "Priority support"]
        },
        Standard: {
            name: "Standard",
            price: 7.99,
            priceId: "price_standard_monthly", // Replace with actual Stripe price ID
            features: ["Up to 100 contacts", "Advanced reminders", "Basic analytics", "Email support"]
        },
        Elite: {
            name: "Elite",
            price: 15.99,
            priceId: "price_elite_monthly", // Replace with actual Stripe price ID
            features: ["Up to 500 contacts", "Premium reminders", "Advanced analytics", "Priority support", "Gift suggestions"],
            popular: true
        },
        Premium: {
            name: "Premium",
            price: 23.99,
            priceId: "price_premium_monthly", // Replace with actual Stripe price ID
            features: ["Unlimited contacts", "All features", "Advanced analytics", "24/7 support", "Custom integrations", "Team collaboration"]
        },
        Enterprise: {
            name: "Enterprise",
            price: 0, // Custom pricing
            priceId: "", // No Stripe price ID - contact sales
            features: ["Everything in Premium", "Custom integrations", "Dedicated account manager", "SLA guarantees", "Advanced security", "Custom training", "Priority implementation", "White-label options"]
        }
    };

    // Calculate trial days left
    const trialDaysLeft = trialStartDate
        ? Math.max(0, 14 - Math.floor((Date.now() - trialStartDate.getTime()) / (1000 * 60 * 60 * 24)))
        : 0;

    const isTrialActive = plan === "Trial" && trialDaysLeft > 0;

    useEffect(() => {
        if (user) {
            const savedPlan = localStorage.getItem(`birthdaySaaSPlan_${user.email}`) as Plan;
            const savedTrialStart = localStorage.getItem(`trialStartDate_${user.email}`);

            if (savedPlan) {
                setPlan(savedPlan);
            }

            if (savedTrialStart) {
                setTrialStartDate(new Date(savedTrialStart));
            }

            // Check if trial has expired
            if (savedPlan === "Trial" && savedTrialStart) {
                const trialStart = new Date(savedTrialStart);
                const daysSinceStart = Math.floor((Date.now() - trialStart.getTime()) / (1000 * 60 * 60 * 24));
                if (daysSinceStart >= 14) {
                    setPlan("Free");
                    localStorage.setItem(`birthdaySaaSPlan_${user.email}`, "Free");
                }
            }
        }
    }, [user]);

    useEffect(() => {
        if (user) {
            localStorage.setItem(`birthdaySaaSPlan_${user.email}`, plan);
        }
    }, [plan, user]);

    const startTrial = () => {
        if (user) {
            const now = new Date();
            setPlan("Trial");
            setTrialStartDate(now);
            localStorage.setItem(`birthdaySaaSPlan_${user.email}`, "Trial");
            localStorage.setItem(`trialStartDate_${user.email}`, now.toISOString());
        }
    };

    const upgradeToPlan = async (newPlan: Plan) => {
        if (!user) return;

        if (newPlan === "Free" || newPlan === "Trial") {
            setPlan(newPlan);
            return;
        }

        if (newPlan === "Enterprise") {
            // Enterprise plan requires contacting sales
            alert("Enterprise plan requires custom pricing. Please contact our sales team for more information.");
            return;
        }

        try {
            // Here you would integrate with Stripe
            // For now, we'll simulate the upgrade
            console.log(`Upgrading to ${newPlan}...`);

            // Simulate Stripe checkout
            const confirmed = window.confirm(`Upgrade to ${planDetails[newPlan].name} for £${planDetails[newPlan].price}/month?`);

            if (confirmed) {
                setPlan(newPlan);
                alert(`Successfully upgraded to ${planDetails[newPlan].name}!`);
            }
        } catch (error) {
            console.error("Upgrade failed:", error);
            alert("Upgrade failed. Please try again.");
        }
    };

    const cancelSubscription = () => {
        if (user) {
            setPlan("Free");
            localStorage.removeItem(`trialStartDate_${user.email}`);
            setTrialStartDate(null);
            alert("Subscription cancelled. You've been moved to the Free plan.");
        }
    };

    return (
        <SubscriptionContext.Provider value={{
            plan,
            trialDaysLeft,
            isTrialActive,
            planDetails,
            upgradeToPlan,
            startTrial,
            cancelSubscription
        }}>
            {children}
        </SubscriptionContext.Provider>
    );
};

export const useSubscription = () => {
    const ctx = useContext(SubscriptionContext);
    if (!ctx) throw new Error("useSubscription must be used within SubscriptionProvider");
    return ctx;
};
