import { createContext, useState, useContext, type ReactNode, useEffect } from "react";
import { type User, type UserRole } from "../types";
import { auth, googleProvider } from "../firebase";
import {
    signInWithPopup,
    signOut as firebaseSignOut,
} from "firebase/auth";
import { userManagementService } from "../services/userManagementService";
import { databaseService } from "../services/databaseService";

interface AuthContextType {
    user: User | null;
    login: (email: string, password: string) => Promise<boolean>;
    signup: (email: string, password: string) => Promise<boolean>;
    logout: () => Promise<void>;
    loginWithGoogle: () => Promise<boolean>;
    isAuthLoading: boolean;
    userRole: UserRole;
    isSystemAdmin: boolean;
    isChurchAdmin: boolean;
    isAdmin: boolean;
    refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Utility functions for password hashing using Web Crypto API
const hashPassword = async (password: string): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
    const hashedInput = await hashPassword(password);
    return hashedInput === hashedPassword;
};

export const AuthProvider = ({ children }: { children: ReactNode; }) => {
    const [user, setUser] = useState<User | null>(null);
    const [isAuthLoading, setIsAuthLoading] = useState(false);
    const [userRole, setUserRole] = useState<UserRole>('user');
    const [isSystemAdmin, setIsSystemAdmin] = useState(false);
    const [isChurchAdmin, setIsChurchAdmin] = useState(false);
    const [isAdmin, setIsAdmin] = useState(false);

    // Update user role information
    const updateUserRoleInfo = async (email: string) => {
        try {
            const role = await userManagementService.getUserRole(email);
            const systemAdmin = await userManagementService.isSystemAdmin(email);
            const churchAdmin = await userManagementService.isChurchAdmin(email);
            const admin = await userManagementService.isAdmin(email);

            setUserRole(role);
            setIsSystemAdmin(systemAdmin);
            setIsChurchAdmin(churchAdmin);
            setIsAdmin(admin);
        } catch (error) {
            console.error('Error updating user role info:', error);
            // Set defaults on error
            setUserRole('user');
            setIsSystemAdmin(false);
            setIsChurchAdmin(false);
            setIsAdmin(false);
        }
    };

    const refreshUserData = async () => {
        if (user?.email) {
            const userData = await userManagementService.getUserByEmail(user.email);
            if (userData) {
                setUser(userData);
                await updateUserRoleInfo(user.email);
            }
        }
    };

    const getUserAuth = async (email: string): Promise<string | null> => {
        try {
            const authRecords = await databaseService.query<{ password_hash: string; }>(
                'SELECT password_hash FROM user_auth WHERE email = ?',
                [email]
            );
            return authRecords[0]?.password_hash || null;
        } catch (error) {
            console.error('Error getting user auth:', error);
            return null;
        }
    };

    const setUserAuth = async (email: string, passwordHash: string): Promise<void> => {
        try {
            // Check if user auth already exists
            const existing = await databaseService.query('SELECT * FROM user_auth WHERE email = ?', [email]);

            if (existing.length === 0) {
                await databaseService.create('user_auth', {
                    email,
                    password_hash: passwordHash
                });
            } else {
                await databaseService.query(
                    'UPDATE user_auth SET password_hash = ?, updated_at = ? WHERE email = ?',
                    [passwordHash, new Date().toISOString(), email]
                );
            }
        } catch (error) {
            console.error('Error setting user auth:', error);
            throw error;
        }
    };

    const login = async (email: string, password: string): Promise<boolean> => {
        setIsAuthLoading(true);
        try {
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            const storedPasswordHash = await getUserAuth(email);
            if (storedPasswordHash) {
                const isValid = await verifyPassword(password, storedPasswordHash);
                if (isValid) {
                    // Get or create user data
                    let userData = await userManagementService.getUserByEmail(email);
                    if (!userData) {
                        // Create user if doesn't exist in user management system
                        userData = await userManagementService.createUser({
                            email,
                            role: 'user',
                        });
                    }

                    setUser(userData);
                    await updateUserRoleInfo(email);
                    await userManagementService.updateLastLogin(email);
                    sessionStorage.setItem("birthdaySaaSCurrentUser", email);
                    return true;
                }
            }
            return false;
        } finally {
            setIsAuthLoading(false);
        }
    };

    const signup = async (email: string, password: string): Promise<boolean> => {
        setIsAuthLoading(true);
        try {
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1200));

            const existingAuth = await getUserAuth(email);
            if (existingAuth) return false; // user exists

            const hashedPassword = await hashPassword(password);
            await setUserAuth(email, hashedPassword);

            // Create user in user management system
            const userData = await userManagementService.createUser({
                email,
                role: 'user',
            });

            setUser(userData);
            await updateUserRoleInfo(email);
            sessionStorage.setItem("birthdaySaaSCurrentUser", email);
            return true;
        } finally {
            setIsAuthLoading(false);
        }
    };

    const logout = async () => {
        await firebaseSignOut(auth);
        setUser(null);
        sessionStorage.removeItem("birthdaySaaSCurrentUser");
    };

    const loginWithGoogle = async () => {
        setIsAuthLoading(true);
        try {
            const result = await signInWithPopup(auth, googleProvider);
            const userEmail = result.user.email;
            if (!userEmail) throw new Error("No email from Google account");

            // Get or create user data
            let userData = await userManagementService.getUserByEmail(userEmail);
            if (!userData) {
                userData = await userManagementService.createUser({
                    email: userEmail,
                    name: result.user.displayName || undefined,
                    role: 'user',
                });
            }

            setUser(userData);
            await updateUserRoleInfo(userEmail);
            await userManagementService.updateLastLogin(userEmail);
            sessionStorage.setItem("birthdaySaaSCurrentUser", userEmail);
            return true;
        } catch (error) {
            console.error("Google login failed:", error);
            return false;
        } finally {
            setIsAuthLoading(false);
        }
    };

    useEffect(() => {
        const initializeAuth = async () => {
            try {
                // Initialize database and user management service
                await databaseService.initialize();
                await userManagementService.initialize();

                const loggedInEmail = sessionStorage.getItem("birthdaySaaSCurrentUser");
                if (loggedInEmail) {
                    // Get full user data from user management system
                    const userData = await userManagementService.getUserByEmail(loggedInEmail);
                    if (userData) {
                        setUser(userData);
                        await updateUserRoleInfo(loggedInEmail);
                    } else {
                        // Fallback to basic user object if not found in user management
                        setUser({ email: loggedInEmail });
                    }
                }

                // Initialize default admin users
                await userManagementService.initializeDefaultAdmins();
            } catch (error) {
                console.error('Failed to initialize auth:', error);
            }
        };

        initializeAuth();
    }, []);

    return (
        <AuthContext.Provider value={{
            user,
            login,
            signup,
            logout,
            loginWithGoogle,
            isAuthLoading,
            userRole,
            isSystemAdmin,
            isChurchAdmin,
            isAdmin,
            refreshUserData
        }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const ctx = useContext(AuthContext);
    if (!ctx) throw new Error("useAuth must be used within AuthProvider");
    return ctx;
};
