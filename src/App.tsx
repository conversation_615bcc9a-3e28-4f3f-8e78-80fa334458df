import { Routes, Route, Navigate } from "react-router-dom";
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { queryClient } from './lib/queryClient';
import { AuthProvider, useAuth } from "./context/AuthContext";
import { ContactsProvider } from "./context/ContactsContext";
import { ErrorProvider } from "./context/ErrorContext";
import { LoadingProvider } from "./context/LoadingContext";
import AuthForm from "./components/AuthForm";
import PrivateRoute from "./components/PrivateRoute";
import ErrorBoundary from "./components/ErrorBoundary";
import ErrorDisplay from "./components/ErrorDisplay";

import MainLayout from "./components/MainLayout";
import LandingPage from "./components/LandingPage";
import SubscriptionPage from "./components/SubscriptionPage";
import { SubscriptionProvider } from "./context/SubscriptionContext";
import { GiftProvider } from "./context/GiftContext";
import SignupForm from "./components/SignupForm";
import LoginForm from "./components/LoginForm";
import ForgotPassword from "./components/ForgotPassword";
import ResetPassword from "./components/ResetPassword";
import DatabaseInitializer from "./components/DatabaseInitializer";
import AdminDashboard from "./components/AdminDashboard";

const App = () => {
  return (
    <ErrorBoundary>
      <ErrorProvider>
        <LoadingProvider>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <SubscriptionProvider>
                <DatabaseInitializer>
                  <ErrorDisplay />
                  <Routes>
                    <Route path="/" element={<LandingPage />} />
                    <Route path="/signup" element={<SignupForm />} />
                    <Route path="/login" element={<LoginForm />} />
                    <Route path="/forgot-password" element={<ForgotPassword />} />
                    <Route path="/reset-password" element={<ResetPassword />} />
                    <Route
                      path="/auth"
                      element={<AuthFormRedirect />}
                    />
                    <Route
                      path="/dashboard"
                      element={
                        <PrivateRoute>
                          <ContactsProvider>
                            <GiftProvider>
                              <MainLayout />
                            </GiftProvider>
                          </ContactsProvider>
                        </PrivateRoute>
                      }
                    />
                    <Route
                      path="/subscription"
                      element={<SubscriptionPage />}
                    />
                    <Route
                      path="/admin"
                      element={
                        <PrivateRoute>
                          <AdminDashboard />
                        </PrivateRoute>
                      }
                    />
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                  <ReactQueryDevtools initialIsOpen={false} />
                </DatabaseInitializer>
              </SubscriptionProvider>
            </AuthProvider>
          </QueryClientProvider>
        </LoadingProvider>
      </ErrorProvider>
    </ErrorBoundary>
  );
};



// Redirect logged-in users from /login to /dashboard
const AuthFormRedirect = () => {
  const { user } = useAuth();
  if (user) return <Navigate to="/dashboard" replace />;
  return <AuthForm />;
};

export default App;
