// Test component to verify login functionality works
import React, { useState } from 'react';
import { databaseService } from './services/databaseService';
import { userManagementService } from './services/userManagementService';

const TestLogin: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addResult('🔄 Starting database and user management tests...');

      // Test 1: Initialize database
      addResult('1️⃣ Testing database initialization...');
      await databaseService.initialize();
      addResult('✅ Database initialized successfully');

      // Test 2: Initialize user management
      addResult('2️⃣ Testing user management initialization...');
      await userManagementService.initialize();
      addResult('✅ User management initialized successfully');

      // Test 3: Create default admins
      addResult('3️⃣ Testing default admin creation...');
      await userManagementService.initializeDefaultAdmins();
      addResult('✅ Default admins created successfully');

      // Test 4: Check if users exist
      addResult('4️⃣ Testing user retrieval...');
      const systemAdmin = await userManagementService.getUserByEmail('<EMAIL>');
      const churchAdmin = await userManagementService.getUserByEmail('<EMAIL>');

      if (systemAdmin) {
        addResult(`✅ System admin found: ${systemAdmin.name} (${systemAdmin.role})`);
      } else {
        addResult('❌ System admin not found');
      }

      if (churchAdmin) {
        addResult(`✅ Church admin found: ${churchAdmin.name} (${churchAdmin.role})`);
      } else {
        addResult('❌ Church admin not found');
      }

      // Test 5: Test user creation
      addResult('5️⃣ Testing new user creation...');
      try {
        const testUser = await userManagementService.createUser({
          email: '<EMAIL>',
          role: 'user'
        });
        addResult(`✅ Test user created: ${testUser.email} (${testUser.role})`);
      } catch (error) {
        addResult(`❌ Failed to create test user: ${error}`);
      }

      // Test 6: Get all users
      addResult('6️⃣ Testing get all users...');
      const allUsers = await userManagementService.getAllUsers();
      addResult(`✅ Found ${allUsers.length} total users`);

      // Test 7: Database stats
      addResult('7️⃣ Testing database stats...');
      const stats = await databaseService.getStats();
      addResult(`✅ Database stats: ${JSON.stringify(stats)}`);

      addResult('🎉 All tests completed successfully!');

    } catch (error) {
      addResult(`❌ Test failed: ${error}`);
      console.error('Test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const clearMigrationHistory = async () => {
    if (!confirm('Are you sure you want to clear migration history? This will allow migrations to run again.')) {
      return;
    }

    setIsRunning(true);
    try {
      addResult('Clearing migration history...');
      await migrationService.clearMigrationHistory();
      addResult('Migration history cleared');
    } catch (error) {
      console.error('Error clearing migration history:', error);
      addResult('Error clearing migration history: ' + error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">🧪 Login System Test</h2>

      <div className="space-y-4 mb-6">
        <button
          onClick={runTests}
          disabled={isRunning}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </button>

        <button
          onClick={clearResults}
          disabled={isRunning}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50 ml-2"
        >
          Clear Results
        </button>

        <button
          onClick={clearMigrationHistory}
          disabled={isRunning}
          className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50 ml-2"
        >
          Clear Migration History
        </button>
      </div>

      {testResults.length > 0 && (
        <div>
          <h3 className="font-semibold text-gray-800 mb-2">Test Results</h3>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className={
                result.includes('❌') ? 'text-red-400' :
                  result.includes('✅') ? 'text-green-400' :
                    result.includes('🔄') ? 'text-yellow-400' :
                      'text-blue-400'
              }>
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Test Information</h3>
        <p className="text-sm text-blue-700">
          This test verifies that the SQLite database and user management system are working correctly.
          It will create the default admin users and test basic CRUD operations.
        </p>
        <div className="mt-2 text-xs text-blue-600">
          <div>• System Admin: <EMAIL> / admin123</div>
          <div>• Church Admin: <EMAIL> / church123</div>
        </div>
      </div>
    </div>
  );
};

export default TestLogin;
