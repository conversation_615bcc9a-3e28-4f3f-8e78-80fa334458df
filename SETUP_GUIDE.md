# 🚀 TrackCelebrations SaaS Setup Guide

Complete setup guide for transforming TrackCelebrations into a full-fledged SaaS application with Stripe integration.

## 📋 Prerequisites

- Node.js 16+ and npm
- Stripe account (free)
- Git
- Code editor (VS Code recommended)

## 🔧 Quick Setup

### 1. Clone and Install

```bash
# Clone the repository
git clone <your-repo-url>
cd trackcelebrations

# Install frontend dependencies
npm install

# Install backend dependencies
cd backend
npm install
cd ..
```

### 2. Stripe Configuration

#### Create Stripe Account
1. Go to [stripe.com](https://stripe.com) and create an account
2. Complete account verification
3. Navigate to Dashboard > Developers > API keys

#### Get API Keys
- **Publishable Key**: `pk_test_...` (for frontend)
- **Secret Key**: `sk_test_...` (for backend)

#### Create Products and Prices
In Stripe Dashboard > Products, create:

**Standard Plan**
- Name: "Standard Plan"
- Price: $7.99/month, $79.99/year
- Copy the Price IDs

**Elite Plan** 
- Name: "Elite Plan" 
- Price: $15.99/month, $159.99/year
- Copy the Price IDs

**Premium Plan**
- Name: "Premium Plan"
- Price: $23.99/month, $239.99/year  
- Copy the Price IDs

### 3. Environment Setup

#### Frontend Environment
```bash
# Copy and edit frontend environment
cp .env.example .env

# Edit .env with your values:
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
VITE_API_BASE_URL=http://localhost:3001/api
```

#### Backend Environment
```bash
# Copy and edit backend environment
cd backend
cp .env.example .env

# Edit .env with your values:
STRIPE_SECRET_KEY=sk_test_your_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
FRONTEND_URL=http://localhost:5173

# Add your actual Stripe Price IDs:
STRIPE_PRICE_STANDARD_MONTHLY=price_your_id_here
STRIPE_PRICE_STANDARD_YEARLY=price_your_id_here
STRIPE_PRICE_ELITE_MONTHLY=price_your_id_here
STRIPE_PRICE_ELITE_YEARLY=price_your_id_here
STRIPE_PRICE_PREMIUM_MONTHLY=price_your_id_here
STRIPE_PRICE_PREMIUM_YEARLY=price_your_id_here
```

### 4. Start Development Servers

#### Terminal 1 - Backend API
```bash
cd backend
npm run dev
```

#### Terminal 2 - Frontend App
```bash
npm run dev
```

Your app will be available at:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 🎯 Testing the SaaS Features

### 1. User Registration & Trial
1. Open http://localhost:5173
2. Sign up for a new account
3. You should see a trial conversion modal
4. Click "Start Free Trial" to begin 14-day trial

### 2. Feature Gating
1. Navigate to "Contacts" page
2. Notice the usage limit display (10 contacts for free users)
3. Go to "Messaging" tab
4. Premium features show upgrade prompts for free users

### 3. Subscription Management
1. Click "Subscription" in sidebar
2. View different plan options
3. Toggle between monthly/yearly billing
4. Test upgrade flow (uses Stripe test mode)

### 4. Admin Dashboard
1. Upgrade to Premium plan (or modify feature access)
2. Navigate to "Admin" in sidebar
3. View subscription analytics and user management

## 🔗 Stripe Webhook Setup

### Development Testing
```bash
# Install Stripe CLI
brew install stripe/stripe-cli/stripe  # macOS
# or download from https://stripe.com/docs/stripe-cli

# Login to Stripe
stripe login

# Forward webhooks to local backend
stripe listen --forward-to localhost:3001/api/webhooks/stripe
```

### Production Webhooks
1. In Stripe Dashboard > Developers > Webhooks
2. Add endpoint: `https://your-domain.com/api/webhooks/stripe`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

## 🎨 SaaS Features Overview

### ✅ Implemented Features

#### Subscription Management
- [x] Free, Trial, Standard, Elite, Premium, Enterprise plans
- [x] 14-day trial (no credit card required)
- [x] Monthly/yearly billing with discounts
- [x] Stripe Checkout integration
- [x] Customer portal for billing management

#### Feature Gating
- [x] Plan-based feature access control
- [x] Usage limits (contacts, messaging, etc.)
- [x] Upgrade prompts and CTAs
- [x] Trial conversion flows

#### Admin Dashboard
- [x] Subscription analytics
- [x] User management interface
- [x] Revenue tracking
- [x] Real-time metrics

#### Payment Processing
- [x] Secure Stripe integration
- [x] Webhook event handling
- [x] Subscription lifecycle management
- [x] Payment failure handling

### 🔄 User Journey

1. **Landing** → User visits site, sees pricing
2. **Signup** → Creates account (Free plan)
3. **Trial Prompt** → Offered 14-day trial
4. **Feature Discovery** → Explores premium features
5. **Conversion** → Upgrades to paid plan
6. **Retention** → Ongoing value and feature updates

## 🚀 Production Deployment

### Environment Variables
```bash
# Production environment
NODE_ENV=production
STRIPE_SECRET_KEY=sk_live_your_live_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
FRONTEND_URL=https://your-domain.com
```

### Deployment Checklist
- [ ] Switch to Stripe live keys
- [ ] Configure production webhook endpoints
- [ ] Set up SSL/TLS certificates
- [ ] Configure domain and DNS
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy
- [ ] Test payment flows end-to-end

## 📊 Key Metrics to Track

### Business Metrics
- Monthly Recurring Revenue (MRR)
- Customer Acquisition Cost (CAC)
- Customer Lifetime Value (CLV)
- Churn Rate
- Trial-to-Paid Conversion Rate

### Technical Metrics
- API Response Times
- Webhook Success Rate
- Payment Success Rate
- Feature Adoption Rates

## 🛠 Customization

### Adding New Plans
1. Create plan in Stripe Dashboard
2. Add price IDs to environment variables
3. Update `PLAN_TO_PRICE_ID` in `stripeService.ts`
4. Add plan details to `SubscriptionManagement.tsx`

### Adding New Features
1. Define feature in `useFeatureAccess.ts`
2. Set required plan level
3. Wrap components with `<FeatureGate>`
4. Add usage tracking if needed

### Modifying Trial Period
Update trial logic in:
- `backend/routes/stripe.js` (trial duration)
- `TrialConversionFlow.tsx` (UI messaging)
- `useFeatureAccess.ts` (trial feature access)

## 🆘 Troubleshooting

### Common Issues

**Stripe Keys Not Working**
- Verify keys are correct in environment files
- Ensure using test keys for development
- Check Stripe Dashboard for key status

**Webhooks Not Receiving**
- Verify webhook URL is accessible
- Check webhook secret matches environment
- Use Stripe CLI for local testing

**Feature Gating Not Working**
- Check user plan in browser dev tools
- Verify feature definitions in `useFeatureAccess.ts`
- Ensure components are wrapped with `<FeatureGate>`

### Support Resources
- [Stripe Documentation](https://stripe.com/docs)
- [React Query Docs](https://tanstack.com/query)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)

## 🎉 Success!

You now have a fully functional SaaS application with:
- ✅ Subscription management
- ✅ Payment processing  
- ✅ Feature gating
- ✅ Admin dashboard
- ✅ Trial conversions
- ✅ Usage analytics

Ready to scale your birthday management SaaS! 🚀
