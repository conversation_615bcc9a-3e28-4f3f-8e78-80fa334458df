{"name": "trackcelebrations", "private": true, "version": "0.0.0", "type": "module", "webRoot": "${workspaceFolder}/src", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:open": "jest --coverage && node scripts/test-coverage.js --open", "test:analyze": "node scripts/test-coverage.js", "test:ci": "jest --coverage --watchAll=false --passWithNoTests"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "firebase": "^11.10.0", "jest-dom": "^4.0.0", "jest-environment-jsdom": "^30.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.1", "sql.js": "^1.13.0", "stripe": "^18.3.0", "tailwindcss": "^4.1.11", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}