# 🎉 WeWish - Birthday & Anniversary SaaS Platform

[![Tests](https://github.com/your-username/wewish/workflows/🧪%20Unit%20Tests%20&%20Coverage/badge.svg)](https://github.com/your-username/wewish/actions)
[![Coverage](https://codecov.io/gh/your-username/wewish/branch/main/graph/badge.svg)](https://codecov.io/gh/your-username/wewish)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

A comprehensive SaaS platform for managing birthdays, anniversaries, and special occasions with automated messaging, gift tracking, and analytics.

## ✨ Features

### 🎂 **Birthday Management**
- Contact management with birthday tracking
- Automated birthday reminders and messages
- Landmark birthday celebrations (18th, 21st, 30th, etc.)
- Birthday calendar with upcoming events

### 💕 **Anniversary Tracking**
- Wedding, dating, and business anniversary support
- Automated anniversary notifications
- Anniversary milestone celebrations
- Custom anniversary types and categories

### 🎁 **Gift Management**
- Gift history tracking with ratings and notes
- Gift reminder system for upcoming occasions
- Gift suggestions based on budget and preferences
- Purchase tracking and expense management

### 📧 **Automated Messaging**
- Customizable message templates for birthdays and anniversaries
- Automated message scheduling and delivery
- Bulk messaging capabilities for multiple contacts
- Message history and delivery tracking

### 📊 **Analytics & Insights**
- Birthday and anniversary analytics dashboard
- Spending analysis and budget tracking
- Contact engagement metrics
- Celebration success tracking

### 💳 **Subscription Management**
- Multiple subscription tiers (Free, Standard, Elite, Premium)
- 14-day free trial without credit card
- Stripe payment integration
- Usage-based feature access control

## 🚀 Quick Start

### Prerequisites
- Node.js 18.x or higher
- npm or yarn package manager

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/wewish.git
cd wewish

# Install dependencies
npm install

# Install backend dependencies
cd backend && npm install && cd ..

# Start development server
npm run dev

# Start backend server (in another terminal)
cd backend && npm start
```

### Environment Setup

Create `.env` file in the root directory:

```bash
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_URL=http://localhost:5173
```

Create `backend/.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App Configuration
FRONTEND_URL=http://localhost:5173
PORT=3001
NODE_ENV=development
```

## 🧪 Testing

The application includes comprehensive unit tests with Jest and React Testing Library, plus automated CI/CD workflows.

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run tests for CI (no watch mode)
npm run test:ci

# Analyze test coverage
npm run test:analyze
```

### 🚀 GitHub Actions Workflows

Automated testing and quality assurance with multiple workflows:

- **🧪 Main Test Workflow** - Comprehensive testing with coverage reporting
- **🚀 CI/CD Pipeline** - Streamlined CI/CD for production deployment
- **🔍 Pull Request Checks** - PR validation with coverage and security
- **🌐 Cross-Platform Testing** - Multi-OS and Node.js version testing

See [GITHUB_ACTIONS_GUIDE.md](GITHUB_ACTIONS_GUIDE.md) for detailed workflow documentation.

## 🏗️ Tech Stack

### Frontend
- **React 19** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Query** - Server state management
- **React Router** - Client-side routing

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **SQLite** - Lightweight database with WAL mode
- **Stripe** - Payment processing
- **Firebase Auth** - Authentication service

### Testing & Quality
- **Jest** - Testing framework
- **React Testing Library** - Component testing
- **ESLint** - Code linting
- **TypeScript** - Static type checking
- **GitHub Actions** - CI/CD automation

## 📁 Project Structure

```
wewish/
├── src/
│   ├── components/          # React components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # Business logic and API services
│   ├── context/            # React context providers
│   ├── utils/              # Utility functions
│   └── types/              # TypeScript type definitions
├── backend/
│   ├── routes/             # API routes
│   ├── middleware/         # Express middleware
│   ├── services/           # Backend services
│   └── utils/              # Backend utilities
├── .github/
│   └── workflows/          # GitHub Actions workflows
├── coverage/               # Test coverage reports
└── docs/                   # Documentation
```

## 🚀 Deployment

### Production Deployment

The application is ready for deployment with:

- **DigitalOcean** - Recommended hosting platform
- **Stripe Live Mode** - Production payment processing
- **Firebase Production** - Production authentication
- **SSL/HTTPS** - Secure connections
- **Automated Backups** - Database backup system

See [DIGITALOCEAN_DEPLOYMENT_PLAN.md](DIGITALOCEAN_DEPLOYMENT_PLAN.md) for detailed deployment instructions.

### Environment Variables

Production environment variables:

```bash
# Frontend (.env.production)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_URL=https://yourdomain.com

# Backend (.env.production)
STRIPE_SECRET_KEY=sk_live_your_live_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_secret
NODE_ENV=production
DATABASE_PATH=/var/lib/wewish/database.db
```

## 📊 Test Coverage

Current test coverage status:

- **Services**: 85%+ coverage (5/12 files tested)
- **Hooks**: 70%+ coverage (2/9 files tested)
- **Components**: 60%+ coverage (9/30+ files tested)
- **Overall**: 72%+ coverage

Target: 90%+ coverage for production deployment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Write tests for new features
- Maintain 90%+ test coverage
- Follow TypeScript best practices
- Use conventional commit messages
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Stripe for payment processing
- Firebase for authentication services
- All contributors and testers

## 📞 Support

For support and questions:

- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [Create an issue](https://github.com/your-username/wewish/issues)
- 📖 Documentation: [View docs](https://docs.wewish.app)

---

**Made with ❤️ for celebrating life's special moments** 🎉
