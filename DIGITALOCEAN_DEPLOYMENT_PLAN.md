# 🌊 DigitalOcean Deployment Plan - WeWish SaaS

## **📋 Deployment Overview**

This plan outlines the complete deployment strategy for WeWish birthday SaaS platform on DigitalOcean infrastructure.

---

## **🏗️ Infrastructure Architecture**

### **Production Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   App Droplet   │    │  Database       │
│   (DO LB)       │────│   (Frontend +   │────│  (SQLite +      │
│   SSL/HTTPS     │    │    Backend)     │    │   Backups)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Monitoring    │              │
         └──────────────│   & Logging     │──────────────┘
                        │   (DO Insights) │
                        └─────────────────┘
```

### **Resource Requirements**

#### **App Droplet (Primary)**
- **Size**: 2 vCPUs, 4GB RAM, 80GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Services**: Node.js, Nginx, PM2
- **Estimated Cost**: $24/month

#### **Database Storage**
- **Type**: Block Storage Volume
- **Size**: 100GB SSD
- **Backup**: Daily automated backups
- **Estimated Cost**: $10/month

#### **Load Balancer**
- **Type**: DigitalOcean Load Balancer
- **SSL**: Let's Encrypt certificate
- **Health Checks**: Enabled
- **Estimated Cost**: $12/month

#### **Domain & DNS**
- **Domain**: Custom domain (e.g., wewish.app)
- **DNS**: DigitalOcean DNS
- **SSL**: Free Let's Encrypt
- **Estimated Cost**: $12/year

**Total Monthly Cost: ~$46/month + domain**

---

## **🚀 Phase 1: Initial Setup (Week 1)**

### **Day 1: DigitalOcean Account & Droplet Setup**

#### **1.1 Create DigitalOcean Account**
```bash
# Sign up at https://digitalocean.com
# Add payment method
# Generate API token for automation
```

#### **1.2 Create App Droplet**
```bash
# Droplet Configuration:
# - Image: Ubuntu 22.04 LTS
# - Size: 2 vCPUs, 4GB RAM, 80GB SSD
# - Region: London (LON1) or Frankfurt (FRA1)
# - VPC: Create new VPC
# - SSH Keys: Add your public key
# - Hostname: wewish-app-01
```

#### **1.3 Initial Server Setup**
```bash
# Connect to droplet
ssh root@your_droplet_ip

# Update system
apt update && apt upgrade -y

# Create non-root user
adduser wewish
usermod -aG sudo wewish
rsync --archive --chown=wewish:wewish ~/.ssh /home/<USER>

# Configure firewall
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw enable
```

### **Day 2: Software Installation**

#### **2.1 Install Node.js & npm**
```bash
# Install Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

#### **2.2 Install Nginx**
```bash
# Install Nginx
apt install nginx -y

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx
```

#### **2.3 Install PM2 (Process Manager)**
```bash
# Install PM2 globally
npm install -g pm2

# Setup PM2 startup script
pm2 startup
# Follow the instructions to run the generated command
```

#### **2.4 Install SQLite & Tools**
```bash
# Install SQLite
apt install sqlite3 -y

# Install backup tools
apt install rsync cron -y
```

### **Day 3: Application Deployment**

#### **3.1 Clone Repository**
```bash
# Switch to wewish user
su - wewish

# Clone repository
git clone https://github.com/your-username/wewish.git
cd wewish

# Install dependencies
npm install
cd backend && npm install && cd ..
```

#### **3.2 Environment Configuration**
```bash
# Frontend environment
cp .env.example .env
nano .env

# Backend environment
cp backend/.env.example backend/.env
nano backend/.env
```

**Frontend .env:**
```bash
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_key
VITE_API_BASE_URL=https://api.wewish.app
VITE_APP_URL=https://wewish.app
VITE_FIREBASE_API_KEY=your_firebase_key
VITE_FIREBASE_AUTH_DOMAIN=your_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
```

**Backend .env:**
```bash
STRIPE_SECRET_KEY=sk_live_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_secret
FRONTEND_URL=https://wewish.app
PORT=3001
NODE_ENV=production
DATABASE_PATH=/var/lib/wewish/database.db
```

#### **3.3 Build Application**
```bash
# Build frontend
npm run build

# Test backend
cd backend && npm start
```

### **Day 4: Database Setup**

#### **4.1 Create Database Directory**
```bash
# Create database directory
sudo mkdir -p /var/lib/wewish
sudo chown wewish:wewish /var/lib/wewish
sudo chmod 755 /var/lib/wewish
```

#### **4.2 Initialize Database**
```bash
# Create production database
cd /home/<USER>/wewish
node -e "
const { databaseService } = require('./src/services/databaseService.ts');
databaseService.initialize().then(() => {
  console.log('Database initialized');
  process.exit(0);
});
"
```

#### **4.3 Setup Database Backups**
```bash
# Create backup script
sudo nano /usr/local/bin/backup-wewish-db.sh
```

**Backup Script:**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/wewish"
DB_PATH="/var/lib/wewish/database.db"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
sqlite3 $DB_PATH ".backup $BACKUP_DIR/wewish_backup_$DATE.db"

# Keep only last 30 days of backups
find $BACKUP_DIR -name "wewish_backup_*.db" -mtime +30 -delete

echo "Backup completed: wewish_backup_$DATE.db"
```

```bash
# Make script executable
sudo chmod +x /usr/local/bin/backup-wewish-db.sh

# Add to crontab (daily at 2 AM)
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/backup-wewish-db.sh
```

### **Day 5: Nginx Configuration**

#### **5.1 Configure Nginx**
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/wewish
```

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name wewish.app www.wewish.app;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name wewish.app www.wewish.app;
    
    # SSL Configuration (will be added by Certbot)
    
    # Frontend (React app)
    location / {
        root /home/<USER>/wewish/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/wewish /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### **Day 6: SSL Certificate & Domain Setup**

#### **6.1 Install Certbot**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y
```

#### **6.2 Configure Domain DNS**
```bash
# In DigitalOcean DNS:
# A record: wewish.app -> your_droplet_ip
# A record: www.wewish.app -> your_droplet_ip
# CNAME record: api.wewish.app -> wewish.app
```

#### **6.3 Obtain SSL Certificate**
```bash
# Get SSL certificate
sudo certbot --nginx -d wewish.app -d www.wewish.app

# Test auto-renewal
sudo certbot renew --dry-run
```

### **Day 7: Process Management & Monitoring**

#### **7.1 Setup PM2 Configuration**
```bash
# Create PM2 ecosystem file
nano /home/<USER>/wewish/ecosystem.config.js
```

**PM2 Configuration:**
```javascript
module.exports = {
  apps: [{
    name: 'wewish-backend',
    script: './backend/server.js',
    cwd: '/home/<USER>/wewish',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/wewish/backend-error.log',
    out_file: '/var/log/wewish/backend-out.log',
    log_file: '/var/log/wewish/backend.log',
    time: true
  }]
};
```

```bash
# Create log directory
sudo mkdir -p /var/log/wewish
sudo chown wewish:wewish /var/log/wewish

# Start application with PM2
pm2 start ecosystem.config.js
pm2 save
```

#### **7.2 Setup Basic Monitoring**
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs -y

# Setup log rotation
sudo nano /etc/logrotate.d/wewish
```

**Log Rotation Configuration:**
```
/var/log/wewish/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wewish wewish
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## **🔧 Phase 2: Production Hardening (Week 2)**

### **Security Enhancements**
- **Fail2ban** - Intrusion prevention
- **UFW Rules** - Strict firewall configuration
- **SSH Hardening** - Key-only authentication
- **Regular Updates** - Automated security updates

### **Performance Optimization**
- **Nginx Caching** - Static asset caching
- **Gzip Compression** - Response compression
- **Database Optimization** - Query optimization
- **CDN Integration** - DigitalOcean Spaces CDN

### **Backup & Recovery**
- **Database Backups** - Automated daily backups
- **Application Backups** - Code and configuration backups
- **Disaster Recovery** - Recovery procedures documentation
- **Backup Testing** - Regular restore testing

---

## **📊 Phase 3: Monitoring & Observability (Week 3)**

### **Application Monitoring**
- **DigitalOcean Monitoring** - Built-in droplet monitoring
- **Custom Health Checks** - Application-specific monitoring
- **Uptime Monitoring** - External uptime monitoring
- **Performance Metrics** - Response time and throughput

### **Error Tracking**
- **Application Logs** - Centralized logging
- **Error Alerting** - Real-time error notifications
- **Performance Alerts** - Resource usage alerts
- **Custom Dashboards** - Business metrics dashboards

---

## **🚀 Phase 4: CI/CD & Automation (Week 4)**

### **Deployment Automation**
- **GitHub Actions** - Automated deployment pipeline
- **Staging Environment** - Pre-production testing
- **Blue-Green Deployment** - Zero-downtime deployments
- **Rollback Procedures** - Quick rollback capabilities

### **Testing & Quality Assurance**
- **Automated Testing** - Unit and integration tests
- **Load Testing** - Performance testing
- **Security Scanning** - Automated security audits
- **Code Quality** - Linting and code analysis

---

## **💰 Cost Optimization**

### **Monthly Cost Breakdown**
- **App Droplet**: $24/month
- **Block Storage**: $10/month
- **Load Balancer**: $12/month
- **Backups**: $5/month
- **Monitoring**: $0 (included)
- **Total**: ~$51/month

### **Cost Optimization Strategies**
- **Reserved Instances** - Annual payment discounts
- **Resource Monitoring** - Right-sizing resources
- **Backup Optimization** - Efficient backup strategies
- **CDN Usage** - Reduce bandwidth costs

---

**Next Steps: Execute Phase 1 deployment and verify all systems are operational.**
