version: '3.8'

services:
  frontend:
    build: . # Path to your React app Dockerfile folder
    ports:
      - '3000:3000'
    environment:
      - REACT_APP_BACKUP_API_TOKEN=${BACKUP_API_TOKEN}
    depends_on:
      - backup-api

  backup-api:
    build: ./backup-api # Path to your API Dockerfile folder (needs to be created)
    ports:
      - '4000:4000'
    environment:
      - PORT=4000
      - BACKUP_DIR=/backups
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - REPO_OWNER=${REPO_OWNER}
      - REPO_NAME=${REPO_NAME}
      - WORKFLOW_ID=${WORKFLOW_ID}
      - AUTH_TOKEN=${BACKUP_API_TOKEN}
    volumes:
      - backups:/backups

volumes:
  backups:
