# 🗄️ Database Options for Birthday SaaS App

## **Overview**

This document outlines database options for migrating from localStorage to a robust persistence layer, with SQLite (WAL mode) as the primary recommendation.

## **📋 SQLite Implementation Plan**

### **Phase 1: Database Setup & Schema**

#### **1.1 Install Dependencies**

```bash
npm install better-sqlite3 @types/better-sqlite3
npm install electron-store # For Electron apps
# OR for web apps:
npm install sql.js
```

#### **1.2 Database Schema Design**

```sql
-- Users table
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Contacts table
CREATE TABLE contacts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  birthday DATE,
  anniversary_date DATE,
  anniversary_type TEXT,
  partner_name TEXT,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Reminders table
CREATE TABLE reminders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  contact_id INTEGER,
  title TEXT NOT NULL,
  person TEXT NOT NULL,
  date DATE NOT NULL,
  reminder_date DATE NOT NULL,
  type TEXT CHECK (type IN ('birthday', 'anniversary', 'custom')) NOT NULL,
  is_active BOOLEAN DEFAULT 1,
  notifications TEXT, -- JSON array
  notes TEXT,
  recurring BOOLEAN DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE SET NULL
);

-- Gift history table
CREATE TABLE gift_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  contact_id INTEGER,
  gift_name TEXT NOT NULL,
  recipient TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  date DATE NOT NULL,
  category TEXT,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE SET NULL
);

-- Settings table
CREATE TABLE user_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER UNIQUE NOT NULL,
  email_notifications BOOLEAN DEFAULT 1,
  push_notifications BOOLEAN DEFAULT 1,
  sms_notifications BOOLEAN DEFAULT 0,
  reminder_timing TEXT, -- JSON object
  quiet_hours TEXT, -- JSON object
  frequency TEXT, -- JSON object
  auto_mark_complete BOOLEAN DEFAULT 0,
  weekly_digest BOOLEAN DEFAULT 1,
  landmark_birthdays BOOLEAN DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Message templates table
CREATE TABLE message_templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT CHECK (type IN ('birthday', 'anniversary', 'landmark', 'reminder', 'weekly_admin')) NOT NULL,
  is_active BOOLEAN DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_contacts_user_id ON contacts (user_id);
CREATE INDEX idx_contacts_birthday ON contacts (birthday);
CREATE INDEX idx_contacts_anniversary ON contacts (anniversary_date);
CREATE INDEX idx_reminders_user_id ON reminders (user_id);
CREATE INDEX idx_reminders_date ON reminders (reminder_date);
CREATE INDEX idx_gift_history_user_id ON gift_history (user_id);
CREATE INDEX idx_templates_user_id ON message_templates (user_id);
CREATE INDEX idx_templates_type ON message_templates (type);
```

### **Phase 2: Database Service Layer**

#### **2.1 Database Connection & WAL Mode**

```typescript
// src/services/database.ts
import Database from 'better-sqlite3';
import path from 'path';

class DatabaseService {
  private db: Database.Database;

  constructor() {
    const dbPath = path.join(process.cwd(), 'data', 'birthday-saas.db');
    this.db = new Database(dbPath);
    
    // Enable WAL mode for better concurrency
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('synchronous = NORMAL');
    this.db.pragma('cache_size = 1000000');
    this.db.pragma('temp_store = memory');
    
    this.initializeSchema();
  }

  private initializeSchema() {
    // Run schema creation scripts
    this.db.exec(/* SQL schema from above */);
  }

  getDatabase() {
    return this.db;
  }

  close() {
    this.db.close();
  }
}

export const dbService = new DatabaseService();
```

#### **2.2 Repository Pattern Implementation**
```typescript
// src/repositories/ContactRepository.ts
export class ContactRepository {
  private db: Database.Database;

  constructor(database: Database.Database) {
    this.db = database;
  }

  async findByUserId(userId: number): Promise<Contact[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM contacts 
      WHERE user_id = ? 
      ORDER BY name ASC
    `);
    return stmt.all(userId);
  }

  async create(contact: CreateContactInput): Promise<Contact> {
    const stmt = this.db.prepare(`
      INSERT INTO contacts (user_id, name, email, phone, birthday, anniversary_date, anniversary_type, partner_name, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      contact.userId,
      contact.name,
      contact.email,
      contact.phone,
      contact.birthday,
      contact.anniversaryDate,
      contact.anniversaryType,
      contact.partnerName,
      contact.notes
    );

    return this.findById(result.lastInsertRowid as number);
  }

  // ... other CRUD methods
}
```

### **Phase 3: Service Layer Updates**

#### **3.1 Update Existing Services**

```typescript
// src/services/contactService.ts
import { ContactRepository } from '../repositories/ContactRepository';
import { dbService } from './database';

const contactRepo = new ContactRepository(dbService.getDatabase());

export const contactService = {
  getContacts: async (userEmail: string): Promise<Contact[]> => {
    const user = await userService.findByEmail(userEmail);
    return contactRepo.findByUserId(user.id);
  },

  createContact: async (userEmail: string, contact: CreateContactInput): Promise<Contact> => {
    const user = await userService.findByEmail(userEmail);
    return contactRepo.create({ ...contact, userId: user.id });
  },

  // ... other methods
};
```

### **Phase 4: Migration Strategy**

#### **4.1 Data Migration Script**
```typescript
// scripts/migrate-from-localstorage.ts
export async function migrateFromLocalStorage() {
  // 1. Read existing localStorage data
  // 2. Transform to new schema
  // 3. Insert into SQLite
  // 4. Verify migration
  // 5. Clear localStorage (optional)
}
```

#### **4.2 Gradual Migration**
```typescript
// Hybrid approach during transition
export const hybridContactService = {
  getContacts: async (userEmail: string) => {
    try {
      // Try SQLite first
      return await sqliteContactService.getContacts(userEmail);
    } catch (error) {
      // Fallback to localStorage
      console.warn('SQLite failed, using localStorage:', error);
      return localStorageContactService.getContacts(userEmail);
    }
  }
};
```

## **🎯 Pros and Cons Analysis**

### **✅ Pros of SQLite + WAL Mode**

#### **Performance Benefits:**
- **WAL Mode:** Multiple readers can access database simultaneously
- **Better Concurrency:** Writers don't block readers
- **Faster Writes:** WAL mode provides better write performance
- **Atomic Transactions:** ACID compliance ensures data integrity

#### **Data Management:**
- **Relational Structure:** Proper foreign keys and constraints
- **Query Flexibility:** Complex queries with JOINs, aggregations
- **Data Validation:** Schema-level validation and constraints
- **Backup & Recovery:** Simple file-based backups

#### **Scalability:**
- **Large Datasets:** Handles millions of records efficiently
- **Indexing:** Fast lookups with proper indexes
- **Memory Management:** Efficient memory usage with pagination
- **File Size:** Compact storage with compression

#### **Development Benefits:**
- **SQL Familiarity:** Standard SQL syntax
- **Type Safety:** Better TypeScript integration
- **Migration Support:** Schema versioning and migrations
- **Testing:** Easier to test with real database

### **❌ Cons of SQLite + WAL Mode**

#### **Deployment Complexity:**
- **File System Access:** Requires local file system (not suitable for serverless)
- **Platform Dependencies:** Native binaries for different platforms
- **Deployment Size:** Larger bundle size with native dependencies

#### **Concurrency Limitations:**
- **Single Writer:** Only one writer at a time (though WAL helps)
- **Network Access:** No network access (local only)
- **Distributed Systems:** Not suitable for multi-server deployments

#### **Web Limitations:**
- **Browser Compatibility:** Limited browser support (needs WebAssembly)
- **File Access:** Browser security restrictions
- **Storage Limits:** Browser storage quotas

#### **Operational Concerns:**
- **Backup Complexity:** Need to handle WAL files in backups
- **Corruption Risk:** File corruption possible (though rare)
- **Platform Lock-in:** Tied to SQLite ecosystem

## **🚀 Recommended Implementation Strategy**

### **Phase 1: Foundation (Week 1)**
1. Set up SQLite with WAL mode
2. Create database schema
3. Implement repository pattern
4. Create migration scripts

### **Phase 2: Core Services (Week 2)**
1. Migrate contact service
2. Migrate reminder service
3. Update React Query hooks
4. Add error handling

### **Phase 3: Advanced Features (Week 3)**
1. Migrate settings service
2. Migrate gift service
3. Add data validation
4. Performance optimization

### **Phase 4: Production Ready (Week 4)**
1. Comprehensive testing
2. Backup/restore functionality
3. Migration from localStorage
4. Documentation

## **🎯 Alternative Considerations**

### **For Web Apps:**
- **IndexedDB** with Dexie.js for browser storage
- **PouchDB** for offline-first with sync capabilities
- **Firebase Firestore** for cloud-native solution

### **For Desktop Apps:**
- **SQLite** is perfect choice
- **LevelDB** for key-value storage
- **PostgreSQL** for advanced features

### **For Cloud Deployment:**
- **PostgreSQL** with connection pooling
- **MongoDB** for document-based storage
- **Supabase** for managed PostgreSQL

**SQLite with WAL mode is excellent for desktop applications and local development, but consider cloud alternatives for web deployment and multi-user scenarios.**
