# 💳 Stripe Integration Runbook - WeWish SaaS

## **📋 Overview**

This runbook provides step-by-step instructions for setting up, testing, and deploying Stripe payment integration for the WeWish birthday SaaS platform.

---

## **🔧 Current Integration Status**

### **✅ Implemented Components**
- ✅ **Stripe SDK Integration** - Frontend and backend
- ✅ **Customer Management** - Create and retrieve customers
- ✅ **Subscription Plans** - Standard, Elite, Premium tiers
- ✅ **Trial System** - 14-day free trial implementation
- ✅ **Checkout Sessions** - Secure payment processing
- ✅ **Customer Portal** - Billing management interface
- ✅ **Webhook Handling** - Payment event processing
- ✅ **Plan Upgrades** - Subscription tier changes

### **⚠️ Needs Testing/Verification**
- ⚠️ **Webhook Endpoints** - Event handling verification
- ⚠️ **Payment Flows** - End-to-end testing
- ⚠️ **Error Handling** - Failed payment scenarios
- ⚠️ **Subscription Lifecycle** - Cancel, pause, resume
- ⚠️ **Tax Calculation** - Automatic tax handling

---

## **🚀 Setup Instructions**

### **Step 1: Stripe Account Setup**

#### **1.1 Create Stripe Account**
```bash
# Visit https://dashboard.stripe.com/register
# Complete business verification
# Enable test mode for development
```

#### **1.2 Get API Keys**
```bash
# Navigate to: Developers > API keys
# Copy Publishable key (pk_test_...)
# Copy Secret key (sk_test_...)
# Copy Webhook secret (whsec_...)
```

#### **1.3 Configure Products & Prices**
```bash
# Navigate to: Products > Add product
# Create products for each plan:
```

**Standard Plan:**
- Name: "WeWish Standard"
- Price: £9.99/month, £99.99/year
- Features: 100 contacts, basic messaging

**Elite Plan:**
- Name: "WeWish Elite" 
- Price: £19.99/month, £199.99/year
- Features: 500 contacts, advanced messaging, analytics

**Premium Plan:**
- Name: "WeWish Premium"
- Price: £39.99/month, £399.99/year
- Features: Unlimited contacts, all features

### **Step 2: Environment Configuration**

#### **2.1 Frontend Environment (.env)**
```bash
# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
VITE_API_BASE_URL=http://localhost:3001/api

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=WeWish
```

#### **2.2 Backend Environment (backend/.env)**
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Price IDs (from Stripe Dashboard)
STRIPE_STANDARD_MONTHLY_PRICE_ID=price_standard_monthly
STRIPE_STANDARD_YEARLY_PRICE_ID=price_standard_yearly
STRIPE_ELITE_MONTHLY_PRICE_ID=price_elite_monthly
STRIPE_ELITE_YEARLY_PRICE_ID=price_elite_yearly
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_premium_monthly
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_premium_yearly

# App Configuration
FRONTEND_URL=http://localhost:5173
PORT=3001
NODE_ENV=development
```

### **Step 3: Webhook Configuration**

#### **3.1 Create Webhook Endpoint**
```bash
# In Stripe Dashboard: Developers > Webhooks > Add endpoint
# Endpoint URL: https://yourdomain.com/api/stripe/webhook
# Events to send:
```

**Required Events:**
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.created`
- `customer.updated`

#### **3.2 Test Webhook Locally**
```bash
# Install Stripe CLI
curl -s https://packages.stripe.com/api/security/keypair/stripe-cli-gpg/public | gpg --dearmor | sudo tee /usr/share/keyrings/stripe.gpg
echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.com/stripe-cli-debian-local stable main" | sudo tee -a /etc/apt/sources.list.d/stripe.list
sudo apt update
sudo apt install stripe

# Login to Stripe
stripe login

# Forward webhooks to local server
stripe listen --forward-to localhost:3001/api/stripe/webhook
```

---

## **🧪 Testing Procedures**

### **Test 1: Customer Creation**
```bash
# Test creating a new customer
curl -X POST http://localhost:3001/api/stripe/create-customer \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User"
  }'

# Expected Response:
# { "id": "cus_..." }
```

### **Test 2: Trial Start**
```bash
# Test starting a trial
curl -X POST http://localhost:3001/api/stripe/start-trial \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User"
  }'

# Expected Response:
# { "customerId": "cus_..." }
```

### **Test 3: Checkout Session**
```bash
# Test creating checkout session
curl -X POST http://localhost:3001/api/stripe/create-checkout-session \
  -H "Content-Type: application/json" \
  -d '{
    "priceId": "price_standard_monthly",
    "customerId": "cus_...",
    "successUrl": "http://localhost:5173/dashboard?success=true",
    "cancelUrl": "http://localhost:5173/subscription?cancelled=true"
  }'

# Expected Response:
# { "url": "https://checkout.stripe.com/..." }
```

### **Test 4: Subscription Status**
```bash
# Test getting subscription status
curl -X GET http://localhost:3001/api/stripe/subscription/cus_...

# Expected Response:
# {
#   "status": "active",
#   "plan": { "nickname": "Standard" },
#   "current_period_end": **********
# }
```

### **Test 5: Customer Portal**
```bash
# Test customer portal access
curl -X POST http://localhost:3001/api/stripe/create-portal-session \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": "cus_...",
    "returnUrl": "http://localhost:5173/dashboard"
  }'

# Expected Response:
# { "url": "https://billing.stripe.com/..." }
```

---

## **🔍 End-to-End Testing Scenarios**

### **Scenario 1: New User Trial Flow**
1. **User Registration** - Create account in WeWish
2. **Trial Start** - Click "Start 14-day trial"
3. **Feature Access** - Verify premium features unlocked
4. **Trial Expiry** - Test trial expiration after 14 days
5. **Upgrade Prompt** - Verify upgrade prompts appear

### **Scenario 2: Subscription Purchase Flow**
1. **Plan Selection** - Choose Standard/Elite/Premium plan
2. **Checkout Process** - Complete Stripe checkout
3. **Payment Success** - Verify successful payment
4. **Feature Unlock** - Confirm plan features activated
5. **Billing Portal** - Test billing management access

### **Scenario 3: Subscription Management**
1. **Plan Upgrade** - Upgrade from Standard to Elite
2. **Billing Cycle Change** - Switch monthly to yearly
3. **Payment Method Update** - Change credit card
4. **Subscription Cancel** - Cancel subscription
5. **Reactivation** - Reactivate cancelled subscription

### **Scenario 4: Failed Payment Handling**
1. **Payment Failure** - Simulate failed payment
2. **Retry Logic** - Verify automatic retry attempts
3. **Dunning Management** - Test dunning email sequence
4. **Service Suspension** - Verify feature restrictions
5. **Recovery Flow** - Test payment recovery process

---

## **🚨 Error Handling & Troubleshooting**

### **Common Issues & Solutions**

#### **Issue 1: Webhook Signature Verification Failed**
```bash
# Symptoms: 400 error on webhook endpoint
# Solution: Verify webhook secret in environment variables
# Check: STRIPE_WEBHOOK_SECRET matches Stripe dashboard
```

#### **Issue 2: Customer Not Found**
```bash
# Symptoms: Customer lookup fails
# Solution: Ensure customer is created before subscription
# Check: Customer email matches exactly
```

#### **Issue 3: Price ID Not Found**
```bash
# Symptoms: Checkout session creation fails
# Solution: Verify price IDs in Stripe dashboard
# Check: Price IDs in environment variables are correct
```

#### **Issue 4: Subscription Status Sync Issues**
```bash
# Symptoms: UI shows wrong subscription status
# Solution: Implement webhook event handling
# Check: Webhook events are being processed correctly
```

---

## **📊 Monitoring & Analytics**

### **Key Metrics to Track**
- **Conversion Rate** - Trial to paid conversion
- **Churn Rate** - Monthly subscription cancellations
- **Revenue Growth** - Monthly recurring revenue (MRR)
- **Payment Success Rate** - Successful payment percentage
- **Customer Lifetime Value** - Average customer value

### **Stripe Dashboard Monitoring**
- **Payments** - Monitor successful/failed payments
- **Subscriptions** - Track active subscriptions
- **Customers** - Monitor customer growth
- **Revenue** - Track revenue trends
- **Disputes** - Handle chargebacks and disputes

---

## **🔒 Security Best Practices**

### **API Key Management**
- ✅ Use environment variables for all keys
- ✅ Never commit keys to version control
- ✅ Rotate keys regularly
- ✅ Use different keys for test/production
- ✅ Implement key access logging

### **Webhook Security**
- ✅ Verify webhook signatures
- ✅ Use HTTPS for webhook endpoints
- ✅ Implement idempotency handling
- ✅ Log all webhook events
- ✅ Handle webhook retries properly

### **Payment Data Handling**
- ✅ Never store payment card data
- ✅ Use Stripe's secure tokenization
- ✅ Implement PCI compliance measures
- ✅ Encrypt sensitive customer data
- ✅ Regular security audits

---

**Next Steps: Execute testing procedures and verify all payment flows work correctly.**
