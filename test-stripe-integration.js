#!/usr/bin/env node

/**
 * Stripe Integration Test Suite
 * Tests all Stripe functionality for WeWish SaaS
 */

const axios = require('axios');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001/api';
const TEST_EMAIL = '<EMAIL>';
const TEST_NAME = 'Test User';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Test functions
async function testCreateCustomer() {
  try {
    info('Testing customer creation...');
    
    const response = await axios.post(`${API_BASE_URL}/stripe/create-customer`, {
      email: TEST_EMAIL,
      name: TEST_NAME
    });

    if (response.data && response.data.id) {
      success(`Customer created: ${response.data.id}`);
      return response.data.id;
    } else {
      error('Customer creation failed - no ID returned');
      return null;
    }
  } catch (err) {
    error(`Customer creation failed: ${err.message}`);
    return null;
  }
}

async function testStartTrial() {
  try {
    info('Testing trial start...');
    
    const response = await axios.post(`${API_BASE_URL}/stripe/start-trial`, {
      email: TEST_EMAIL,
      name: TEST_NAME
    });

    if (response.data && response.data.customerId) {
      success(`Trial started for customer: ${response.data.customerId}`);
      return response.data.customerId;
    } else {
      error('Trial start failed - no customer ID returned');
      return null;
    }
  } catch (err) {
    error(`Trial start failed: ${err.message}`);
    return null;
  }
}

async function testCreateCheckoutSession(customerId) {
  try {
    info('Testing checkout session creation...');
    
    const response = await axios.post(`${API_BASE_URL}/stripe/create-checkout-session`, {
      priceId: 'price_standard_monthly', // This should be replaced with actual price ID
      customerId: customerId,
      successUrl: 'http://localhost:5173/dashboard?success=true',
      cancelUrl: 'http://localhost:5173/subscription?cancelled=true'
    });

    if (response.data && response.data.url) {
      success(`Checkout session created: ${response.data.url}`);
      return response.data.url;
    } else {
      error('Checkout session creation failed - no URL returned');
      return null;
    }
  } catch (err) {
    error(`Checkout session creation failed: ${err.message}`);
    return null;
  }
}

async function testGetSubscriptionStatus(customerId) {
  try {
    info('Testing subscription status retrieval...');
    
    const response = await axios.get(`${API_BASE_URL}/stripe/subscription/${customerId}`);

    if (response.data) {
      success(`Subscription status: ${JSON.stringify(response.data, null, 2)}`);
      return response.data;
    } else {
      warning('No subscription data returned');
      return null;
    }
  } catch (err) {
    error(`Subscription status retrieval failed: ${err.message}`);
    return null;
  }
}

async function testCreatePortalSession(customerId) {
  try {
    info('Testing customer portal session creation...');
    
    const response = await axios.post(`${API_BASE_URL}/stripe/create-portal-session`, {
      customerId: customerId,
      returnUrl: 'http://localhost:5173/dashboard'
    });

    if (response.data && response.data.url) {
      success(`Portal session created: ${response.data.url}`);
      return response.data.url;
    } else {
      error('Portal session creation failed - no URL returned');
      return null;
    }
  } catch (err) {
    error(`Portal session creation failed: ${err.message}`);
    return null;
  }
}

async function testWebhookEndpoint() {
  try {
    info('Testing webhook endpoint...');
    
    // This is a basic test - in reality, webhooks are tested with Stripe CLI
    const response = await axios.post(`${API_BASE_URL}/stripe/webhook`, {
      // Mock webhook payload
      type: 'customer.created',
      data: {
        object: {
          id: 'cus_test123',
          email: TEST_EMAIL
        }
      }
    }, {
      headers: {
        'stripe-signature': 'test_signature'
      }
    });

    // We expect this to fail signature verification, which is correct
    warning('Webhook endpoint test - signature verification expected to fail (this is correct)');
    return true;
  } catch (err) {
    if (err.response && err.response.status === 400) {
      success('Webhook endpoint is working (signature verification failed as expected)');
      return true;
    } else {
      error(`Webhook endpoint test failed: ${err.message}`);
      return false;
    }
  }
}

async function runAllTests() {
  log('\n🧪 Starting Stripe Integration Test Suite\n', 'bold');
  
  // Check if backend is running
  try {
    await axios.get(`${API_BASE_URL}/health`);
    success('Backend server is running');
  } catch (err) {
    error('Backend server is not running. Please start the backend first.');
    process.exit(1);
  }

  let customerId = null;
  let testResults = {
    customerCreation: false,
    trialStart: false,
    checkoutSession: false,
    subscriptionStatus: false,
    portalSession: false,
    webhookEndpoint: false
  };

  // Test 1: Create Customer
  customerId = await testCreateCustomer();
  testResults.customerCreation = !!customerId;

  // Test 2: Start Trial
  if (!customerId) {
    customerId = await testStartTrial();
  }
  testResults.trialStart = !!customerId;

  if (customerId) {
    // Test 3: Create Checkout Session
    const checkoutUrl = await testCreateCheckoutSession(customerId);
    testResults.checkoutSession = !!checkoutUrl;

    // Test 4: Get Subscription Status
    const subscriptionData = await testGetSubscriptionStatus(customerId);
    testResults.subscriptionStatus = !!subscriptionData;

    // Test 5: Create Portal Session
    const portalUrl = await testCreatePortalSession(customerId);
    testResults.portalSession = !!portalUrl;

    if (checkoutUrl) {
      info(`\n💳 To complete payment testing, visit: ${checkoutUrl}`);
    }

    if (portalUrl) {
      info(`🏛️  To test customer portal, visit: ${portalUrl}`);
    }
  }

  // Test 6: Webhook Endpoint
  testResults.webhookEndpoint = await testWebhookEndpoint();

  // Summary
  log('\n📊 Test Results Summary\n', 'bold');
  
  Object.entries(testResults).forEach(([test, passed]) => {
    if (passed) {
      success(`${test}: PASSED`);
    } else {
      error(`${test}: FAILED`);
    }
  });

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`, 'bold');
  
  if (passedTests === totalTests) {
    success('\n🎉 All tests passed! Stripe integration is working correctly.');
  } else if (passedTests >= totalTests * 0.8) {
    warning('\n⚠️  Most tests passed. Check failed tests and fix issues.');
  } else {
    error('\n❌ Multiple tests failed. Stripe integration needs attention.');
  }

  log('\n📝 Next Steps:', 'bold');
  info('1. Fix any failed tests');
  info('2. Test with Stripe CLI for webhook verification');
  info('3. Test with real payment methods in test mode');
  info('4. Verify all payment flows in the frontend application');
  
  rl.close();
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🧪 Stripe Integration Test Suite

Usage: node test-stripe-integration.js [options]

Options:
  --help, -h     Show this help message
  --api-url      Set API base URL (default: http://localhost:3001/api)

Environment Variables:
  API_BASE_URL   Base URL for the API (default: http://localhost:3001/api)

Examples:
  node test-stripe-integration.js
  node test-stripe-integration.js --api-url https://api.wewish.app
  API_BASE_URL=https://api.wewish.app node test-stripe-integration.js
  `);
  process.exit(0);
}

// Override API URL if provided
const apiUrlIndex = process.argv.indexOf('--api-url');
if (apiUrlIndex !== -1 && process.argv[apiUrlIndex + 1]) {
  API_BASE_URL = process.argv[apiUrlIndex + 1];
}

// Run the tests
runAllTests().catch(err => {
  error(`Test suite failed: ${err.message}`);
  process.exit(1);
});
