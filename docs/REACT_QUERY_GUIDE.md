# TanStack React Query Integration Guide

This guide explains how TanStack React Query has been integrated into the WeWish birthday management application.

## 🚀 What's Been Set Up

### 1. Installation
- `@tanstack/react-query` - Core library
- `@tanstack/react-query-devtools` - Development tools

### 2. Configuration
- **QueryClient**: Configured in `src/lib/queryClient.ts`
- **Provider**: Added to `src/App.tsx` provider chain
- **DevTools**: Available in development mode

### 3. Services Layer
- **`src/services/contactsService.ts`**: Mock API for contacts
- **`src/services/giftService.ts`**: Mock API for gifts

### 4. React Query Hooks
- **`src/hooks/useContacts.ts`**: Contact-related queries and mutations
- **`src/hooks/useGifts.ts`**: Gift-related queries and mutations

### 5. Example Component
- **`src/components/ContactsPageWithQuery.tsx`**: Demonstrates React Query usage

## 📋 Key Features

### Query Features
- **Automatic Caching**: Data is cached and reused across components
- **Background Refetching**: Stale data is refetched automatically
- **Loading States**: Built-in loading, error, and success states
- **Optimistic Updates**: UI updates immediately, syncs with server later
- **Query Invalidation**: Smart cache invalidation on mutations

### Mutation Features
- **Automatic Retries**: Failed requests are retried automatically
- **Optimistic Updates**: UI updates before server confirmation
- **Cache Updates**: Mutations automatically update related queries
- **Error Handling**: Centralized error handling

## 🔧 Usage Examples

### Basic Query
```typescript
import { useContactsQuery } from '../hooks/useContacts';

const MyComponent = () => {
  const { data: contacts, isLoading, error } = useContactsQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      {contacts.map(contact => (
        <div key={contact.id}>{contact.name}</div>
      ))}
    </div>
  );
};
```

### Mutation with Optimistic Updates
```typescript
import { useCreateContact } from '../hooks/useContacts';

const AddContactForm = () => {
  const createContact = useCreateContact();
  
  const handleSubmit = async (contactData) => {
    try {
      await createContact.mutateAsync(contactData);
      // Success! Cache is automatically updated
    } catch (error) {
      // Handle error
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
      <button 
        type="submit" 
        disabled={createContact.isPending}
      >
        {createContact.isPending ? 'Adding...' : 'Add Contact'}
      </button>
    </form>
  );
};
```

### Dependent Queries
```typescript
import { useContactQuery, useContactGiftHistoryQuery } from '../hooks/useContacts';

const ContactDetails = ({ contactId }) => {
  const { data: contact } = useContactQuery(contactId);
  const { data: giftHistory } = useContactGiftHistoryQuery(contactId);
  
  return (
    <div>
      <h1>{contact?.name}</h1>
      <p>Gifts given: {giftHistory?.length || 0}</p>
    </div>
  );
};
```

## 🎯 Query Keys Strategy

### Hierarchical Structure
```typescript
export const contactsKeys = {
  all: ['contacts'] as const,
  lists: () => [...contactsKeys.all, 'list'] as const,
  list: (userEmail: string) => [...contactsKeys.lists(), userEmail] as const,
  details: () => [...contactsKeys.all, 'detail'] as const,
  detail: (userEmail: string, id: string) => [...contactsKeys.details(), userEmail, id] as const,
};
```

### Benefits
- **Precise Invalidation**: Invalidate specific queries or groups
- **Efficient Caching**: Related queries share cache space
- **Easy Debugging**: Clear query identification in DevTools

## 🔄 Cache Management

### Automatic Updates
- **Create**: New items added to list cache
- **Update**: Specific item and list caches updated
- **Delete**: Item removed from all related caches

### Manual Invalidation
```typescript
// Invalidate all contact queries
queryClient.invalidateQueries({ queryKey: contactsKeys.all });

// Invalidate specific user's contacts
queryClient.invalidateQueries({ queryKey: contactsKeys.list(userEmail) });
```

## 🛠 Development Tools

### React Query DevTools
- **Query Inspector**: View all queries and their states
- **Cache Explorer**: Inspect cached data
- **Network Activity**: Monitor refetch behavior
- **Performance Metrics**: Query timing and performance

### Access DevTools
1. Open your app in development mode
2. Look for the React Query DevTools icon (usually bottom-left)
3. Click to open the DevTools panel

## 📊 Performance Benefits

### Before React Query
- Manual loading states
- Manual error handling
- No caching between components
- Duplicate API calls
- Complex state management

### After React Query
- Automatic loading/error states
- Intelligent caching
- Background refetching
- Optimistic updates
- Simplified component logic

## 🚀 Next Steps

### 1. Replace Context Usage
Gradually replace existing context-based data fetching with React Query hooks:

```typescript
// Before (Context)
const { contacts, loading, error } = useContacts();

// After (React Query)
const { data: contacts, isLoading, error } = useContactsQuery();
```

### 2. Add Real API Integration
Replace mock services with real API calls:

```typescript
// Update services to call real endpoints
export const contactsService = {
  getContacts: async (userEmail: string) => {
    const response = await fetch(`/api/users/${userEmail}/contacts`);
    return response.json();
  },
  // ... other methods
};
```

### 3. Implement Offline Support
Add offline capabilities with React Query:

```typescript
// Enable offline support
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      networkMode: 'offlineFirst',
    },
  },
});
```

### 4. Add Infinite Queries
For large datasets, implement infinite scrolling:

```typescript
import { useInfiniteQuery } from '@tanstack/react-query';

const useInfiniteContacts = () => {
  return useInfiniteQuery({
    queryKey: ['contacts', 'infinite'],
    queryFn: ({ pageParam = 0 }) => fetchContacts(pageParam),
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  });
};
```

## 🎉 Benefits Summary

✅ **Automatic caching and synchronization**  
✅ **Built-in loading and error states**  
✅ **Optimistic updates for better UX**  
✅ **Background refetching**  
✅ **Intelligent query invalidation**  
✅ **Reduced boilerplate code**  
✅ **Better performance**  
✅ **Excellent developer experience**  

React Query transforms data fetching from a complex, error-prone process into a simple, declarative experience!
