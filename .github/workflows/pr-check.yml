name: 🔍 Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

jobs:
  pr-validation:
    name: 🔍 PR Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 <PERSON>t changes
        run: |
          echo "🔍 Running linter on changed files..."
          npm run lint

      - name: 🧪 Run tests
        run: |
          echo "🧪 Running unit tests..."
          npm run test:ci

      - name: 🏗️ Test build
        run: |
          echo "🏗️ Testing build process..."
          npm run build

      - name: 📊 Generate coverage report
        run: |
          echo "📊 Generating coverage report for PR..."
          
          # Check if coverage meets minimum requirements
          if [ -f coverage/coverage-summary.json ]; then
            STATEMENTS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.statements.pct")
            BRANCHES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.branches.pct")
            FUNCTIONS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.functions.pct")
            LINES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.lines.pct")
            
            echo "📈 Coverage Summary:"
            echo "  Statements: ${STATEMENTS}%"
            echo "  Branches: ${BRANCHES}%"
            echo "  Functions: ${FUNCTIONS}%"
            echo "  Lines: ${LINES}%"
            
            # Calculate overall coverage
            OVERALL=$(echo "($STATEMENTS + $BRANCHES + $FUNCTIONS + $LINES) / 4" | bc -l)
            OVERALL_ROUNDED=$(printf "%.1f" $OVERALL)
            
            echo "  Overall: ${OVERALL_ROUNDED}%"
            
            # Set coverage status
            if (( $(echo "$OVERALL >= 80" | bc -l) )); then
              echo "✅ Coverage looks good!"
            elif (( $(echo "$OVERALL >= 70" | bc -l) )); then
              echo "⚠️ Coverage is acceptable but could be improved"
            else
              echo "❌ Coverage is below recommended threshold"
              echo "Please add tests to improve coverage before merging"
            fi
          fi

      - name: 📝 Comment coverage on PR
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info
          delete-old-comments: true

      - name: 🔍 Check for test files
        run: |
          echo "🔍 Checking if new code includes tests..."
          
          # Get list of changed files
          CHANGED_FILES=$(git diff --name-only origin/main...HEAD)
          
          # Check for new source files
          NEW_SRC_FILES=$(echo "$CHANGED_FILES" | grep -E '\.(ts|tsx)$' | grep -v '\.test\.' | grep -v '\.spec\.' | grep -E '^src/' || true)
          
          # Check for new test files
          NEW_TEST_FILES=$(echo "$CHANGED_FILES" | grep -E '\.(test|spec)\.(ts|tsx)$' || true)
          
          if [ -n "$NEW_SRC_FILES" ]; then
            echo "📝 New source files detected:"
            echo "$NEW_SRC_FILES"
            
            if [ -z "$NEW_TEST_FILES" ]; then
              echo "⚠️ No new test files found for new source code"
              echo "Consider adding tests for the new functionality"
            else
              echo "✅ New test files found:"
              echo "$NEW_TEST_FILES"
            fi
          else
            echo "ℹ️ No new source files in this PR"
          fi

      - name: 📋 PR Summary
        run: |
          echo "## 🔍 Pull Request Validation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Test results
          echo "### 🧪 Test Results" >> $GITHUB_STEP_SUMMARY
          echo "✅ All tests passed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Coverage results
          if [ -f coverage/coverage-summary.json ]; then
            echo "### 📊 Coverage Report" >> $GITHUB_STEP_SUMMARY
            
            STATEMENTS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.statements.pct")
            BRANCHES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.branches.pct")
            FUNCTIONS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.functions.pct")
            LINES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.lines.pct")
            
            echo "| Metric | Coverage |" >> $GITHUB_STEP_SUMMARY
            echo "|--------|----------|" >> $GITHUB_STEP_SUMMARY
            echo "| Statements | ${STATEMENTS}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Branches | ${BRANCHES}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Functions | ${FUNCTIONS}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Lines | ${LINES}% |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Build results
          echo "### 🏗️ Build Status" >> $GITHUB_STEP_SUMMARY
          echo "✅ Build completed successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Next steps
          echo "### 🎯 Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Review the code changes" >> $GITHUB_STEP_SUMMARY
          echo "- Check the coverage report above" >> $GITHUB_STEP_SUMMARY
          echo "- Ensure all feedback is addressed" >> $GITHUB_STEP_SUMMARY
          echo "- Ready for merge when approved!" >> $GITHUB_STEP_SUMMARY

  size-check:
    name: 📦 Bundle Size Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🏗️ Build for size analysis
        run: npm run build

      - name: 📊 Analyze bundle size
        run: |
          echo "📦 Analyzing bundle size..."
          
          if [ -d "dist" ]; then
            echo "📊 Build output size:"
            du -sh dist/
            
            echo ""
            echo "📄 Individual file sizes:"
            find dist -name "*.js" -o -name "*.css" | head -10 | xargs ls -lh
            
            # Check for large files (>1MB)
            LARGE_FILES=$(find dist -size +1M -name "*.js" -o -size +1M -name "*.css" || true)
            if [ -n "$LARGE_FILES" ]; then
              echo ""
              echo "⚠️ Large files detected (>1MB):"
              echo "$LARGE_FILES"
              echo "Consider code splitting or optimization"
            else
              echo ""
              echo "✅ No unusually large files detected"
            fi
          else
            echo "❌ Build directory not found"
            exit 1
          fi

  security-check:
    name: 🔒 Security Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Security audit
        run: |
          echo "🔒 Running security audit..."
          npm audit --audit-level=moderate || {
            echo "⚠️ Security vulnerabilities found"
            echo "Please review and fix before merging"
            exit 1
          }

      - name: 🔍 Check for secrets
        run: |
          echo "🔍 Checking for potential secrets..."
          
          # Check for common secret patterns
          if git diff --name-only origin/main...HEAD | xargs grep -l -E "(api_key|secret|password|token)" 2>/dev/null; then
            echo "⚠️ Potential secrets found in changed files"
            echo "Please review and ensure no sensitive data is committed"
          else
            echo "✅ No obvious secrets detected"
          fi

  auto-merge-check:
    name: 🤖 Auto-merge Eligibility
    runs-on: ubuntu-latest
    needs: [pr-validation, size-check, security-check]
    if: github.actor == 'dependabot[bot]' || contains(github.event.pull_request.labels.*.name, 'auto-merge')
    
    steps:
      - name: ✅ Auto-merge ready
        run: |
          echo "🤖 This PR is eligible for auto-merge"
          echo "All checks have passed:"
          echo "  ✅ Tests"
          echo "  ✅ Coverage"
          echo "  ✅ Build"
          echo "  ✅ Security"
          echo "  ✅ Bundle size"

      - name: 📋 Auto-merge summary
        run: |
          echo "## 🤖 Auto-merge Ready" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "This pull request has passed all automated checks and is ready for auto-merge." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ Passed Checks" >> $GITHUB_STEP_SUMMARY
          echo "- Unit tests and coverage" >> $GITHUB_STEP_SUMMARY
          echo "- Build verification" >> $GITHUB_STEP_SUMMARY
          echo "- Security audit" >> $GITHUB_STEP_SUMMARY
          echo "- Bundle size analysis" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "The PR will be automatically merged once approved by a maintainer." >> $GITHUB_STEP_SUMMARY
