name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20.x'

jobs:
  test:
    name: 🧪 Test & Build
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Lint code
        run: npm run lint

      - name: 🧪 Run tests with coverage
        run: npm run test:ci

      - name: 🏗️ Build application
        run: npm run build

      - name: 📊 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: wewish-coverage
          fail_ci_if_error: false

      - name: 📈 Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info

      - name: 💾 Archive build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/
          retention-days: 7

      - name: 💾 Archive coverage report
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: coverage/
          retention-days: 7

  quality-check:
    name: 🚦 Quality Gate
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🧪 Generate coverage
        run: npm run test:ci

      - name: 🚦 Check coverage thresholds
        run: |
          echo "🔍 Checking coverage thresholds..."
          
          if [ ! -f coverage/coverage-summary.json ]; then
            echo "❌ Coverage file not found!"
            exit 1
          fi
          
          # Extract coverage percentages
          STATEMENTS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.statements.pct")
          BRANCHES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.branches.pct")
          FUNCTIONS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.functions.pct")
          LINES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.lines.pct")
          
          echo "📊 Coverage Results:"
          echo "  Statements: ${STATEMENTS}%"
          echo "  Branches: ${BRANCHES}%"
          echo "  Functions: ${FUNCTIONS}%"
          echo "  Lines: ${LINES}%"
          
          # Set minimum threshold (adjust as needed)
          THRESHOLD=70
          
          # Check each metric
          check_threshold() {
            local metric=$1
            local value=$2
            if (( $(echo "$value < $THRESHOLD" | bc -l) )); then
              echo "❌ $metric coverage $value% is below $THRESHOLD%"
              return 1
            else
              echo "✅ $metric coverage $value% meets threshold"
              return 0
            fi
          }
          
          FAILED=false
          check_threshold "Statements" $STATEMENTS || FAILED=true
          check_threshold "Branches" $BRANCHES || FAILED=true
          check_threshold "Functions" $FUNCTIONS || FAILED=true
          check_threshold "Lines" $LINES || FAILED=true
          
          if [ "$FAILED" = true ]; then
            echo ""
            echo "💥 Quality gate failed! Some coverage metrics are below $THRESHOLD%"
            echo "Please add more tests to improve coverage."
            exit 1
          else
            echo ""
            echo "✅ Quality gate passed! All coverage metrics meet the $THRESHOLD% threshold."
          fi

      - name: 📋 Generate test summary
        if: always()
        run: |
          echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ -f coverage/coverage-summary.json ]; then
            STATEMENTS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.statements.pct")
            BRANCHES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.branches.pct")
            FUNCTIONS=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.functions.pct")
            LINES=$(node -p "JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.lines.pct")
            
            echo "| Metric | Coverage | Status |" >> $GITHUB_STEP_SUMMARY
            echo "|--------|----------|--------|" >> $GITHUB_STEP_SUMMARY
            echo "| Statements | ${STATEMENTS}% | $([ $(echo "$STATEMENTS >= 70" | bc -l) -eq 1 ] && echo "✅" || echo "❌") |" >> $GITHUB_STEP_SUMMARY
            echo "| Branches | ${BRANCHES}% | $([ $(echo "$BRANCHES >= 70" | bc -l) -eq 1 ] && echo "✅" || echo "❌") |" >> $GITHUB_STEP_SUMMARY
            echo "| Functions | ${FUNCTIONS}% | $([ $(echo "$FUNCTIONS >= 70" | bc -l) -eq 1 ] && echo "✅" || echo "❌") |" >> $GITHUB_STEP_SUMMARY
            echo "| Lines | ${LINES}% | $([ $(echo "$LINES >= 70" | bc -l) -eq 1 ] && echo "✅" || echo "❌") |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            
            # Overall status
            OVERALL=$(echo "($STATEMENTS + $BRANCHES + $FUNCTIONS + $LINES) / 4" | bc -l)
            OVERALL_ROUNDED=$(printf "%.1f" $OVERALL)
            
            if (( $(echo "$OVERALL >= 90" | bc -l) )); then
              echo "🎉 **Excellent!** Overall coverage: ${OVERALL_ROUNDED}%" >> $GITHUB_STEP_SUMMARY
            elif (( $(echo "$OVERALL >= 80" | bc -l) )); then
              echo "✅ **Good!** Overall coverage: ${OVERALL_ROUNDED}%" >> $GITHUB_STEP_SUMMARY
            elif (( $(echo "$OVERALL >= 70" | bc -l) )); then
              echo "⚠️ **Acceptable** Overall coverage: ${OVERALL_ROUNDED}%" >> $GITHUB_STEP_SUMMARY
            else
              echo "❌ **Needs Improvement** Overall coverage: ${OVERALL_ROUNDED}%" >> $GITHUB_STEP_SUMMARY
            fi
          fi

  security:
    name: 🔒 Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Run npm audit
        run: npm audit --audit-level=moderate

      - name: 🛡️ Run security scan
        run: |
          echo "🔒 Running security checks..."
          
          # Check for common security issues
          echo "Checking for sensitive files..."
          if find . -name "*.env*" -not -path "./node_modules/*" -not -name "*.example" | grep -q .; then
            echo "⚠️ Found .env files - ensure they're in .gitignore"
          fi
          
          # Check package.json for known vulnerable packages
          echo "✅ Security audit completed"

  deploy-ready:
    name: 🚀 Deployment Ready
    runs-on: ubuntu-latest
    needs: [test, quality-check, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: 🎉 Ready for deployment
        run: |
          echo "🚀 All checks passed! WeWish SaaS is ready for deployment."
          echo ""
          echo "✅ Tests: PASSED"
          echo "✅ Quality Gate: PASSED" 
          echo "✅ Security: PASSED"
          echo "✅ Build: PASSED"
          echo ""
          echo "🎯 Next steps:"
          echo "  1. Review the changes"
          echo "  2. Deploy to staging environment"
          echo "  3. Run integration tests"
          echo "  4. Deploy to production"

      - name: 📊 Deployment summary
        run: |
          echo "## 🚀 Deployment Ready" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "All quality checks have passed! The application is ready for deployment." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ Completed Checks" >> $GITHUB_STEP_SUMMARY
          echo "- Unit tests and coverage" >> $GITHUB_STEP_SUMMARY
          echo "- Code quality and linting" >> $GITHUB_STEP_SUMMARY
          echo "- Security audit" >> $GITHUB_STEP_SUMMARY
          echo "- Build verification" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🎯 Ready for:" >> $GITHUB_STEP_SUMMARY
          echo "- Staging deployment" >> $GITHUB_STEP_SUMMARY
          echo "- Production deployment" >> $GITHUB_STEP_SUMMARY
          echo "- Release creation" >> $GITHUB_STEP_SUMMARY
