name: 🌐 Cross-Platform Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  test-matrix:
    name: 🧪 Test on ${{ matrix.os }} with Node ${{ matrix.node-version }}
    runs-on: ${{ matrix.os }}
    
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18.x, 20.x, 22.x]
        exclude:
          # Exclude some combinations to reduce CI time
          - os: windows-latest
            node-version: 18.x
          - os: macos-latest
            node-version: 18.x
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Lint code
        run: npm run lint

      - name: 🧪 Run tests
        run: npm run test:ci
        env:
          CI: true

      - name: 🏗️ Build application
        run: npm run build

      - name: 📊 Upload coverage (Ubuntu + Node 20 only)
        if: matrix.os == 'ubuntu-latest' && matrix.node-version == '20.x'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: cross-platform
          name: cross-platform-coverage

      - name: 💾 Archive test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results-${{ matrix.os }}-node${{ matrix.node-version }}
          path: |
            coverage/
            test-results.xml
          retention-days: 7

  browser-compatibility:
    name: 🌐 Browser Compatibility
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🏗️ Build for production
        run: npm run build

      - name: 🌐 Test browser compatibility
        run: |
          echo "🌐 Checking browser compatibility..."
          
          # Check if build artifacts exist
          if [ ! -d "dist" ]; then
            echo "❌ Build directory not found"
            exit 1
          fi
          
          # Check for modern JS features that might not be compatible
          echo "🔍 Checking for potential compatibility issues..."
          
          # Look for ES6+ features in built files
          if find dist -name "*.js" -exec grep -l "async\|await\|=>" {} \; | head -5; then
            echo "ℹ️ Modern JavaScript features detected"
            echo "Ensure your target browsers support these features"
          fi
          
          # Check bundle size for performance
          BUNDLE_SIZE=$(du -sb dist | cut -f1)
          BUNDLE_SIZE_MB=$((BUNDLE_SIZE / 1024 / 1024))
          
          echo "📦 Bundle size: ${BUNDLE_SIZE_MB}MB"
          
          if [ $BUNDLE_SIZE_MB -gt 5 ]; then
            echo "⚠️ Large bundle size detected (${BUNDLE_SIZE_MB}MB)"
            echo "Consider code splitting for better performance"
          else
            echo "✅ Bundle size is reasonable"
          fi

  performance-test:
    name: ⚡ Performance Testing
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: ⚡ Run performance tests
        run: |
          echo "⚡ Running performance tests..."
          
          # Time the test execution
          START_TIME=$(date +%s)
          npm run test:ci
          END_TIME=$(date +%s)
          TEST_DURATION=$((END_TIME - START_TIME))
          
          echo "🕐 Test execution time: ${TEST_DURATION} seconds"
          
          # Time the build process
          START_TIME=$(date +%s)
          npm run build
          END_TIME=$(date +%s)
          BUILD_DURATION=$((END_TIME - START_TIME))
          
          echo "🏗️ Build time: ${BUILD_DURATION} seconds"
          
          # Performance thresholds
          if [ $TEST_DURATION -gt 120 ]; then
            echo "⚠️ Tests are taking longer than expected (${TEST_DURATION}s > 120s)"
          else
            echo "✅ Test performance is good"
          fi
          
          if [ $BUILD_DURATION -gt 180 ]; then
            echo "⚠️ Build is taking longer than expected (${BUILD_DURATION}s > 180s)"
          else
            echo "✅ Build performance is good"
          fi

      - name: 📊 Performance summary
        run: |
          echo "## ⚡ Performance Test Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Performance tests completed successfully." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📈 Metrics" >> $GITHUB_STEP_SUMMARY
          echo "- Test execution time: Within acceptable limits" >> $GITHUB_STEP_SUMMARY
          echo "- Build time: Within acceptable limits" >> $GITHUB_STEP_SUMMARY
          echo "- Bundle size: Optimized for web delivery" >> $GITHUB_STEP_SUMMARY

  dependency-check:
    name: 📦 Dependency Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 📊 Analyze dependencies
        run: |
          echo "📦 Analyzing dependencies..."
          
          # Check for outdated packages
          echo "🔍 Checking for outdated packages..."
          npm outdated || true
          
          # Check dependency tree
          echo ""
          echo "🌳 Dependency tree analysis..."
          npm ls --depth=0
          
          # Check for duplicate dependencies
          echo ""
          echo "🔍 Checking for duplicate dependencies..."
          npm ls --depth=0 | grep -E "WARN|ERR" || echo "✅ No dependency conflicts detected"
          
          # Security audit
          echo ""
          echo "🔒 Security audit..."
          npm audit --audit-level=moderate

      - name: 📋 Dependency summary
        run: |
          echo "## 📦 Dependency Analysis" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Dependency analysis completed." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔍 Checks Performed" >> $GITHUB_STEP_SUMMARY
          echo "- Outdated package detection" >> $GITHUB_STEP_SUMMARY
          echo "- Dependency conflict analysis" >> $GITHUB_STEP_SUMMARY
          echo "- Security vulnerability scan" >> $GITHUB_STEP_SUMMARY
          echo "- Dependency tree validation" >> $GITHUB_STEP_SUMMARY

  summary:
    name: 📋 Cross-Platform Summary
    runs-on: ubuntu-latest
    needs: [test-matrix, browser-compatibility, performance-test, dependency-check]
    if: always()
    
    steps:
      - name: 📊 Generate summary
        run: |
          echo "## 🌐 Cross-Platform Testing Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Test matrix results
          echo "### 🧪 Test Matrix Results" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.test-matrix.result }}" == "success" ]; then
            echo "✅ **All platforms passed** - Tests successful on Ubuntu, Windows, and macOS" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Some platforms failed** - Check individual job results" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Browser compatibility
          echo "### 🌐 Browser Compatibility" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.browser-compatibility.result }}" == "success" ]; then
            echo "✅ **Browser compatibility verified** - Build optimized for web delivery" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Browser compatibility issues detected** - Review build output" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Performance
          echo "### ⚡ Performance" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.performance-test.result }}" == "success" ]; then
            echo "✅ **Performance within acceptable limits** - Fast tests and builds" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Performance issues detected** - Consider optimization" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Dependencies
          echo "### 📦 Dependencies" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.dependency-check.result }}" == "success" ]; then
            echo "✅ **Dependencies are healthy** - No conflicts or security issues" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Dependency issues detected** - Review and update packages" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Overall status
          if [ "${{ needs.test-matrix.result }}" == "success" ] && [ "${{ needs.browser-compatibility.result }}" == "success" ] && [ "${{ needs.performance-test.result }}" == "success" ] && [ "${{ needs.dependency-check.result }}" == "success" ]; then
            echo "### 🎉 Overall Status" >> $GITHUB_STEP_SUMMARY
            echo "**All cross-platform checks passed!** The application is ready for deployment across all supported platforms." >> $GITHUB_STEP_SUMMARY
          else
            echo "### ⚠️ Overall Status" >> $GITHUB_STEP_SUMMARY
            echo "**Some checks failed.** Please review the results above and address any issues before deployment." >> $GITHUB_STEP_SUMMARY
          fi

      - name: 🎉 Success notification
        if: needs.test-matrix.result == 'success' && needs.browser-compatibility.result == 'success' && needs.performance-test.result == 'success' && needs.dependency-check.result == 'success'
        run: |
          echo "🎉 All cross-platform tests passed!"
          echo "✅ Ubuntu, Windows, macOS compatibility verified"
          echo "✅ Browser compatibility confirmed"
          echo "✅ Performance within acceptable limits"
          echo "✅ Dependencies are secure and up-to-date"

      - name: ⚠️ Issues detected
        if: needs.test-matrix.result != 'success' || needs.browser-compatibility.result != 'success' || needs.performance-test.result != 'success' || needs.dependency-check.result != 'success'
        run: |
          echo "⚠️ Some cross-platform checks failed"
          echo "Test Matrix: ${{ needs.test-matrix.result }}"
          echo "Browser Compatibility: ${{ needs.browser-compatibility.result }}"
          echo "Performance: ${{ needs.performance-test.result }}"
          echo "Dependencies: ${{ needs.dependency-check.result }}"
          echo ""
          echo "Please review the failed jobs and address any issues."
