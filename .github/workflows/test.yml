name: 🧪 Unit Tests & Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  test:
    name: 🧪 Run Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Lint code
        run: npm run lint

      - name: 🏗️ Build project
        run: npm run build

      - name: 🧪 Run unit tests
        run: npm run test:ci
        env:
          CI: true

      - name: 📊 Upload coverage to Codecov
        if: matrix.node-version == '20.x'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

      - name: 📈 Coverage Report
        if: matrix.node-version == '20.x'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info
          delete-old-comments: true

      - name: 📋 Test Summary
        if: matrix.node-version == '20.x'
        run: |
          echo "## 🧪 Test Results" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Coverage |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|----------|" >> $GITHUB_STEP_SUMMARY
          
          # Extract coverage percentages from coverage summary
          if [ -f coverage/coverage-summary.json ]; then
            STATEMENTS=$(cat coverage/coverage-summary.json | jq -r '.total.statements.pct')
            BRANCHES=$(cat coverage/coverage-summary.json | jq -r '.total.branches.pct')
            FUNCTIONS=$(cat coverage/coverage-summary.json | jq -r '.total.functions.pct')
            LINES=$(cat coverage/coverage-summary.json | jq -r '.total.lines.pct')
            
            echo "| Statements | ${STATEMENTS}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Branches | ${BRANCHES}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Functions | ${FUNCTIONS}% |" >> $GITHUB_STEP_SUMMARY
            echo "| Lines | ${LINES}% |" >> $GITHUB_STEP_SUMMARY
            
            # Add status badges
            if (( $(echo "$STATEMENTS >= 90" | bc -l) )); then
              echo "✅ **Excellent coverage!** All metrics above 90%" >> $GITHUB_STEP_SUMMARY
            elif (( $(echo "$STATEMENTS >= 80" | bc -l) )); then
              echo "⚠️ **Good coverage** - Consider improving to 90%+" >> $GITHUB_STEP_SUMMARY
            else
              echo "❌ **Coverage needs improvement** - Below 80% threshold" >> $GITHUB_STEP_SUMMARY
            fi
          fi

      - name: 💾 Archive test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results-node-${{ matrix.node-version }}
          path: |
            coverage/
            test-results.xml
          retention-days: 30

      - name: 💾 Archive coverage reports
        if: matrix.node-version == '20.x'
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: coverage/lcov-report/
          retention-days: 30

  quality-gate:
    name: 🚦 Quality Gate
    runs-on: ubuntu-latest
    needs: test
    if: always()
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🧪 Run tests with coverage
        run: npm run test:ci

      - name: 🚦 Check coverage thresholds
        run: |
          echo "🔍 Checking coverage thresholds..."
          
          # Extract coverage data
          STATEMENTS=$(cat coverage/coverage-summary.json | jq -r '.total.statements.pct')
          BRANCHES=$(cat coverage/coverage-summary.json | jq -r '.total.branches.pct')
          FUNCTIONS=$(cat coverage/coverage-summary.json | jq -r '.total.functions.pct')
          LINES=$(cat coverage/coverage-summary.json | jq -r '.total.lines.pct')
          
          echo "📊 Coverage Results:"
          echo "  Statements: ${STATEMENTS}%"
          echo "  Branches: ${BRANCHES}%"
          echo "  Functions: ${FUNCTIONS}%"
          echo "  Lines: ${LINES}%"
          
          # Check thresholds (80% minimum for CI)
          THRESHOLD=80
          FAILED=false
          
          if (( $(echo "$STATEMENTS < $THRESHOLD" | bc -l) )); then
            echo "❌ Statements coverage ${STATEMENTS}% is below ${THRESHOLD}%"
            FAILED=true
          fi
          
          if (( $(echo "$BRANCHES < $THRESHOLD" | bc -l) )); then
            echo "❌ Branches coverage ${BRANCHES}% is below ${THRESHOLD}%"
            FAILED=true
          fi
          
          if (( $(echo "$FUNCTIONS < $THRESHOLD" | bc -l) )); then
            echo "❌ Functions coverage ${FUNCTIONS}% is below ${THRESHOLD}%"
            FAILED=true
          fi
          
          if (( $(echo "$LINES < $THRESHOLD" | bc -l) )); then
            echo "❌ Lines coverage ${LINES}% is below ${THRESHOLD}%"
            FAILED=true
          fi
          
          if [ "$FAILED" = true ]; then
            echo "💥 Quality gate failed! Coverage below ${THRESHOLD}% threshold."
            exit 1
          else
            echo "✅ Quality gate passed! All coverage metrics above ${THRESHOLD}%."
          fi

      - name: 🎯 Performance Check
        run: |
          echo "⚡ Running performance checks..."
          
          # Check test execution time
          START_TIME=$(date +%s)
          npm run test:ci > /dev/null 2>&1
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))
          
          echo "🕐 Test execution time: ${DURATION} seconds"
          
          # Warn if tests take too long (over 2 minutes)
          if [ $DURATION -gt 120 ]; then
            echo "⚠️ Tests are taking longer than expected (${DURATION}s > 120s)"
            echo "Consider optimizing slow tests or splitting test suites"
          else
            echo "✅ Test performance is good (${DURATION}s)"
          fi

  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 🔧 Install dependencies
        run: npm ci

      - name: 🔍 Run security audit
        run: |
          echo "🔒 Running npm security audit..."
          npm audit --audit-level=moderate
          
          echo "🔍 Checking for known vulnerabilities..."
          npx audit-ci --moderate

      - name: 🛡️ CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: 🏗️ Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  notify:
    name: 📢 Notify Results
    runs-on: ubuntu-latest
    needs: [test, quality-gate, security-scan]
    if: always()
    
    steps:
      - name: 📊 Workflow Summary
        run: |
          echo "## 🎯 WeWish SaaS - Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Test results
          if [ "${{ needs.test.result }}" == "success" ]; then
            echo "✅ **Unit Tests**: All tests passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Unit Tests**: Some tests failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Quality gate results
          if [ "${{ needs.quality-gate.result }}" == "success" ]; then
            echo "✅ **Quality Gate**: Coverage thresholds met" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Quality Gate**: Coverage below threshold" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Security scan results
          if [ "${{ needs.security-scan.result }}" == "success" ]; then
            echo "✅ **Security Scan**: No vulnerabilities found" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Security Scan**: Potential issues detected" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Next Steps" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.test.result }}" == "success" ] && [ "${{ needs.quality-gate.result }}" == "success" ] && [ "${{ needs.security-scan.result }}" == "success" ]; then
            echo "🚀 **Ready for deployment!** All checks passed." >> $GITHUB_STEP_SUMMARY
          else
            echo "🔧 **Action required:** Please review and fix the failing checks above." >> $GITHUB_STEP_SUMMARY
          fi

      - name: 🎉 Success Notification
        if: needs.test.result == 'success' && needs.quality-gate.result == 'success' && needs.security-scan.result == 'success'
        run: |
          echo "🎉 All tests passed! WeWish SaaS is ready for deployment."
          echo "✅ Unit tests: PASSED"
          echo "✅ Coverage: PASSED"
          echo "✅ Security: PASSED"

      - name: 💥 Failure Notification
        if: needs.test.result != 'success' || needs.quality-gate.result != 'success' || needs.security-scan.result != 'success'
        run: |
          echo "💥 Some checks failed. Please review the results above."
          echo "Test Status: ${{ needs.test.result }}"
          echo "Quality Gate: ${{ needs.quality-gate.result }}"
          echo "Security Scan: ${{ needs.security-scan.result }}"
          exit 1
