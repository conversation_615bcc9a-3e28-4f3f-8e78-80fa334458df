const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const router = express.Router();

// Import storage from stripe routes
const stripeRoutes = require('./stripe');

// Stripe webhook endpoint
router.post('/stripe', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('Received Stripe webhook event:', event.type);

  try {
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;

      case 'customer.created':
        await handleCustomerCreated(event.data.object);
        break;

      case 'customer.updated':
        await handleCustomerUpdated(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Webhook event handlers
async function handleCheckoutSessionCompleted(session) {
  console.log('Checkout session completed:', session.id);
  
  try {
    // Get customer and subscription details
    const customerId = session.customer;
    const subscriptionId = session.subscription;

    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      
      // Store subscription info
      stripeRoutes.subscriptions.set(customerId, {
        id: subscription.id,
        status: subscription.status,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        plan: {
          id: subscription.items.data[0].price.id,
          nickname: subscription.items.data[0].price.nickname || 'Unknown',
          amount: subscription.items.data[0].price.unit_amount,
          currency: subscription.items.data[0].price.currency,
          interval: subscription.items.data[0].price.recurring.interval,
        },
        customer: subscription.customer,
      });

      // Remove trial status if exists
      if (stripeRoutes.trials.has(customerId)) {
        stripeRoutes.trials.delete(customerId);
      }

      console.log(`Subscription ${subscriptionId} activated for customer ${customerId}`);
    }
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

async function handleSubscriptionCreated(subscription) {
  console.log('Subscription created:', subscription.id);
  
  try {
    const customerId = subscription.customer;
    
    // Store subscription info
    stripeRoutes.subscriptions.set(customerId, {
      id: subscription.id,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      plan: {
        id: subscription.items.data[0].price.id,
        nickname: subscription.items.data[0].price.nickname || 'Unknown',
        amount: subscription.items.data[0].price.unit_amount,
        currency: subscription.items.data[0].price.currency,
        interval: subscription.items.data[0].price.recurring.interval,
      },
      customer: subscription.customer,
    });

    console.log(`Subscription ${subscription.id} created for customer ${customerId}`);
  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionUpdated(subscription) {
  console.log('Subscription updated:', subscription.id);
  
  try {
    const customerId = subscription.customer;
    
    // Update subscription info
    stripeRoutes.subscriptions.set(customerId, {
      id: subscription.id,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      plan: {
        id: subscription.items.data[0].price.id,
        nickname: subscription.items.data[0].price.nickname || 'Unknown',
        amount: subscription.items.data[0].price.unit_amount,
        currency: subscription.items.data[0].price.currency,
        interval: subscription.items.data[0].price.recurring.interval,
      },
      customer: subscription.customer,
    });

    console.log(`Subscription ${subscription.id} updated for customer ${customerId}`);
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  console.log('Subscription deleted:', subscription.id);
  
  try {
    const customerId = subscription.customer;
    
    // Remove subscription info
    if (stripeRoutes.subscriptions.has(customerId)) {
      stripeRoutes.subscriptions.delete(customerId);
    }

    console.log(`Subscription ${subscription.id} deleted for customer ${customerId}`);
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  console.log('Invoice payment succeeded:', invoice.id);
  
  try {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      
      // Update subscription info with latest period
      stripeRoutes.subscriptions.set(customerId, {
        id: subscription.id,
        status: subscription.status,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        plan: {
          id: subscription.items.data[0].price.id,
          nickname: subscription.items.data[0].price.nickname || 'Unknown',
          amount: subscription.items.data[0].price.unit_amount,
          currency: subscription.items.data[0].price.currency,
          interval: subscription.items.data[0].price.recurring.interval,
        },
        customer: subscription.customer,
      });

      console.log(`Payment succeeded for subscription ${subscriptionId}`);
    }
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleInvoicePaymentFailed(invoice) {
  console.log('Invoice payment failed:', invoice.id);
  
  try {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    // Here you could implement logic to:
    // - Send payment failure notifications
    // - Temporarily suspend account access
    // - Retry payment logic
    
    console.log(`Payment failed for customer ${customerId}, subscription ${subscriptionId}`);
    
    // For now, just log the failure
    // In production, you might want to update user status or send notifications
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

async function handleCustomerCreated(customer) {
  console.log('Customer created:', customer.id);
  
  try {
    // Store customer info
    if (customer.email) {
      stripeRoutes.customers.set(customer.email, customer);
    }

    console.log(`Customer ${customer.id} created with email ${customer.email}`);
  } catch (error) {
    console.error('Error handling customer created:', error);
  }
}

async function handleCustomerUpdated(customer) {
  console.log('Customer updated:', customer.id);
  
  try {
    // Update customer info
    if (customer.email) {
      stripeRoutes.customers.set(customer.email, customer);
    }

    console.log(`Customer ${customer.id} updated`);
  } catch (error) {
    console.error('Error handling customer updated:', error);
  }
}

module.exports = router;
