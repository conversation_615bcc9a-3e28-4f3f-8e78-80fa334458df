const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// In-memory storage for demo (replace with database in production)
const customers = new Map();
const subscriptions = new Map();
const trials = new Map();

// Create checkout session
router.post('/create-checkout-session', async (req, res) => {
  try {
    const { priceId, customerId, successUrl, cancelUrl } = req.body;

    if (!priceId) {
      return res.status(400).json({ error: 'Price ID is required' });
    }

    const sessionParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url:
        successUrl || `${process.env.FRONTEND_URL}/dashboard?success=true`,
      cancel_url:
        cancelUrl || `${process.env.FRONTEND_URL}/subscription?cancelled=true`,
      automatic_tax: { enabled: true },
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto'
      }
    };

    // If customer ID provided, use existing customer
    if (customerId) {
      sessionParams.customer = customerId;
    } else {
      // Create new customer
      sessionParams.customer_creation = 'always';
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    res.json({
      sessionId: session.id,
      url: session.url
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create customer portal session
router.post('/create-portal-session', async (req, res) => {
  try {
    const { customerId, returnUrl } = req.body;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl || `${process.env.FRONTEND_URL}/subscription`
    });

    res.json({ url: session.url });
  } catch (error) {
    console.error('Error creating portal session:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create customer
router.post('/create-customer', async (req, res) => {
  try {
    const { email, name } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        created_by: 'wewish_app'
      }
    });

    // Store customer info
    customers.set(email, customer);

    res.json({ id: customer.id });
  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get customer by email
router.get('/customer', async (req, res) => {
  try {
    const { email } = req.query;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Check local storage first
    if (customers.has(email)) {
      return res.json(customers.get(email));
    }

    // Search Stripe for customer
    const customers_list = await stripe.customers.list({
      email,
      limit: 1
    });

    if (customers_list.data.length > 0) {
      const customer = customers_list.data[0];
      customers.set(email, customer);
      res.json({ id: customer.id });
    } else {
      res.status(404).json({ error: 'Customer not found' });
    }
  } catch (error) {
    console.error('Error getting customer:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get subscription
router.get('/subscription/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;

    const subscriptions_list = await stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
      limit: 1
    });

    if (subscriptions_list.data.length > 0) {
      const subscription = subscriptions_list.data[0];
      res.json({
        id: subscription.id,
        status: subscription.status,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        plan: {
          id: subscription.items.data[0].price.id,
          nickname: subscription.items.data[0].price.nickname || 'Unknown',
          amount: subscription.items.data[0].price.unit_amount,
          currency: subscription.items.data[0].price.currency,
          interval: subscription.items.data[0].price.recurring.interval
        },
        customer: subscription.customer
      });
    } else {
      res.status(404).json({ error: 'No active subscription found' });
    }
  } catch (error) {
    console.error('Error getting subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cancel subscription
router.post('/cancel-subscription', async (req, res) => {
  try {
    const { subscriptionId } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({ error: 'Subscription ID is required' });
    }

    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true
    });

    res.json({ message: 'Subscription will be cancelled at period end' });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update subscription
router.post('/update-subscription', async (req, res) => {
  try {
    const { subscriptionId, newPriceId } = req.body;

    if (!subscriptionId || !newPriceId) {
      return res
        .status(400)
        .json({ error: 'Subscription ID and new price ID are required' });
    }

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    const updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        items: [
          {
            id: subscription.items.data[0].id,
            price: newPriceId
          }
        ],
        proration_behavior: 'create_prorations'
      }
    );

    res.json({
      id: updatedSubscription.id,
      status: updatedSubscription.status,
      current_period_start: updatedSubscription.current_period_start,
      current_period_end: updatedSubscription.current_period_end,
      plan: {
        id: updatedSubscription.items.data[0].price.id,
        nickname: updatedSubscription.items.data[0].price.nickname || 'Unknown',
        amount: updatedSubscription.items.data[0].price.unit_amount,
        currency: updatedSubscription.items.data[0].price.currency,
        interval: updatedSubscription.items.data[0].price.recurring.interval
      },
      customer: updatedSubscription.customer
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Start trial
router.post('/start-trial', async (req, res) => {
  try {
    const { email, name } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Create or get customer
    let customer;
    if (customers.has(email)) {
      customer = customers.get(email);
    } else {
      customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          trial_start: new Date().toISOString(),
          created_by: 'wewish_app'
        }
      });
      customers.set(email, customer);
    }

    // Store trial info
    trials.set(customer.id, {
      customerId: customer.id,
      email,
      trialStart: new Date().toISOString(),
      trialEnd: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString() // 14 days
    });

    res.json({ customerId: customer.id });
  } catch (error) {
    console.error('Error starting trial:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get trial status
router.get('/trial-status/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;

    if (trials.has(customerId)) {
      const trial = trials.get(customerId);
      const now = new Date();
      const trialEnd = new Date(trial.trialEnd);
      const daysLeft = Math.max(
        0,
        Math.ceil((trialEnd - now) / (1000 * 60 * 60 * 24))
      );

      res.json({
        isTrialActive: daysLeft > 0,
        trialDaysLeft: daysLeft,
        trialStartDate: trial.trialStart
      });
    } else {
      res.json({
        isTrialActive: false,
        trialDaysLeft: 0,
        trialStartDate: null
      });
    }
  } catch (error) {
    console.error('Error getting trial status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Export storage for webhook access
router.customers = customers;
router.subscriptions = subscriptions;
router.trials = trials;

module.exports = router;
