{"name": "trackcelebrations-backend", "version": "1.0.0", "description": "TrackCelebrations SaaS Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "stripe": "^14.12.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "body-parser": "^1.20.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["saas", "stripe", "subscription", "birthday", "management"], "author": "TrackCelebrations Team", "license": "MIT"}