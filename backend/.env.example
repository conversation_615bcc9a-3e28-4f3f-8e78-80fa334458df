# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (replace with your actual price IDs from Stripe Dashboard)
STRIPE_PRICE_STANDARD_MONTHLY=price_1234567890
STRIPE_PRICE_STANDARD_YEARLY=price_1234567891
STRIPE_PRICE_ELITE_MONTHLY=price_1234567892
STRIPE_PRICE_ELITE_YEARLY=price_1234567893
STRIPE_PRICE_PREMIUM_MONTHLY=price_1234567894
STRIPE_PRICE_PREMIUM_YEARLY=price_1234567895

# Database Configuration (for future use)
DATABASE_URL=postgresql://username:password@localhost:5432/trackcelebrations
REDIS_URL=redis://localhost:6379

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# JWT Configuration (for future authentication)
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
