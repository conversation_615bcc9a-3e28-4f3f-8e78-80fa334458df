const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const bodyParser = require('body-parser');
require('dotenv').config();

const stripeRoutes = require('./routes/stripe');
const webhookRoutes = require('./routes/webhooks');

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true,
}));

// Body parsing middleware
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// Raw body parser for Stripe webhooks
app.use('/api/webhooks/stripe', bodyParser.raw({ type: 'application/json' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API routes
app.use('/api/stripe', stripeRoutes);
app.use('/api/webhooks', webhookRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  if (err.type === 'StripeCardError') {
    return res.status(400).json({
      error: {
        message: err.message,
        type: 'card_error',
        code: err.code,
      },
    });
  }
  
  if (err.type === 'StripeInvalidRequestError') {
    return res.status(400).json({
      error: {
        message: err.message,
        type: 'invalid_request_error',
      },
    });
  }
  
  res.status(500).json({
    error: {
      message: 'Internal server error',
      type: 'api_error',
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Route not found',
      type: 'not_found',
    },
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 WeWish Backend API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`💳 Stripe API: http://localhost:${PORT}/api/stripe`);
  console.log(`🔗 Webhooks: http://localhost:${PORT}/api/webhooks`);
});

module.exports = app;
