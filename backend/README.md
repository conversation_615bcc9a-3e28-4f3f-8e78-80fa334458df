# WeWish Backend API

Express.js backend API for WeWish SaaS application with Stripe integration.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Stripe account

### Installation

1. **Install dependencies**
```bash
cd backend
npm install
```

2. **Environment Setup**
```bash
cp .env.example .env
# Edit .env with your actual values
```

3. **Start Development Server**
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## 🔧 Configuration

### Required Environment Variables

```env
# Stripe Keys (from Stripe Dashboard)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Frontend URL
FRONTEND_URL=http://localhost:5173
```

### Stripe Setup

1. **Create Stripe Account** at https://stripe.com
2. **Get API Keys** from Dashboard > Developers > API keys
3. **Create Products & Prices**:
   - Standard Plan: $7.99/month, $79.99/year
   - Elite Plan: $15.99/month, $159.99/year  
   - Premium Plan: $23.99/month, $239.99/year
4. **Set up Webhook Endpoint** at `your-domain.com/api/webhooks/stripe`

## 📡 API Endpoints

### Stripe Routes (`/api/stripe`)

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/create-checkout-session` | Create Stripe checkout session |
| POST | `/create-portal-session` | Create customer portal session |
| POST | `/create-customer` | Create new Stripe customer |
| GET | `/customer?email=` | Get customer by email |
| GET | `/subscription/:customerId` | Get customer subscription |
| POST | `/cancel-subscription` | Cancel subscription |
| POST | `/update-subscription` | Update subscription plan |
| POST | `/start-trial` | Start 14-day trial |
| GET | `/trial-status/:customerId` | Get trial status |

### Webhook Routes (`/api/webhooks`)

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/stripe` | Handle Stripe webhook events |

### Health Check

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | API health status |

## 🔒 Security Features

- **Helmet.js** - Security headers
- **Rate Limiting** - 100 requests per 15 minutes
- **CORS** - Configured for frontend domain
- **Webhook Verification** - Stripe signature validation
- **Input Validation** - Request parameter validation

## 📊 Webhook Events Handled

- `checkout.session.completed` - Subscription activation
- `customer.subscription.created` - New subscription
- `customer.subscription.updated` - Plan changes
- `customer.subscription.deleted` - Cancellations
- `invoice.payment_succeeded` - Successful payments
- `invoice.payment_failed` - Failed payments
- `customer.created` - New customer
- `customer.updated` - Customer updates

## 🧪 Testing

### Manual Testing

1. **Health Check**
```bash
curl http://localhost:3001/health
```

2. **Create Customer**
```bash
curl -X POST http://localhost:3001/api/stripe/create-customer \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"Test User"}'
```

3. **Start Trial**
```bash
curl -X POST http://localhost:3001/api/stripe/start-trial \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"Test User"}'
```

### Stripe CLI Testing

1. **Install Stripe CLI**
```bash
# macOS
brew install stripe/stripe-cli/stripe

# Login to Stripe
stripe login
```

2. **Forward Webhooks**
```bash
stripe listen --forward-to localhost:3001/api/webhooks/stripe
```

3. **Trigger Test Events**
```bash
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
```

## 🚀 Production Deployment

### Environment Setup
```env
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-domain.com
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Deployment Checklist
- [ ] Set production Stripe keys
- [ ] Configure webhook endpoint URL
- [ ] Set up SSL/TLS certificates
- [ ] Configure production database
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy

## 📈 Monitoring

### Health Checks
- API health endpoint: `/health`
- Stripe webhook status in dashboard
- Error logging and monitoring

### Key Metrics
- API response times
- Webhook processing success rate
- Subscription conversion rates
- Payment failure rates

## 🔄 Development Workflow

1. **Start Backend**
```bash
npm run dev
```

2. **Start Frontend** (in separate terminal)
```bash
cd ../
npm run dev
```

3. **Test Integration**
- Visit http://localhost:5173
- Test subscription flows
- Verify webhook processing

## 📚 Additional Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [Express.js Documentation](https://expressjs.com/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
